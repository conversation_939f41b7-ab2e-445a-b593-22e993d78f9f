/**
 * Email Service for Notifications
 * Supports multiple email providers with template system
 */

import nodemailer from 'nodemailer';
import { User, Order } from '../types';

// Email configuration
const EMAIL_CONFIG = {
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || '',
    },
  },
  from: {
    name: process.env.EMAIL_FROM_NAME || 'EcommercePro',
    address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
  },
};

// Email templates
const EMAIL_TEMPLATES = {
  welcome: {
    subject: {
      en: 'Welcome to EcommercePro!',
      ar: 'مرحباً بك في إيكوميرس برو!',
    },
    html: {
      en: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Welcome to EcommercePro!</h1>
          <p>Dear {{firstName}},</p>
          <p>Thank you for joining EcommercePro. We're excited to have you as part of our community!</p>
          <p>You can now:</p>
          <ul>
            <li>Browse our extensive product catalog</li>
            <li>Place orders with secure payment</li>
            <li>Track your orders in real-time</li>
            <li>Manage your account and preferences</li>
          </ul>
          <p>If you have any questions, feel free to contact our support team.</p>
          <p>Best regards,<br>The EcommercePro Team</p>
        </div>
      `,
      ar: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
          <h1 style="color: #333;">مرحباً بك في إيكوميرس برو!</h1>
          <p>عزيزي {{firstName}}،</p>
          <p>شكراً لانضمامك إلى إيكوميرس برو. نحن متحمسون لوجودك كجزء من مجتمعنا!</p>
          <p>يمكنك الآن:</p>
          <ul>
            <li>تصفح كتالوج منتجاتنا الواسع</li>
            <li>تقديم الطلبات بدفع آمن</li>
            <li>تتبع طلباتك في الوقت الفعلي</li>
            <li>إدارة حسابك وتفضيلاتك</li>
          </ul>
          <p>إذا كان لديك أي أسئلة، لا تتردد في الاتصال بفريق الدعم لدينا.</p>
          <p>أطيب التحيات،<br>فريق إيكوميرس برو</p>
        </div>
      `,
    },
  },
  orderConfirmation: {
    subject: {
      en: 'Order Confirmation - {{orderNumber}}',
      ar: 'تأكيد الطلب - {{orderNumber}}',
    },
    html: {
      en: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Order Confirmation</h1>
          <p>Dear {{firstName}},</p>
          <p>Thank you for your order! We've received your order and it's being processed.</p>
          
          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>Order Details</h3>
            <p><strong>Order Number:</strong> {{orderNumber}}</p>
            <p><strong>Order Date:</strong> {{orderDate}}</p>
            <p><strong>Total Amount:</strong> {{total}} {{currency}}</p>
          </div>

          <div style="margin: 20px 0;">
            <h3>Items Ordered</h3>
            {{#each items}}
            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
              <p><strong>{{productName}}</strong></p>
              <p>Quantity: {{quantity}} × {{unitPrice}} {{currency}} = {{totalPrice}} {{currency}}</p>
            </div>
            {{/each}}
          </div>

          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>Shipping Address</h3>
            <p>{{shippingAddress.firstName}} {{shippingAddress.lastName}}</p>
            <p>{{shippingAddress.street}}</p>
            <p>{{shippingAddress.city}}, {{shippingAddress.state}} {{shippingAddress.postalCode}}</p>
            <p>{{shippingAddress.country}}</p>
          </div>

          <p>We'll send you another email when your order ships.</p>
          <p>Best regards,<br>The EcommercePro Team</p>
        </div>
      `,
      ar: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
          <h1 style="color: #333;">تأكيد الطلب</h1>
          <p>عزيزي {{firstName}}،</p>
          <p>شكراً لطلبك! لقد استلمنا طلبك وهو قيد المعالجة.</p>
          
          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>تفاصيل الطلب</h3>
            <p><strong>رقم الطلب:</strong> {{orderNumber}}</p>
            <p><strong>تاريخ الطلب:</strong> {{orderDate}}</p>
            <p><strong>المبلغ الإجمالي:</strong> {{total}} {{currency}}</p>
          </div>

          <div style="margin: 20px 0;">
            <h3>المنتجات المطلوبة</h3>
            {{#each items}}
            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
              <p><strong>{{productName}}</strong></p>
              <p>الكمية: {{quantity}} × {{unitPrice}} {{currency}} = {{totalPrice}} {{currency}}</p>
            </div>
            {{/each}}
          </div>

          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>عنوان الشحن</h3>
            <p>{{shippingAddress.firstName}} {{shippingAddress.lastName}}</p>
            <p>{{shippingAddress.street}}</p>
            <p>{{shippingAddress.city}}, {{shippingAddress.state}} {{shippingAddress.postalCode}}</p>
            <p>{{shippingAddress.country}}</p>
          </div>

          <p>سنرسل لك بريداً إلكترونياً آخر عند شحن طلبك.</p>
          <p>أطيب التحيات،<br>فريق إيكوميرس برو</p>
        </div>
      `,
    },
  },
  orderShipped: {
    subject: {
      en: 'Your Order Has Shipped - {{orderNumber}}',
      ar: 'تم شحن طلبك - {{orderNumber}}',
    },
    html: {
      en: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Your Order Has Shipped!</h1>
          <p>Dear {{firstName}},</p>
          <p>Great news! Your order {{orderNumber}} has been shipped and is on its way to you.</p>
          
          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>Tracking Information</h3>
            <p><strong>Tracking Number:</strong> {{trackingNumber}}</p>
            <p><strong>Estimated Delivery:</strong> {{estimatedDelivery}}</p>
          </div>

          <p>You can track your package using the tracking number above.</p>
          <p>Best regards,<br>The EcommercePro Team</p>
        </div>
      `,
      ar: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
          <h1 style="color: #333;">تم شحن طلبك!</h1>
          <p>عزيزي {{firstName}}،</p>
          <p>أخبار رائعة! تم شحن طلبك {{orderNumber}} وهو في طريقه إليك.</p>
          
          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>معلومات التتبع</h3>
            <p><strong>رقم التتبع:</strong> {{trackingNumber}}</p>
            <p><strong>التسليم المتوقع:</strong> {{estimatedDelivery}}</p>
          </div>

          <p>يمكنك تتبع طردك باستخدام رقم التتبع أعلاه.</p>
          <p>أطيب التحيات،<br>فريق إيكوميرس برو</p>
        </div>
      `,
    },
  },
  passwordReset: {
    subject: {
      en: 'Password Reset Request',
      ar: 'طلب إعادة تعيين كلمة المرور',
    },
    html: {
      en: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Password Reset Request</h1>
          <p>Dear {{firstName}},</p>
          <p>We received a request to reset your password. Click the button below to reset it:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetLink}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
          </div>

          <p>This link will expire in 1 hour for security reasons.</p>
          <p>If you didn't request this password reset, please ignore this email.</p>
          <p>Best regards,<br>The EcommercePro Team</p>
        </div>
      `,
      ar: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
          <h1 style="color: #333;">طلب إعادة تعيين كلمة المرور</h1>
          <p>عزيزي {{firstName}}،</p>
          <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بك. انقر على الزر أدناه لإعادة تعيينها:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetLink}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">إعادة تعيين كلمة المرور</a>
          </div>

          <p>ستنتهي صلاحية هذا الرابط خلال ساعة واحدة لأسباب أمنية.</p>
          <p>إذا لم تطلب إعادة تعيين كلمة المرور هذه، يرجى تجاهل هذا البريد الإلكتروني.</p>
          <p>أطيب التحيات،<br>فريق إيكوميرس برو</p>
        </div>
      `,
    },
  },
};

/**
 * Email Service Class
 */
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter(EMAIL_CONFIG.smtp);
  }

  /**
   * Render email template with data
   */
  private renderTemplate(template: string, data: any): string {
    let rendered = template;
    
    // Simple template rendering (replace {{variable}} with data)
    Object.keys(data).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      rendered = rendered.replace(regex, data[key] || '');
    });

    // Handle arrays (like order items)
    if (data.items && Array.isArray(data.items)) {
      const itemsRegex = /{{#each items}}([\s\S]*?){{\/each}}/g;
      rendered = rendered.replace(itemsRegex, (match, itemTemplate) => {
        return data.items.map((item: any) => {
          let itemHtml = itemTemplate;
          Object.keys(item).forEach(itemKey => {
            const itemRegex = new RegExp(`{{${itemKey}}}`, 'g');
            itemHtml = itemHtml.replace(itemRegex, item[itemKey] || '');
          });
          return itemHtml;
        }).join('');
      });
    }

    return rendered;
  }

  /**
   * Send email
   */
  async sendEmail(
    to: string,
    subject: string,
    html: string,
    text?: string
  ): Promise<boolean> {
    try {
      const mailOptions = {
        from: `${EMAIL_CONFIG.from.name} <${EMAIL_CONFIG.from.address}>`,
        to,
        subject,
        html,
        text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(user: User): Promise<boolean> {
    const language = user.language || 'en';
    const template = EMAIL_TEMPLATES.welcome;
    
    const subject = template.subject[language as keyof typeof template.subject];
    const html = this.renderTemplate(template.html[language as keyof typeof template.html], {
      firstName: user.firstName,
      lastName: user.lastName,
    });

    return this.sendEmail(user.email, subject, html);
  }

  /**
   * Send order confirmation email
   */
  async sendOrderConfirmationEmail(user: User, order: any): Promise<boolean> {
    const language = user.language || 'en';
    const template = EMAIL_TEMPLATES.orderConfirmation;
    
    const subject = this.renderTemplate(
      template.subject[language as keyof typeof template.subject],
      { orderNumber: order.orderNumber }
    );
    
    const html = this.renderTemplate(template.html[language as keyof typeof template.html], {
      firstName: user.firstName,
      orderNumber: order.orderNumber,
      orderDate: new Date(order.createdAt).toLocaleDateString(),
      total: order.total.toFixed(2),
      currency: order.currency,
      items: order.items,
      shippingAddress: order.shippingAddress,
    });

    return this.sendEmail(user.email, subject, html);
  }

  /**
   * Send order shipped email
   */
  async sendOrderShippedEmail(
    user: User, 
    order: any, 
    trackingNumber: string
  ): Promise<boolean> {
    const language = user.language || 'en';
    const template = EMAIL_TEMPLATES.orderShipped;
    
    const subject = this.renderTemplate(
      template.subject[language as keyof typeof template.subject],
      { orderNumber: order.orderNumber }
    );
    
    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 3); // 3 days from now
    
    const html = this.renderTemplate(template.html[language as keyof typeof template.html], {
      firstName: user.firstName,
      orderNumber: order.orderNumber,
      trackingNumber,
      estimatedDelivery: estimatedDelivery.toLocaleDateString(),
    });

    return this.sendEmail(user.email, subject, html);
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(
    user: User, 
    resetToken: string
  ): Promise<boolean> {
    const language = user.language || 'en';
    const template = EMAIL_TEMPLATES.passwordReset;
    
    const subject = template.subject[language as keyof typeof template.subject];
    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`;
    
    const html = this.renderTemplate(template.html[language as keyof typeof template.html], {
      firstName: user.firstName,
      resetLink,
    });

    return this.sendEmail(user.email, subject, html);
  }

  /**
   * Send custom email
   */
  async sendCustomEmail(
    to: string,
    subject: string,
    templateName: string,
    data: any
  ): Promise<boolean> {
    // This would be used for custom templates stored in database
    // For now, just send a simple email
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1>${subject}</h1>
        <pre>${JSON.stringify(data, null, 2)}</pre>
      </div>
    `;

    return this.sendEmail(to, subject, html);
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('Email service connection verified');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();
