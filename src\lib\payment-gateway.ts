/**
 * Payment Gateway Integration
 * Supports multiple payment providers with unified interface
 */

import Strip<PERSON> from 'stripe';
import { Order, PaymentMethod } from '../types';

// Payment configuration
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || '';
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID || '';
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET || '';

// Initialize Stripe
const stripe = new Stripe(STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethod?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  transactionId?: string;
  status?: string;
  error?: string;
  metadata?: any;
}

export interface RefundResult {
  success: boolean;
  refundId?: string;
  amount?: number;
  status?: string;
  error?: string;
}

/**
 * Abstract Payment Provider Interface
 */
abstract class PaymentProvider {
  abstract createPaymentIntent(amount: number, currency: string, metadata?: any): Promise<PaymentIntent>;
  abstract confirmPayment(paymentIntentId: string, paymentMethodId?: string): Promise<PaymentResult>;
  abstract refundPayment(paymentId: string, amount?: number): Promise<RefundResult>;
  abstract getPaymentStatus(paymentId: string): Promise<string>;
}

/**
 * Stripe Payment Provider
 */
class StripeProvider extends PaymentProvider {
  async createPaymentIntent(amount: number, currency: string = 'sar', metadata?: any): Promise<PaymentIntent> {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        automatic_payment_methods: {
          enabled: true,
        },
        metadata: metadata || {},
      });

      return {
        id: paymentIntent.id,
        clientSecret: paymentIntent.client_secret!,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
      };
    } catch (error) {
      console.error('Stripe payment intent creation failed:', error);
      throw new Error('Failed to create payment intent');
    }
  }

  async confirmPayment(paymentIntentId: string, paymentMethodId?: string): Promise<PaymentResult> {
    try {
      const paymentIntent = await stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: paymentMethodId,
      });

      return {
        success: paymentIntent.status === 'succeeded',
        paymentId: paymentIntent.id,
        transactionId: paymentIntent.charges.data[0]?.id,
        status: paymentIntent.status,
        metadata: paymentIntent.metadata,
      };
    } catch (error) {
      console.error('Stripe payment confirmation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment confirmation failed',
      };
    }
  }

  async refundPayment(paymentId: string, amount?: number): Promise<RefundResult> {
    try {
      const refund = await stripe.refunds.create({
        payment_intent: paymentId,
        amount: amount ? Math.round(amount * 100) : undefined,
      });

      return {
        success: refund.status === 'succeeded',
        refundId: refund.id,
        amount: refund.amount / 100,
        status: refund.status,
      };
    } catch (error) {
      console.error('Stripe refund failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Refund failed',
      };
    }
  }

  async getPaymentStatus(paymentId: string): Promise<string> {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentId);
      return paymentIntent.status;
    } catch (error) {
      console.error('Failed to get Stripe payment status:', error);
      return 'unknown';
    }
  }
}

/**
 * PayPal Payment Provider
 */
class PayPalProvider extends PaymentProvider {
  private async getAccessToken(): Promise<string> {
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');
    
    const response = await fetch('https://api-m.sandbox.paypal.com/v1/oauth2/token', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=client_credentials',
    });

    const data = await response.json();
    return data.access_token;
  }

  async createPaymentIntent(amount: number, currency: string = 'USD', metadata?: any): Promise<PaymentIntent> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await fetch('https://api-m.sandbox.paypal.com/v2/checkout/orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          intent: 'CAPTURE',
          purchase_units: [{
            amount: {
              currency_code: currency.toUpperCase(),
              value: amount.toFixed(2),
            },
          }],
          application_context: {
            return_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
            cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`,
          },
        }),
      });

      const order = await response.json();
      
      return {
        id: order.id,
        clientSecret: order.id, // PayPal uses order ID as client secret
        amount,
        currency,
        status: order.status,
      };
    } catch (error) {
      console.error('PayPal order creation failed:', error);
      throw new Error('Failed to create PayPal order');
    }
  }

  async confirmPayment(paymentIntentId: string): Promise<PaymentResult> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await fetch(`https://api-m.sandbox.paypal.com/v2/checkout/orders/${paymentIntentId}/capture`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      
      return {
        success: result.status === 'COMPLETED',
        paymentId: result.id,
        transactionId: result.purchase_units[0]?.payments?.captures[0]?.id,
        status: result.status,
        metadata: result,
      };
    } catch (error) {
      console.error('PayPal payment capture failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment capture failed',
      };
    }
  }

  async refundPayment(paymentId: string, amount?: number): Promise<RefundResult> {
    try {
      const accessToken = await this.getAccessToken();
      
      // First, get the capture ID from the order
      const orderResponse = await fetch(`https://api-m.sandbox.paypal.com/v2/checkout/orders/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      
      const order = await orderResponse.json();
      const captureId = order.purchase_units[0]?.payments?.captures[0]?.id;
      
      if (!captureId) {
        throw new Error('No capture found for this payment');
      }
      
      const refundResponse = await fetch(`https://api-m.sandbox.paypal.com/v2/payments/captures/${captureId}/refund`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount ? {
            currency_code: 'USD',
            value: amount.toFixed(2),
          } : undefined,
        }),
      });

      const refund = await refundResponse.json();
      
      return {
        success: refund.status === 'COMPLETED',
        refundId: refund.id,
        amount: parseFloat(refund.amount?.value || '0'),
        status: refund.status,
      };
    } catch (error) {
      console.error('PayPal refund failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Refund failed',
      };
    }
  }

  async getPaymentStatus(paymentId: string): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await fetch(`https://api-m.sandbox.paypal.com/v2/checkout/orders/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const order = await response.json();
      return order.status || 'unknown';
    } catch (error) {
      console.error('Failed to get PayPal payment status:', error);
      return 'unknown';
    }
  }
}

/**
 * Cash on Delivery Provider (Mock)
 */
class CashOnDeliveryProvider extends PaymentProvider {
  async createPaymentIntent(amount: number, currency: string, metadata?: any): Promise<PaymentIntent> {
    const id = `cod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      id,
      clientSecret: id,
      amount,
      currency,
      status: 'pending',
    };
  }

  async confirmPayment(paymentIntentId: string): Promise<PaymentResult> {
    return {
      success: true,
      paymentId: paymentIntentId,
      transactionId: `cod_txn_${Date.now()}`,
      status: 'pending_delivery',
    };
  }

  async refundPayment(paymentId: string, amount?: number): Promise<RefundResult> {
    return {
      success: true,
      refundId: `cod_refund_${Date.now()}`,
      amount: amount || 0,
      status: 'completed',
    };
  }

  async getPaymentStatus(paymentId: string): Promise<string> {
    return 'pending_delivery';
  }
}

/**
 * Payment Gateway Manager
 */
export class PaymentGateway {
  private providers: Map<string, PaymentProvider> = new Map();

  constructor() {
    this.providers.set('stripe', new StripeProvider());
    this.providers.set('paypal', new PayPalProvider());
    this.providers.set('cash_on_delivery', new CashOnDeliveryProvider());
  }

  getProvider(method: string): PaymentProvider {
    const provider = this.providers.get(method);
    if (!provider) {
      throw new Error(`Unsupported payment method: ${method}`);
    }
    return provider;
  }

  async createPaymentIntent(
    method: string,
    amount: number,
    currency: string = 'SAR',
    metadata?: any
  ): Promise<PaymentIntent> {
    const provider = this.getProvider(method);
    return provider.createPaymentIntent(amount, currency, metadata);
  }

  async confirmPayment(method: string, paymentIntentId: string, paymentMethodId?: string): Promise<PaymentResult> {
    const provider = this.getProvider(method);
    return provider.confirmPayment(paymentIntentId, paymentMethodId);
  }

  async refundPayment(method: string, paymentId: string, amount?: number): Promise<RefundResult> {
    const provider = this.getProvider(method);
    return provider.refundPayment(paymentId, amount);
  }

  async getPaymentStatus(method: string, paymentId: string): Promise<string> {
    const provider = this.getProvider(method);
    return provider.getPaymentStatus(paymentId);
  }

  getSupportedMethods(): string[] {
    return Array.from(this.providers.keys());
  }
}

// Export singleton instance
export const paymentGateway = new PaymentGateway();
