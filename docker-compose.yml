# Docker Compose configuration for EcommercePro
# Includes application, database, cache, and monitoring services

version: '3.8'

services:
  # Main application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: ecommercepro-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-your-super-secret-encryption-key-32-chars}
      - SQLITE_DB_PATH=/app/data/database.sqlite
      - REDIS_URL=redis://redis:6379
      - SMTP_HOST=${SMTP_HOST:-smtp.gmail.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - EMAIL_FROM_ADDRESS=${EMAIL_FROM_ADDRESS}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - PAYPAL_CLIENT_ID=${PAYPAL_CLIENT_ID}
      - PAYPAL_CLIENT_SECRET=${PAYPAL_CLIENT_SECRET}
    volumes:
      - app_data:/app/data
      - app_uploads:/app/uploads
    depends_on:
      - redis
    networks:
      - ecommercepro-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: ecommercepro-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-ecommercepro123}
    volumes:
      - redis_data:/data
    networks:
      - ecommercepro-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: ecommercepro-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - app_uploads:/var/www/uploads:ro
    depends_on:
      - app
    networks:
      - ecommercepro-network

  # Database backup service
  db-backup:
    image: alpine:latest
    container_name: ecommercepro-backup
    restart: unless-stopped
    volumes:
      - app_data:/data:ro
      - backup_data:/backup
      - ./scripts/backup.sh:/backup.sh:ro
    command: sh -c "chmod +x /backup.sh && crond -f"
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
    networks:
      - ecommercepro-network

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: ecommercepro-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ecommercepro-network
    profiles:
      - monitoring

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: ecommercepro-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ecommercepro-network
    profiles:
      - monitoring

  # Log aggregation with Loki (optional)
  loki:
    image: grafana/loki:latest
    container_name: ecommercepro-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - ecommercepro-network
    profiles:
      - monitoring

  # Log shipping with Promtail (optional)
  promtail:
    image: grafana/promtail:latest
    container_name: ecommercepro-promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - ecommercepro-network
    profiles:
      - monitoring

# Named volumes for data persistence
volumes:
  app_data:
    driver: local
  app_uploads:
    driver: local
  redis_data:
    driver: local
  backup_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local

# Custom network
networks:
  ecommercepro-network:
    driver: bridge
