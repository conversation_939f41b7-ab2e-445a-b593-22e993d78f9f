'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ShoppingCart, Heart, Star, ArrowRight, X, Search,
  Grid, List, SlidersHorizontal, Tag, Filter, ChevronDown,
  ArrowUpDown, ArrowDownUp, CheckCircle, Eye, Truck, Package,
  Sparkles, Flame, TrendingUp, ShieldCheck, Clock, Zap,
  Info, RefreshCw
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { LazyImage } from '../ui/LazyImage';
import { EnhancedImage } from '../ui/EnhancedImage';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { AuthModal } from '../auth/AuthModal';
import { WholesaleQuoteForm } from '../forms/WholesaleQuoteForm';
import { products, productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';
import { Product, ProductFiltersState, SortOption } from '../../types/index'; // CRITICAL: Ensure this is the ONLY Product import
import { useAuthenticatedAction } from '../../hooks/useAuthenticatedAction';
import { Badge } from '../ui/Badge';
import { Tooltip } from '../ui/Tooltip';
import { EnhancedProductFilters } from './EnhancedProductFilters';
import { ShopHeader } from './ShopHeader';
import { ShopFooter } from './ShopFooter';
import { FeaturedProduct } from './FeaturedProduct';
import { EnhancedProductCard } from '../product/EnhancedProductCard';
import { QuickView } from './QuickView';

interface ShopPageEnhancedProps {
  initialFilters?: {
    featured?: boolean;
    category?: string;
    searchQuery?: string;
  };
}

export const ShopPageEnhanced = ({ initialFilters }: ShopPageEnhancedProps) => {
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [sortOption, setSortOption] = useState<SortOption>('featured');
  const [isLoading, setIsLoading] = useState(true);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');

  const maxPrice = useMemo(() => products.reduce((max, p) => p.price > max ? p.price : max, 0), [products]);

  const [filters, setFilters] = useState<ProductFiltersState>({
    category: initialFilters?.category || 'all',
    priceRange: { min: 0, max: maxPrice || 50000 },
    inStock: false,
    onSale: false,
    featured: initialFilters?.featured || false,
    searchQuery: initialFilters?.searchQuery || ''
  });

  // تحديث الفلاتر عند تغير السعر الأقصى
  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceRange: {
        ...prevFilters.priceRange,
        max: maxPrice || 50000
      }
    }));
  }, [maxPrice]);

  // محاكاة تحميل البيانات
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 600); // تقليل وقت التحميل لتحسين تجربة المستخدم
    return () => clearTimeout(timer);
  }, []);

  // إغلاق قائمة الترتيب عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = () => {
      if (showSortDropdown) {
        setShowSortDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showSortDropdown]);

  // حساب عدد الفلاتر النشطة
  useEffect(() => {
    let count = 0;
    if (filters.category !== 'all') count++;
    if (filters.inStock) count++;
    if (filters.onSale) count++;
    if (filters.featured) count++;
    if (filters.searchQuery) count++;
    if (filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) count++;
    setActiveFiltersCount(count);
  }, [filters, maxPrice]);

  // إظهار رسالة نجاح
  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setShowSuccessToast(true);

    setTimeout(() => {
      setShowSuccessToast(false);
    }, 3000);
  };

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { theme, resolvedTheme } = useTheme();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث URL مع الفلاتر النشطة
  const updateUrlWithFilters = useCallback(() => {
    const params = new URLSearchParams();
    if (filters.featured) params.set('featured', 'true');
    if (filters.category !== 'all') params.set('category', filters.category);
    if (filters.searchQuery) params.set('q', filters.searchQuery);
    if (filters.onSale) params.set('sale', 'true');
    if (filters.inStock) params.set('instock', 'true');
    if (filters.priceRange.min > 0) params.set('min', filters.priceRange.min.toString());
    if (filters.priceRange.max < maxPrice) params.set('max', filters.priceRange.max.toString());

    const url = `/${currentLanguage}/shop${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(url, { scroll: false });
  }, [router, filters, maxPrice, currentLanguage]);

  // تحديث عدد الفلاتر النشطة وتحديث URL
  useEffect(() => {
    let count = 0;
    if (filters.category !== 'all') count++;
    if (filters.inStock) count++;
    if (filters.onSale) count++;
    if (filters.featured) count++;
    if (filters.searchQuery) count++;
    if (filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) count++;
    setActiveFiltersCount(count);
    updateUrlWithFilters();
  }, [filters, updateUrlWithFilters]);

  // تصفية المنتجات حسب الفلاتر
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      // تصفية حسب الفئة
      if (filters.category !== 'all' && product.category !== filters.category) return false;

      // تصفية حسب المخزون
      if (filters.inStock && product.stock <= 0) return false;

      // تصفية حسب العروض
      if (filters.onSale && (!product.compareAtPrice || product.compareAtPrice <= product.price)) return false;

      // تصفية حسب المنتجات المميزة
      if (filters.featured && !product.featured) return false;

      // تصفية حسب نطاق السعر
      if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) return false;

      // تصفية حسب البحث
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        const nameMatch = product.name.toLowerCase().includes(query);
        const nameArMatch = product.name_ar ? product.name_ar.toLowerCase().includes(query) : false;
        const descMatch = product.description.toLowerCase().includes(query);
        const descArMatch = product.description_ar ? product.description_ar.toLowerCase().includes(query) : false;
        const categoryMatch = product.category.toLowerCase().includes(query);
        const tagsMatch = product.tags.some(tag => tag.toLowerCase().includes(query));

        return nameMatch || nameArMatch || descMatch || descArMatch || categoryMatch || tagsMatch;
      }

      return true;
    });
  }, [filters]);

  // ترتيب المنتجات حسب الخيار المحدد
  const sortedProducts = useMemo(() => {
    let sorted = [...filteredProducts];

    switch (sortOption) {
      case 'featured':
        // ترتيب المنتجات المميزة أولاً، ثم حسب التقييم
        return sorted.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return (b.rating ?? 0) - (a.rating ?? 0);
        });

      case 'newest':
        // ترتيب حسب تاريخ الإضافة (الأحدث أولاً)
        return sorted.sort((a, b) => new Date(b.createdAt ?? 0).getTime() - new Date(a.createdAt ?? 0).getTime());

      case 'price-asc':
        // ترتيب حسب السعر (من الأقل إلى الأعلى)
        return sorted.sort((a, b) => a.price - b.price);

      case 'price-desc':
        // ترتيب حسب السعر (من الأعلى إلى الأقل)
        return sorted.sort((a, b) => b.price - a.price);

      case 'popular':
        // ترتيب حسب التقييم والمراجعات
        return sorted.sort((a, b) => {
          const aRating = a.rating ?? 0;
          const bRating = b.rating ?? 0;
          const aReviews = a.reviewCount ?? 0;
          const bReviews = b.reviewCount ?? 0;

          // ترتيب حسب التقييم أولاً، ثم حسب عدد المراجعات
          if (aRating !== bRating) return bRating - aRating;
          return bReviews - aReviews;
        });

      case 'discount':
        // ترتيب حسب نسبة الخصم (الأعلى أولاً)
        return sorted.sort((a, b) => {
          const discountA = (a.compareAtPrice != null && a.price != null && a.compareAtPrice > a.price) ? ((a.compareAtPrice - a.price) / a.compareAtPrice) : 0;
          const discountB = (b.compareAtPrice != null && b.price != null && b.compareAtPrice > b.price) ? ((b.compareAtPrice - b.price) / b.compareAtPrice) : 0;
          return discountB - discountA;
        });

      default:
        return sorted;
    }
  }, [filteredProducts, sortOption]);

  const handleUnauthenticated = () => {
    setShowAuthModal(true);
  };

  const handleAddToCart = useAuthenticatedAction((product: Product, quantity: number = 1) => {
    cartStore.addItem(product, quantity);
    // إظهار رسالة نجاح
    const message = currentLanguage === 'ar'
      ? `تمت إضافة ${product.name} إلى سلة التسوق`
      : `${product.name} added to cart`;
    showToast(message, 'success');
  }, handleUnauthenticated);

  const handleWholesaleInquiry = useAuthenticatedAction((product: Product) => {
    setSelectedProduct(product);
    setShowWholesaleForm(true);
  }, handleUnauthenticated);

  const handleToggleWishlist = useAuthenticatedAction((product: Product) => {
    if (wishlistStore.isInWishlist(product.id)) {
      wishlistStore.removeItem(product.id);
      const message = currentLanguage === 'ar'
        ? `تمت إزالة ${product.name} من المفضلة`
        : `${product.name} removed from wishlist`;
      showToast(message, 'info');
    } else {
      wishlistStore.addItem(product);
      const message = currentLanguage === 'ar'
        ? `تمت إضافة ${product.name} إلى المفضلة`
        : `${product.name} added to wishlist`;
      showToast(message, 'success');
    }
  }, handleUnauthenticated);

  const handleQuickView = (product: Product) => {
    setQuickViewProduct(product);
  };

  const resetFilters = () => {
    setFilters({
      category: 'all',
      priceRange: { min: 0, max: maxPrice || 50000 },
      inStock: false,
      onSale: false,
      featured: false,
      searchQuery: ''
    });
    setSortOption('featured');
    setShowMobileFilters(false);

    // إظهار رسالة إعادة تعيين الفلاتر
    const message = currentLanguage === 'ar'
      ? 'تم إعادة تعيين جميع الفلاتر'
      : 'All filters have been reset';
    showToast(message, 'info');
  };

  // تبديل وضع العرض (شبكة/قائمة)
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
  };

  // التحقق من وجود منتجات مميزة
  const hasFeaturedProducts = useMemo(() => {
    return products.some(product => product.featured);
  }, [products]);

  // الحصول على المنتجات المميزة
  const featuredProducts = useMemo(() => {
    return products.filter(product => product.featured).slice(0, 4);
  }, [products]);

  // الحصول على المنتجات الأكثر مبيعًا
  const bestSellingProducts = useMemo(() => {
    return [...products]
      .sort((a, b) => (b.rating ?? 0) - (a.rating ?? 0))
      .slice(0, 4);
  }, [products]);

  // الحصول على المنتجات الجديدة
  const newArrivals = useMemo(() => {
    return [...products]
      .sort((a, b) => new Date(b.createdAt ?? 0).getTime() - new Date(a.createdAt ?? 0).getTime())
      .slice(0, 4);
  }, [products]);

  return (
    <div className="container-custom py-8">
      {/* رأس المتجر مع البانر وفئات المنتجات */}
      <ShopHeader
        onSearch={(query) => setFilters(prev => ({ ...prev, searchQuery: query }))}
        onCategorySelect={(category) => setFilters(prev => ({ ...prev, category }))}
        searchQuery={filters.searchQuery}
        selectedCategory={filters.category}
      />

      {/* قسم المنتجات المميزة */}
      {hasFeaturedProducts && !filters.featured && (
        <ScrollAnimation animation="fade" delay={0.2} className="mb-8">
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-xl p-6 relative overflow-hidden">
            {/* عناصر زخرفية */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary-300/20 dark:bg-primary-500/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-accent-300/20 dark:bg-accent-500/10 rounded-full blur-3xl"></div>

            <div className="flex flex-col md:flex-row justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2 flex items-center">
                  <Sparkles className={`${isRTL ? 'ml-2' : 'mr-2'} text-amber-500`} size={20} />
                  {currentLanguage === 'ar' ? 'منتجاتنا المميزة' : 'Our Featured Products'}
                </h2>
                <p className="text-slate-600 dark:text-slate-300 text-sm">
                  {currentLanguage === 'ar'
                    ? 'اكتشف منتجاتنا المميزة المختارة خصيصًا لك'
                    : 'Discover our featured products specially curated for you'}
                </p>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="mt-4 md:mt-0 border-primary-200 dark:border-primary-800 hover:bg-primary-50 dark:hover:bg-primary-900/30"
                onClick={() => setFilters(prev => ({ ...prev, featured: true }))}
              >
                {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured'}
                <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
              </Button>
            </div>

            {/* شبكة المنتجات المميزة */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {featuredProducts.map((product, index) => (
                <HoverAnimation key={product.id} animation="lift">
                  <Card className="bg-white dark:bg-slate-800 h-full flex flex-col overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                    {/* صورة المنتج */}
                    <div className="relative aspect-square overflow-hidden">
                      <Link href={`/${currentLanguage}/shop/${product.slug}`}>
                        <EnhancedImage
                          src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                          alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                          fill={true}
                          objectFit="cover"
                          progressive={true}
                          placeholder="shimmer"
                          className="transition-transform duration-500 hover:scale-105"
                        />
                      </Link>

                      {/* شارة المنتج المميز */}
                      <div className="absolute top-2 left-2 z-10">
                        <Badge variant="warning" className="flex items-center">
                          <Sparkles className="w-3 h-3 mr-1" />
                          {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                        </Badge>
                      </div>

                      {/* شارة الخصم */}
                      {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                        <div className="absolute top-2 right-2 z-10">
                          <Badge variant="error">
                            {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                          </Badge>
                        </div>
                      )}

                      {/* أزرار الإجراءات */}
                      <div className="absolute bottom-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Button
                          variant="icon"
                          size="sm"
                          className="bg-white/90 backdrop-blur-sm text-slate-700 hover:bg-white dark:bg-slate-800/90 dark:text-white dark:hover:bg-slate-700 rounded-full shadow-sm"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleQuickView(product);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* معلومات المنتج */}
                    <div className="p-3 flex flex-col flex-grow">
                      <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                        <h3 className="font-medium text-slate-900 dark:text-white line-clamp-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                          {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                        </h3>
                      </Link>

                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-baseline gap-1">
                          <span className="font-bold text-primary-600 dark:text-primary-400">
                            {formatCurrency(product.price)}
                          </span>
                          {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                            <span className="text-xs text-slate-500 line-through">
                              {formatCurrency(product.compareAtPrice)}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center text-sm text-yellow-500">
                          <Star className="h-3.5 w-3.5 fill-current" />
                          <span className="ml-1 text-xs text-slate-600 dark:text-slate-300">
                            {product.rating?.toFixed(1) || '4.5'}
                          </span>
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="mt-2 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/30 w-full"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleAddToCart(product, 1);
                        }}
                      >
                        <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                      </Button>
                    </div>
                  </Card>
                </HoverAnimation>
              ))}
            </div>
          </div>
        </ScrollAnimation>
      )}

      {/* محتوى المتجر الرئيسي */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* الشريط الجانبي */}
        <div className="lg:col-span-1">
          {/* فلاتر المنتجات */}
          <div className="sticky top-24">
            <EnhancedProductFilters
              filters={filters}
              setFilters={setFilters}
              resetFilters={resetFilters}
              maxPrice={maxPrice}
              productCategories={productCategories}
              showMobileFilters={showMobileFilters}
              setShowMobileFilters={setShowMobileFilters}
              activeFiltersCount={activeFiltersCount}
              tags={Array.from(new Set(products.flatMap(p => p.tags)))}
            />
          </div>

          {/* منتج مميز في الشريط الجانبي */}
          {hasFeaturedProducts && featuredProducts.length > 0 && (
            <div className="mt-8 hidden lg:block">
              <FeaturedProduct
                product={featuredProducts[0]}
                onAddToCart={(product, quantity) => handleAddToCart(product, quantity)}
                onToggleWishlist={() => handleToggleWishlist(featuredProducts[0])}
              />
            </div>
          )}

          {/* المنتجات الأكثر مبيعًا */}
          <div className="mt-8 hidden lg:block">
            <Card className="overflow-hidden border border-primary-100 dark:border-primary-800">
              {/* عنوان القسم */}
              <div className="bg-primary-50 dark:bg-primary-900/30 p-3 border-b border-primary-100 dark:border-primary-800">
                <h3 className="font-semibold text-primary-800 dark:text-primary-300 flex items-center">
                  <Flame className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} text-amber-500`} />
                  {currentLanguage === 'ar' ? 'الأكثر مبيعًا' : 'Best Sellers'}
                </h3>
              </div>

              {/* قائمة المنتجات */}
              <div className="divide-y divide-slate-200 dark:divide-slate-700">
                {bestSellingProducts.slice(0, 3).map((product) => (
                  <Link
                    key={product.id}
                    href={`/${currentLanguage}/shop/${product.slug}`}
                    className="flex p-3 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors"
                  >
                    {/* صورة المنتج */}
                    <div className="relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                      <EnhancedImage
                        src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                        alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                        fill={true}
                        objectFit="cover"
                      />
                    </div>

                    {/* معلومات المنتج */}
                    <div className="ml-3 flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-slate-900 dark:text-white truncate">
                        {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                      </h4>

                      <div className="flex items-center mt-1">
                        <div className="flex text-yellow-500">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={cn(
                                "h-3 w-3",
                                i < Math.floor(product.rating ?? 0) ? "fill-current" : "text-slate-300 dark:text-slate-600"
                              )}
                            />
                          ))}
                        </div>
                        <span className="ml-1 text-xs text-slate-500 dark:text-slate-400">
                          ({product.reviewCount ?? 0})
                        </span>
                      </div>

                      <div className="flex items-baseline mt-1">
                        <span className="text-sm font-bold text-primary-600 dark:text-primary-400">
                          {formatCurrency(product.price)}
                        </span>
                        {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                          <span className="ml-1 text-xs text-slate-500 line-through">
                            {formatCurrency(product.compareAtPrice)}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>

              {/* رابط عرض المزيد */}
              <div className="p-3 bg-primary-50/50 dark:bg-primary-900/20 border-t border-primary-100 dark:border-primary-800">
                <Link
                  href={`/${currentLanguage}/shop?sort=popular`}
                  className="text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center justify-center"
                  onClick={(e) => {
                    e.preventDefault();
                    setSortOption('popular');
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                >
                  {currentLanguage === 'ar' ? 'عرض المزيد من المنتجات الرائجة' : 'View More Popular Products'}
                  <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
                </Link>
              </div>
            </Card>
          </div>
        </div>

        {/* شبكة المنتجات */}
        <div className="lg:col-span-3">
          {/* أدوات الترتيب وتبديل طريقة العرض */}
          <div className="flex flex-wrap justify-between items-center mb-6 bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm">
            <div className="flex items-center mb-2 sm:mb-0">
              <span className="text-sm text-slate-600 dark:text-slate-400 mr-2">
                {currentLanguage === 'ar'
                  ? `${sortedProducts.length} منتج`
                  : `${sortedProducts.length} products`}
              </span>

              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowSortDropdown(!showSortDropdown);
                  }}
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort by'}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>

                {showSortDropdown && (
                  <div className="absolute z-10 mt-1 w-48 bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700">
                    <div className="py-1">
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'featured' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('featured');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'المميزة' : 'Featured'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'newest' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('newest');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'الأحدث' : 'Newest'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-asc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('price-asc');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'السعر: من الأقل إلى الأعلى' : 'Price: Low to High'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-desc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('price-desc');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'السعر: من الأعلى إلى الأقل' : 'Price: High to Low'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'popular' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('popular');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'discount' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('discount');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'أعلى خصم' : 'Biggest Discount'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center">
              <Tooltip content={currentLanguage === 'ar' ? 'تبديل طريقة العرض' : 'Toggle view mode'}>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleViewMode}
                  className="mr-2"
                  aria-label={currentLanguage === 'ar' ? 'تبديل طريقة العرض' : 'Toggle view mode'}
                >
                  {viewMode === 'grid' ? (
                    <List className="h-4 w-4" />
                  ) : (
                    <Grid className="h-4 w-4" />
                  )}
                </Button>
              </Tooltip>

              <Button
                variant={activeFiltersCount > 0 ? "primary" : "outline"}
                size="sm"
                onClick={() => setShowMobileFilters(!showMobileFilters)}
                className="lg:hidden"
              >
                <Filter className="h-4 w-4 mr-2" />
                {currentLanguage === 'ar' ? 'الفلاتر' : 'Filters'}
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2 bg-white text-primary-700">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </div>
          </div>

          {/* شبكة المنتجات */}
          {isLoading ? (
            // حالة التحميل
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-slate-800 rounded-lg overflow-hidden animate-pulse border border-slate-200 dark:border-slate-700 shadow-sm">
                  <div className="aspect-square bg-slate-200 dark:bg-slate-700 relative">
                    {/* محاكاة شارات المنتج */}
                    <div className="absolute top-2 left-2 h-5 w-16 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                    {/* محاكاة أزرار الإجراءات */}
                    <div className="absolute bottom-2 right-2 flex gap-1">
                      <div className="h-8 w-8 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                      <div className="h-8 w-8 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                    </div>
                  </div>
                  <div className="p-4 space-y-3">
                    {/* محاكاة الفئة والتقييم */}
                    <div className="flex justify-between">
                      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4"></div>
                      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4"></div>
                    </div>
                    {/* محاكاة اسم المنتج */}
                    <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-3/4"></div>
                    {/* محاكاة وصف المنتج */}
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-full"></div>
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-2/3"></div>
                    {/* محاكاة السعر والمخزون */}
                    <div className="flex justify-between pt-2">
                      <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-1/3"></div>
                      <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-1/5"></div>
                    </div>
                    {/* محاكاة زر الإضافة إلى السلة */}
                    <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-full mt-4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : sortedProducts.length === 0 ? (
            // لا توجد منتجات
            <div className="bg-white dark:bg-slate-800 rounded-lg p-8 text-center border border-slate-200 dark:border-slate-700 shadow-sm">
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="absolute -inset-4 rounded-full bg-slate-100 dark:bg-slate-700/50 animate-pulse"></div>
                  <Package className="h-16 w-16 text-slate-400 dark:text-slate-500 relative z-10" />
                </div>
              </div>
              <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-3">
                {currentLanguage === 'ar' ? 'لا توجد منتجات' : 'No Products Found'}
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                {currentLanguage === 'ar'
                  ? 'لم نتمكن من العثور على أي منتجات تطابق معايير البحث الخاصة بك. يرجى تجربة معايير بحث مختلفة أو إعادة تعيين الفلاتر.'
                  : 'We couldn\'t find any products that match your search criteria. Try different search terms or reset the filters.'}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button variant="primary" onClick={resetFilters} className="flex items-center justify-center">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFilters({
                      category: 'all',
                      priceRange: { min: 0, max: maxPrice || 50000 },
                      inStock: false,
                      onSale: false,
                      featured: false,
                      searchQuery: ''
                    });
                    setSortOption('featured');
                  }}
                  className="flex items-center justify-center"
                >
                  <Tag className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'تصفح جميع المنتجات' : 'Browse All Products'}
                </Button>
              </div>
            </div>
          ) : (
            // عرض المنتجات
            <div className="space-y-8">
              {/* عرض المنتجات الجديدة في الأعلى إذا كان الترتيب هو الافتراضي */}
              {sortOption === 'featured' && !filters.featured && !filters.onSale && !filters.searchQuery && (
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <TrendingUp className={`h-5 w-5 text-primary-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {currentLanguage === 'ar' ? 'وصل حديثًا' : 'New Arrivals'}
                      </h3>
                    </div>
                    <Button
                      variant="link"
                      size="sm"
                      className="text-primary-600 dark:text-primary-400"
                      onClick={() => setSortOption('newest')}
                    >
                      {currentLanguage === 'ar' ? 'عرض الكل' : 'View All'}
                      <ArrowRight className={`h-4 w-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {newArrivals.slice(0, 3).map((product, index) => (
                      <HoverAnimation key={product.id} animation="lift">
                        <Card className="bg-white dark:bg-slate-800 h-full flex flex-col overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                          {/* صورة المنتج */}
                          <div className="relative aspect-square overflow-hidden">
                            <Link href={`/${currentLanguage}/shop/${product.slug}`}>
                              <EnhancedImage
                                src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                                alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                                fill={true}
                                objectFit="cover"
                                progressive={true}
                                placeholder="shimmer"
                                className="transition-transform duration-500 hover:scale-105"
                              />
                            </Link>

                            {/* شارة المنتج المميز */}
                            <div className="absolute top-2 left-2 z-10">
                              <Badge variant="warning" className="flex items-center">
                                <Sparkles className="w-3 h-3 mr-1" />
                                {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                              </Badge>
                            </div>

                            {/* شارة الخصم */}
                            {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                              <div className="absolute top-2 right-2 z-10">
                                <Badge variant="destructive">
                                  {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                                </Badge>
                              </div>
                            )}

                            {/* أزرار الإجراءات */}
                            <div className="absolute bottom-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <Button
                                variant="outline"
                                size="icon"
                                className="bg-white/90 backdrop-blur-sm text-slate-700 hover:bg-white dark:bg-slate-800/90 dark:text-white dark:hover:bg-slate-700 rounded-full shadow-sm"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleQuickView(product);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* معلومات المنتج */}
                          <div className="p-3 flex flex-col flex-grow">
                            <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                              <h3 className="font-medium text-slate-900 dark:text-white line-clamp-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                              </h3>
                            </Link>

                            <div className="flex items-center justify-between mt-2">
                              <div className="flex items-baseline gap-1">
                                <span className="font-bold text-primary-600 dark:text-primary-400">
                                  {formatCurrency(product.price)}
                                </span>
                                {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                                  <span className="text-xs text-slate-500 line-through">
                                    {formatCurrency(product.compareAtPrice)}
                                  </span>
                                )}
                              </div>

                              <div className="flex items-center text-sm text-yellow-500">
                                <Star className="h-3.5 w-3.5 fill-current" />
                                <span className="ml-1 text-xs text-slate-600 dark:text-slate-300">
                                  {product.rating?.toFixed(1) || '4.5'}
                                </span>
                              </div>
                            </div>

                            <Button
                              variant="ghost"
                              size="sm"
                              className="mt-2 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/30 w-full"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleAddToCart(product, 1);
                              }}
                            >
                              <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                              {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                            </Button>
                          </div>
                        </Card>
                      </HoverAnimation>
                    ))}
                  </div>
                </div>
              )}

              {/* عرض المنتجات المخفضة في الأعلى إذا كان الترتيب هو الافتراضي */}
              {sortOption === 'featured' && !filters.onSale && !filters.featured && !filters.searchQuery && (
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Zap className={`h-5 w-5 text-red-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {currentLanguage === 'ar' ? 'عروض خاصة' : 'Special Offers'}
                      </h3>
                    </div>
                    <Button
                      variant="link"
                      size="sm"
                      className="text-primary-600 dark:text-primary-400"
                      onClick={() => setFilters(prev => ({ ...prev, onSale: true }))}
                    >
                      {currentLanguage === 'ar' ? 'عرض الكل' : 'View All'}
                      <ArrowRight className={`h-4 w-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {products
                      .filter(p => p.compareAtPrice != null && p.compareAtPrice > p.price)
                      .sort((a, b) => ((1 - a.price / a.compareAtPrice) - (1 - b.price / b.compareAtPrice)))
                      .slice(0, 3)
                      .map((product, index) => (
                        <HoverAnimation key={product.id} animation="lift">
                          <Card className="bg-white dark:bg-slate-800 h-full flex flex-col overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                            {/* صورة المنتج */}
                            <div className="relative aspect-square overflow-hidden">
                              <Link href={`/${currentLanguage}/shop/${product.slug}`}>
                                <EnhancedImage
                                  src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                                  alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                                  fill={true}
                                  objectFit="cover"
                                  progressive={true}
                                  placeholder="shimmer"
                                  className="transition-transform duration-500 hover:scale-105"
                                />
                              </Link>

                              {/* شارة المنتج المميز */}
                              <div className="absolute top-2 left-2 z-10">
                                <Badge variant="warning" className="flex items-center">
                                  <Sparkles className="w-3 h-3 mr-1" />
                                  {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                                </Badge>
                              </div>

                              {/* شارة الخصم */}
                              {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                                <div className="absolute top-2 right-2 z-10">
                                  <Badge variant="destructive">
                                    {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                                  </Badge>
                                </div>
                              )}

                              {/* أزرار الإجراءات */}
                              <div className="absolute bottom-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="bg-white/90 backdrop-blur-sm text-slate-700 hover:bg-white dark:bg-slate-800/90 dark:text-white dark:hover:bg-slate-700 rounded-full shadow-sm"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handleQuickView(product);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>

                            {/* معلومات المنتج */}
                            <div className="p-3 flex flex-col flex-grow">
                              <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                                <h3 className="font-medium text-slate-900 dark:text-white line-clamp-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                  {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                                </h3>
                              </Link>

                              <div className="flex items-center justify-between mt-2">
                                <div className="flex items-baseline gap-1">
                                  <span className="font-bold text-primary-600 dark:text-primary-400">
                                    {formatCurrency(product.price)}
                                  </span>
                                  {product.compareAtPrice != null && product.compareAtPrice > product.price && (
                                    <span className="text-xs text-slate-500 line-through">
                                      {formatCurrency(product.compareAtPrice)}
                                    </span>
                                  )}
                                </div>

                                <div className="flex items-center text-sm text-yellow-500">
                                  <Star className="h-3.5 w-3.5 fill-current" />
                                  <span className="ml-1 text-xs text-slate-600 dark:text-slate-300">
                                    {product.rating?.toFixed(1) || '4.5'}
                                  </span>
                                </div>
                              </div>

                              <Button
                                variant="ghost"
                                size="sm"
                                className="mt-2 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/30 w-full"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleAddToCart(product, 1);
                                }}
                              >
                                <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                              </Button>
                            </div>
                          </Card>
                        </HoverAnimation>
                      ))}
                    </div>
                  </div>
                )}

                {/* عرض جميع المنتجات */}
                <div>
                  {sortOption !== 'featured' || filters.featured || filters.onSale || filters.searchQuery ? (
                    <div className="flex items-center mb-4">
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {filters.searchQuery ? (
                          currentLanguage === 'ar' ? `نتائج البحث: "${filters.searchQuery}"` : `Search Results: "${filters.searchQuery}"`
                        ) : filters.featured ? (
                          currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'
                        ) : filters.onSale ? (
                          currentLanguage === 'ar' ? 'المنتجات المخفضة' : 'Sale Products'
                        ) : sortOption === 'newest' ? (
                          currentLanguage === 'ar' ? 'أحدث المنتجات' : 'Newest Products'
                        ) : sortOption === 'popular' ? (
                          currentLanguage === 'ar' ? 'المنتجات الأكثر شعبية' : 'Popular Products'
                        ) : sortOption === 'price-asc' ? (
                          currentLanguage === 'ar' ? 'المنتجات من الأقل إلى الأعلى سعرًا' : 'Products: Low to High Price'
                        ) : sortOption === 'price-desc' ? (
                          currentLanguage === 'ar' ? 'المنتجات من الأعلى إلى الأقل سعرًا' : 'Products: High to Low Price'
                        ) : (
                          currentLanguage === 'ar' ? 'جميع المنتجات' : 'All Products'
                        )}
                      </h3>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {currentLanguage === 'ar' ? 'جميع المنتجات' : 'All Products'}
                      </h3>
                    </div>
                  )}

                  <div className={cn(
                    viewMode === 'grid'
                      ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                      : "flex flex-col gap-6"
                  )}>
                    {sortedProducts.map((product, index) => (
                      <EnhancedProductCard
                        key={product.id + '-' + index} // Ensure key is unique if product.id can repeat due to data issues
                        product={product} // This 'product' must be of type Product from types/index
                        index={index}
                        viewMode={viewMode}
                        onQuickView={() => handleQuickView(product)} // Ensure product here is correctly typed
                        onAddToCart={(product, quantity) => handleAddToCart(product, quantity)} // Ensure prod here is correctly typed
                        onToggleWishlist={() => handleToggleWishlist(product)} // Ensure product here is correctly typed
                        onWholesaleInquiry={() => handleWholesaleInquiry(product)} // Ensure product here is correctly typed
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* ترقيم الصفحات وقسم النشرة الإخبارية */}
          <ShopFooter
            totalProducts={sortedProducts.length}
            currentPage={1}
            itemsPerPage={12}
            onPageChange={(page) => console.log(`Navigate to page ${page}`)}
          />
        </div>
      </div>

      {/* نافذة النظرة السريعة */}
      {quickViewProduct && (
        <QuickView
          product={quickViewProduct}
          onClose={() => setQuickViewProduct(null)}
          onAddToCart={(product, quantity) => handleAddToCart(product, quantity)}
          onToggleWishlist={() => handleToggleWishlist(quickViewProduct)}
        />
      )}

      {/* نافذة طلب عرض سعر للجملة */}
      {showWholesaleForm && selectedProduct && (
        <WholesaleQuoteForm
          product={selectedProduct}
          onClose={() => {
            setShowWholesaleForm(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {/* نافذة تسجيل الدخول */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode="sign-in"
          onSuccess={() => {
            setShowAuthModal(false);
          }}
        />
      )}

      {/* رسالة النجاح */}
      {showSuccessToast && (
        <div className={cn(
          "fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-xl max-w-md",
          "animate-bounce-in transition-all duration-300",
          "backdrop-blur-md border",
          toastType === 'success' ? "bg-green-500/90 text-white border-green-400" :
          toastType === 'error' ? "bg-red-500/90 text-white border-red-400" :
          "bg-blue-500/90 text-white border-blue-400"
        )}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full mr-3",
                toastType === 'success' ? "bg-green-600" :
                toastType === 'error' ? "bg-red-600" :
                "bg-blue-600"
              )}>
                {toastType === 'success' && <CheckCircle className="h-5 w-5" />}
                {toastType === 'error' && <X className="h-5 w-5" />}
                {toastType === 'info' && <Info className="h-5 w-5" />}
              </div>
              <div>
                <h4 className="font-medium mb-1">
                  {toastType === 'success' ? (currentLanguage === 'ar' ? 'تم بنجاح' : 'Success') :
                   toastType === 'error' ? (currentLanguage === 'ar' ? 'خطأ' : 'Error') :
                   (currentLanguage === 'ar' ? 'معلومات' : 'Information')}
                </h4>
                <p className="text-sm text-white/90">{toastMessage}</p>
              </div>
            </div>
            <button
              onClick={() => setShowSuccessToast(false)}
              className="ml-4 p-1 rounded-full hover:bg-white/20 transition-colors"
              aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
