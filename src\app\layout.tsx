import type { Metada<PERSON>, Viewport } from 'next';
import { Inter, Cairo } from 'next/font/google';
import { Suspense } from 'react';
import '../styles/globals.css';

// Import components directly for layout
import Providers from './providers';
import { MainLayout } from '../components/layout/MainLayout';

// Optimized font loading
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  display: 'swap',
  variable: '--font-cairo',
});

// Enhanced metadata
export const metadata: Metadata = {
  title: {
    default: 'EcommercePro - Enterprise E-commerce Platform',
    template: '%s | EcommercePro',
  },
  description: 'Professional e-commerce platform with advanced security, payment integration, and comprehensive admin dashboard. Built with Next.js, TypeScript, and modern technologies.',
  keywords: [
    'ecommerce',
    'online shopping',
    'e-commerce platform',
    'next.js',
    'typescript',
    'react',
    'tailwind css',
    'stripe',
    'paypal',
    'تجارة إلكترونية',
    'متجر إلكتروني',
    'منصة تجارية'
  ],
  authors: [{ name: 'EcommercePro Team', url: 'https://ecommercepro.com' }],
  creator: 'EcommercePro Team',
  publisher: 'EcommercePro',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ecommercepro.com'),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en',
      'ar-SA': '/ar',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ecommercepro.com',
    siteName: 'EcommercePro',
    title: 'EcommercePro - Enterprise E-commerce Platform',
    description: 'Professional e-commerce platform with advanced security and modern features',
    images: [
      {
        url: '/images/og-default.jpg',
        width: 1200,
        height: 630,
        alt: 'EcommercePro Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@ecommercepro',
    creator: '@ecommercepro',
    title: 'EcommercePro - Enterprise E-commerce Platform',
    description: 'Professional e-commerce platform with advanced security and modern features',
    images: ['/images/twitter-default.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/icons/icon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/icons/icon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/icons/apple-touch-icon.png' },
      { url: '/icons/apple-touch-icon-180x180.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'apple-touch-icon-precomposed',
        url: '/icons/apple-touch-icon-precomposed.png',
      },
    ],
  },
  manifest: '/manifest.json',
  category: 'technology',
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${cairo.variable}`} suppressHydrationWarning>
      <head>
        {/* Preload critical resources */}
        <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        <link rel="preload" href="/fonts/cairo-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//images.unsplash.com" />
        <link rel="dns-prefetch" href="//api.stripe.com" />

        {/* Preconnect to critical third-party origins */}
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

        {/* Performance hints */}
        <meta httpEquiv="Accept-CH" content="DPR, Viewport-Width, Width" />
      </head>
      <body className={`${inter.className} antialiased`} suppressHydrationWarning>
        <Suspense fallback={
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        }>
          <Providers>
            <MainLayout>
              {children}
            </MainLayout>
          </Providers>
        </Suspense>

        {/* Performance Optimizations */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Performance optimizations
              (function() {
                // Preload critical resources
                const preloadResource = (href, as, type) => {
                  const link = document.createElement('link');
                  link.rel = 'preload';
                  link.href = href;
                  link.as = as;
                  if (type) link.type = type;
                  document.head.appendChild(link);
                };

                // Preload critical fonts
                preloadResource('/fonts/inter-var.woff2', 'font', 'font/woff2');
                preloadResource('/fonts/cairo-var.woff2', 'font', 'font/woff2');

                // Service Worker Registration
                if ('serviceWorker' in navigator) {
                  window.addEventListener('load', function() {
                    navigator.serviceWorker.register('/sw.js')
                      .then(function(registration) {
                        console.log('SW registered: ', registration);
                      })
                      .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                      });
                  });
                }

                // Performance monitoring
                if ('PerformanceObserver' in window) {
                  // Monitor LCP
                  new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    if (lastEntry.startTime > 2500) {
                      console.warn('LCP is slow:', lastEntry.startTime);
                    }
                  }).observe({ entryTypes: ['largest-contentful-paint'] });
                }
              })();
            `,
          }}
        />
      </body>
    </html>
  );
}
