@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Tailwind components */
@layer components {
  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-info {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-outline {
    @apply border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }

  .btn-outline-primary {
    @apply border border-primary-600 bg-transparent text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg;
  }

  /* Card components */
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Form components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 resize-vertical;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .form-checkbox {
    @apply h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500;
  }

  .form-radio {
    @apply h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-red-600 mt-1;
  }

  .form-help {
    @apply text-sm text-gray-500 mt-1;
  }

  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }

  /* Alert components */
  .alert {
    @apply p-4 rounded-md;
  }

  .alert-primary {
    @apply bg-primary-50 border border-primary-200 text-primary-800;
  }

  .alert-success {
    @apply bg-green-50 border border-green-200 text-green-800;
  }

  .alert-danger {
    @apply bg-red-50 border border-red-200 text-red-800;
  }

  .alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
  }

  .alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-800;
  }

  /* Navigation components */
  .nav {
    @apply flex space-x-8;
  }

  .nav-link {
    @apply text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition-colors;
  }

  .nav-link-active {
    @apply text-primary-600 bg-primary-50;
  }

  /* Table components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  .table-row-hover {
    @apply hover:bg-gray-50;
  }

  /* Modal components */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
  }

  .modal-container {
    @apply fixed inset-0 z-10 overflow-y-auto;
  }

  .modal-content {
    @apply relative bg-white rounded-lg shadow-xl transform transition-all;
  }

  .modal-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .modal-body {
    @apply px-6 py-4;
  }

  .modal-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3;
  }

  /* Dropdown components */
  .dropdown {
    @apply relative inline-block text-left;
  }

  .dropdown-menu {
    @apply absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
  }

  /* Pagination components */
  .pagination {
    @apply flex items-center justify-between;
  }

  .pagination-info {
    @apply text-sm text-gray-700;
  }

  .pagination-nav {
    @apply flex space-x-2;
  }

  .pagination-link {
    @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50;
  }

  .pagination-link-active {
    @apply text-primary-600 bg-primary-50 border-primary-500;
  }

  .pagination-link-disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Loading components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  .spinner-sm {
    @apply h-4 w-4;
  }

  .spinner-md {
    @apply h-6 w-6;
  }

  .spinner-lg {
    @apply h-8 w-8;
  }

  .spinner-xl {
    @apply h-12 w-12;
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .skeleton-text {
    @apply h-4 bg-gray-200 rounded;
  }

  .skeleton-avatar {
    @apply h-10 w-10 bg-gray-200 rounded-full;
  }

  /* Progress components */
  .progress {
    @apply w-full bg-gray-200 rounded-full h-2;
  }

  .progress-bar {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
  }

  /* Tooltip components */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg;
  }

  /* Breadcrumb components */
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500;
  }

  .breadcrumb-item {
    @apply hover:text-gray-700;
  }

  .breadcrumb-separator {
    @apply text-gray-400;
  }

  /* Status indicators */
  .status-dot {
    @apply inline-block h-2 w-2 rounded-full;
  }

  .status-online {
    @apply bg-green-400;
  }

  .status-offline {
    @apply bg-gray-400;
  }

  .status-busy {
    @apply bg-red-400;
  }

  .status-away {
    @apply bg-yellow-400;
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(8px);
  }

  .backdrop-blur-lg {
    backdrop-filter: blur(16px);
  }

  .backdrop-blur-xl {
    backdrop-filter: blur(24px);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}
