export interface Service {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  description: string;
  description_ar: string;
  shortDescription?: string;
  shortDescription_ar?: string;
  category: ServiceCategory;
  subcategory?: ServiceCategory;
  type: ServiceType;
  pricing: ServicePricing;
  duration: ServiceDuration;
  availability: ServiceAvailability;
  requirements: ServiceRequirement[];
  deliverables: ServiceDeliverable[];
  features: string[];
  features_ar: string[];
  images: ServiceImage[];
  documents?: ServiceDocument[];
  faqs: ServiceFAQ[];
  reviews: ServiceReview[];
  rating: ServiceRating;
  tags: string[];
  seo: ServiceSEO;
  status: ServiceStatus;
  isActive: boolean;
  isFeatured: boolean;
  isPopular: boolean;
  orderCount: number;
  viewCount: number;
  createdBy: string;
  assignedTo?: string[];
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  description?: string;
  description_ar?: string;
  icon?: string;
  parentId?: string;
}

export type ServiceType = 
  | 'inspection'
  | 'certification'
  | 'consultation'
  | 'training'
  | 'maintenance'
  | 'installation'
  | 'repair'
  | 'testing'
  | 'audit'
  | 'design'
  | 'development'
  | 'support'
  | 'other';

export interface ServicePricing {
  type: PricingType;
  basePrice: number;
  currency: string;
  billingCycle?: BillingCycle;
  tiers?: PricingTier[];
  customPricing: boolean;
  priceOnRequest: boolean;
  discounts?: ServiceDiscount[];
  taxes?: ServiceTax[];
}

export type PricingType = 'fixed' | 'hourly' | 'daily' | 'monthly' | 'yearly' | 'per_unit' | 'tiered' | 'custom';
export type BillingCycle = 'one_time' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

export interface PricingTier {
  id: string;
  name: string;
  name_ar: string;
  minQuantity: number;
  maxQuantity?: number;
  price: number;
  description?: string;
  description_ar?: string;
}

export interface ServiceDiscount {
  id: string;
  name: string;
  type: 'percentage' | 'fixed';
  value: number;
  conditions?: DiscountCondition[];
  validFrom?: string;
  validTo?: string;
  isActive: boolean;
}

export interface DiscountCondition {
  type: 'min_quantity' | 'min_amount' | 'user_type' | 'first_time' | 'bulk_order';
  value: string | number;
}

export interface ServiceTax {
  name: string;
  rate: number;
  type: 'percentage' | 'fixed';
  isInclusive: boolean;
}

export interface ServiceDuration {
  estimated: number;
  minimum: number;
  maximum: number;
  unit: DurationUnit;
  isFlexible: boolean;
  notes?: string;
  notes_ar?: string;
}

export type DurationUnit = 'hours' | 'days' | 'weeks' | 'months';

export interface ServiceAvailability {
  isAvailable: boolean;
  schedule: ServiceSchedule[];
  timeZone: string;
  leadTime: number;
  leadTimeUnit: DurationUnit;
  maxAdvanceBooking: number;
  maxAdvanceBookingUnit: DurationUnit;
  blackoutDates?: string[];
  capacity?: number;
  locations?: ServiceLocation[];
}

export interface ServiceSchedule {
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
}

export interface ServiceLocation {
  id: string;
  name: string;
  name_ar: string;
  address: string;
  city: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isActive: boolean;
}

export interface ServiceRequirement {
  id: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  type: RequirementType;
  isRequired: boolean;
  options?: string[];
  validation?: RequirementValidation;
}

export type RequirementType = 
  | 'text'
  | 'number'
  | 'email'
  | 'phone'
  | 'date'
  | 'time'
  | 'file'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'textarea';

export interface RequirementValidation {
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  pattern?: string;
  fileTypes?: string[];
  maxFileSize?: number;
}

export interface ServiceDeliverable {
  id: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  type: DeliverableType;
  format?: string;
  estimatedDelivery: number;
  deliveryUnit: DurationUnit;
  isIncluded: boolean;
  additionalCost?: number;
}

export type DeliverableType = 
  | 'document'
  | 'certificate'
  | 'report'
  | 'presentation'
  | 'software'
  | 'hardware'
  | 'training'
  | 'consultation'
  | 'other';

export interface ServiceImage {
  id: string;
  url: string;
  alt: string;
  alt_ar: string;
  isPrimary: boolean;
  sortOrder: number;
  type: 'gallery' | 'before' | 'after' | 'process' | 'result';
}

export interface ServiceDocument {
  id: string;
  name: string;
  name_ar: string;
  url: string;
  type: 'brochure' | 'specification' | 'manual' | 'certificate' | 'sample' | 'other';
  size: number;
  downloadCount: number;
  isPublic: boolean;
}

export interface ServiceFAQ {
  id: string;
  question: string;
  question_ar: string;
  answer: string;
  answer_ar: string;
  category?: string;
  sortOrder: number;
  isActive: boolean;
}

export interface ServiceReview {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  orderId: string;
  rating: number;
  title: string;
  comment: string;
  pros?: string[];
  cons?: string[];
  images?: string[];
  isVerified: boolean;
  isApproved: boolean;
  helpfulCount: number;
  reportCount: number;
  response?: ServiceReviewResponse;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceReviewResponse {
  message: string;
  respondedBy: string;
  respondedAt: string;
}

export interface ServiceRating {
  average: number;
  count: number;
  distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface ServiceSEO {
  metaTitle?: string;
  metaTitle_ar?: string;
  metaDescription?: string;
  metaDescription_ar?: string;
  metaKeywords: string[];
  canonicalUrl?: string;
  ogTitle?: string;
  ogTitle_ar?: string;
  ogDescription?: string;
  ogDescription_ar?: string;
  ogImage?: string;
  structuredData?: Record<string, any>;
}

export type ServiceStatus = 'draft' | 'published' | 'archived' | 'suspended';

export interface ServiceOrder {
  id: string;
  serviceId: string;
  userId: string;
  status: ServiceOrderStatus;
  requirements: ServiceOrderRequirement[];
  pricing: ServiceOrderPricing;
  timeline: ServiceOrderTimeline;
  assignedTo?: string[];
  notes?: string;
  attachments?: ServiceOrderAttachment[];
  communications?: ServiceOrderCommunication[];
  deliverables?: ServiceOrderDeliverable[];
  feedback?: ServiceOrderFeedback;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export type ServiceOrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'in_progress'
  | 'on_hold'
  | 'completed'
  | 'cancelled'
  | 'refunded';

export interface ServiceOrderRequirement {
  requirementId: string;
  value: string | string[] | number | boolean;
  files?: string[];
}

export interface ServiceOrderPricing {
  basePrice: number;
  additionalCosts: ServiceAdditionalCost[];
  discounts: ServiceAppliedDiscount[];
  taxes: ServiceAppliedTax[];
  totalAmount: number;
  currency: string;
}

export interface ServiceAdditionalCost {
  name: string;
  amount: number;
  description?: string;
}

export interface ServiceAppliedDiscount {
  discountId: string;
  name: string;
  amount: number;
  type: 'percentage' | 'fixed';
}

export interface ServiceAppliedTax {
  name: string;
  amount: number;
  rate: number;
}

export interface ServiceOrderTimeline {
  estimatedStart: string;
  estimatedCompletion: string;
  actualStart?: string;
  actualCompletion?: string;
  milestones: ServiceOrderMilestone[];
}

export interface ServiceOrderMilestone {
  id: string;
  name: string;
  description?: string;
  estimatedDate: string;
  actualDate?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'delayed';
  deliverables?: string[];
}

export interface ServiceOrderAttachment {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadedBy: string;
  uploadedAt: string;
}

export interface ServiceOrderCommunication {
  id: string;
  type: 'message' | 'call' | 'meeting' | 'email';
  subject?: string;
  message: string;
  fromUserId: string;
  toUserId: string;
  attachments?: string[];
  isInternal: boolean;
  createdAt: string;
}

export interface ServiceOrderDeliverable {
  deliverableId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'delivered';
  files?: string[];
  notes?: string;
  deliveredAt?: string;
}

export interface ServiceOrderFeedback {
  rating: number;
  comment: string;
  pros?: string[];
  cons?: string[];
  wouldRecommend: boolean;
  submittedAt: string;
}

// Request/Response types
export interface CreateServiceRequest {
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  categoryId: string;
  type: ServiceType;
  pricing: Omit<ServicePricing, 'discounts' | 'taxes'>;
  duration: ServiceDuration;
  availability: ServiceAvailability;
  requirements: Omit<ServiceRequirement, 'id'>[];
  deliverables: Omit<ServiceDeliverable, 'id'>[];
  features: string[];
  features_ar: string[];
  images: Omit<ServiceImage, 'id'>[];
  tags: string[];
  seo?: Partial<ServiceSEO>;
  status: ServiceStatus;
}

export interface ServiceSearchFilters {
  category?: string;
  type?: ServiceType;
  priceMin?: number;
  priceMax?: number;
  rating?: number;
  location?: string;
  availability?: boolean;
  featured?: boolean;
  popular?: boolean;
  tags?: string[];
  search?: string;
  sortBy?: ServiceSortOption;
  sortOrder?: 'asc' | 'desc';
}

export type ServiceSortOption = 
  | 'name'
  | 'price'
  | 'rating'
  | 'popularity'
  | 'newest'
  | 'orders'
  | 'reviews';

export interface ServiceListResponse {
  services: Service[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    categories: { id: string; name: string; count: number }[];
    types: { type: ServiceType; count: number }[];
    priceRange: { min: number; max: number };
    locations: { id: string; name: string; count: number }[];
  };
}
