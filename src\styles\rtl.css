/* RTL (Right-to-Left) styles for Arabic language support */

/* Base RTL styles */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  font-family: 'Noto Sans Arabic', '<PERSON><PERSON><PERSON>', 'Cairo', '<PERSON><PERSON>', sans-serif;
}

/* Text alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Margins and padding */
[dir="rtl"] .ml-auto {
  margin-left: unset;
  margin-right: auto;
}

[dir="rtl"] .mr-auto {
  margin-right: unset;
  margin-left: auto;
}

[dir="rtl"] .ml-1 { margin-left: unset; margin-right: 0.25rem; }
[dir="rtl"] .ml-2 { margin-left: unset; margin-right: 0.5rem; }
[dir="rtl"] .ml-3 { margin-left: unset; margin-right: 0.75rem; }
[dir="rtl"] .ml-4 { margin-left: unset; margin-right: 1rem; }
[dir="rtl"] .ml-5 { margin-left: unset; margin-right: 1.25rem; }
[dir="rtl"] .ml-6 { margin-left: unset; margin-right: 1.5rem; }
[dir="rtl"] .ml-8 { margin-left: unset; margin-right: 2rem; }

[dir="rtl"] .mr-1 { margin-right: unset; margin-left: 0.25rem; }
[dir="rtl"] .mr-2 { margin-right: unset; margin-left: 0.5rem; }
[dir="rtl"] .mr-3 { margin-right: unset; margin-left: 0.75rem; }
[dir="rtl"] .mr-4 { margin-right: unset; margin-left: 1rem; }
[dir="rtl"] .mr-5 { margin-right: unset; margin-left: 1.25rem; }
[dir="rtl"] .mr-6 { margin-right: unset; margin-left: 1.5rem; }
[dir="rtl"] .mr-8 { margin-right: unset; margin-left: 2rem; }

[dir="rtl"] .pl-1 { padding-left: unset; padding-right: 0.25rem; }
[dir="rtl"] .pl-2 { padding-left: unset; padding-right: 0.5rem; }
[dir="rtl"] .pl-3 { padding-left: unset; padding-right: 0.75rem; }
[dir="rtl"] .pl-4 { padding-left: unset; padding-right: 1rem; }
[dir="rtl"] .pl-5 { padding-left: unset; padding-right: 1.25rem; }
[dir="rtl"] .pl-6 { padding-left: unset; padding-right: 1.5rem; }
[dir="rtl"] .pl-8 { padding-left: unset; padding-right: 2rem; }

[dir="rtl"] .pr-1 { padding-right: unset; padding-left: 0.25rem; }
[dir="rtl"] .pr-2 { padding-right: unset; padding-left: 0.5rem; }
[dir="rtl"] .pr-3 { padding-right: unset; padding-left: 0.75rem; }
[dir="rtl"] .pr-4 { padding-right: unset; padding-left: 1rem; }
[dir="rtl"] .pr-5 { padding-right: unset; padding-left: 1.25rem; }
[dir="rtl"] .pr-6 { padding-right: unset; padding-left: 1.5rem; }
[dir="rtl"] .pr-8 { padding-right: unset; padding-left: 2rem; }

/* Flexbox */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

/* Positioning */
[dir="rtl"] .left-0 { left: unset; right: 0; }
[dir="rtl"] .left-1 { left: unset; right: 0.25rem; }
[dir="rtl"] .left-2 { left: unset; right: 0.5rem; }
[dir="rtl"] .left-3 { left: unset; right: 0.75rem; }
[dir="rtl"] .left-4 { left: unset; right: 1rem; }
[dir="rtl"] .left-5 { left: unset; right: 1.25rem; }
[dir="rtl"] .left-6 { left: unset; right: 1.5rem; }
[dir="rtl"] .left-8 { left: unset; right: 2rem; }

[dir="rtl"] .right-0 { right: unset; left: 0; }
[dir="rtl"] .right-1 { right: unset; left: 0.25rem; }
[dir="rtl"] .right-2 { right: unset; left: 0.5rem; }
[dir="rtl"] .right-3 { right: unset; left: 0.75rem; }
[dir="rtl"] .right-4 { right: unset; left: 1rem; }
[dir="rtl"] .right-5 { right: unset; left: 1.25rem; }
[dir="rtl"] .right-6 { right: unset; left: 1.5rem; }
[dir="rtl"] .right-8 { right: unset; left: 2rem; }

/* Borders */
[dir="rtl"] .border-l { border-left: unset; border-right: 1px solid; }
[dir="rtl"] .border-r { border-right: unset; border-left: 1px solid; }
[dir="rtl"] .border-l-0 { border-left: unset; border-right: 0; }
[dir="rtl"] .border-r-0 { border-right: unset; border-left: 0; }

/* Border radius */
[dir="rtl"] .rounded-l { border-radius: 0 0.375rem 0.375rem 0; }
[dir="rtl"] .rounded-r { border-radius: 0.375rem 0 0 0.375rem; }
[dir="rtl"] .rounded-tl { border-top-left-radius: unset; border-top-right-radius: 0.375rem; }
[dir="rtl"] .rounded-tr { border-top-right-radius: unset; border-top-left-radius: 0.375rem; }
[dir="rtl"] .rounded-bl { border-bottom-left-radius: unset; border-bottom-right-radius: 0.375rem; }
[dir="rtl"] .rounded-br { border-bottom-right-radius: unset; border-bottom-left-radius: 0.375rem; }

/* Transforms */
[dir="rtl"] .rotate-90 { transform: rotate(-90deg); }
[dir="rtl"] .rotate-180 { transform: rotate(180deg); }
[dir="rtl"] .rotate-270 { transform: rotate(90deg); }

/* Icons and arrows */
[dir="rtl"] .icon-arrow-left::before {
  content: "→";
}

[dir="rtl"] .icon-arrow-right::before {
  content: "←";
}

/* Navigation */
[dir="rtl"] .nav-item {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .nav-item:last-child {
  margin-right: 0;
}

/* Breadcrumbs */
[dir="rtl"] .breadcrumb-separator::before {
  content: "\\";
  transform: scaleX(-1);
}

/* Dropdown menus */
[dir="rtl"] .dropdown-menu {
  left: unset;
  right: 0;
}

/* Forms */
[dir="rtl"] .form-check-input {
  margin-left: 0;
  margin-right: -1.25rem;
}

[dir="rtl"] .form-check-label {
  padding-left: 0;
  padding-right: 1.25rem;
}

/* Tables */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}

[dir="rtl"] th:first-child,
[dir="rtl"] td:first-child {
  text-align: right;
}

[dir="rtl"] th:last-child,
[dir="rtl"] td:last-child {
  text-align: left;
}

/* Cards */
[dir="rtl"] .card-img-left {
  border-radius: 0 0.375rem 0.375rem 0;
}

[dir="rtl"] .card-img-right {
  border-radius: 0.375rem 0 0 0.375rem;
}

/* Tooltips */
[dir="rtl"] .tooltip-left {
  left: unset;
  right: 100%;
  margin-right: 0.5rem;
  margin-left: 0;
}

[dir="rtl"] .tooltip-right {
  right: unset;
  left: 100%;
  margin-left: 0.5rem;
  margin-right: 0;
}

/* Modals */
[dir="rtl"] .modal-dialog {
  margin-left: auto;
  margin-right: auto;
}

/* Carousel */
[dir="rtl"] .carousel-control-prev {
  left: unset;
  right: 0;
}

[dir="rtl"] .carousel-control-next {
  right: unset;
  left: 0;
}

[dir="rtl"] .carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='m2.75 0 1.5 1.5L2.75 3l-1.5-1.5L2.75 0z'/%3e%3c/svg%3e");
}

[dir="rtl"] .carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='m5.25 0-1.5 1.5L5.25 3l1.5-1.5L5.25 0z'/%3e%3c/svg%3e");
}

/* Pagination */
[dir="rtl"] .pagination {
  direction: rtl;
}

/* Progress bars */
[dir="rtl"] .progress-bar {
  right: 0;
  left: unset;
}

/* Scrollbars */
[dir="rtl"] ::-webkit-scrollbar {
  direction: rtl;
}

/* Custom RTL utilities */
.rtl-flip {
  transform: scaleX(-1);
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(1);
}

.ltr-only {
  display: block;
}

[dir="rtl"] .ltr-only {
  display: none;
}

.rtl-only {
  display: none;
}

[dir="rtl"] .rtl-only {
  display: block;
}

/* Arabic number formatting */
[dir="rtl"] .arabic-numbers {
  font-feature-settings: "lnum" 1;
}

/* Text direction utilities */
.text-ltr {
  direction: ltr;
  text-align: left;
}

.text-rtl {
  direction: rtl;
  text-align: right;
}

/* Float utilities for RTL */
[dir="rtl"] .float-left {
  float: right;
}

[dir="rtl"] .float-right {
  float: left;
}

/* Clear utilities for RTL */
[dir="rtl"] .clear-left {
  clear: right;
}

[dir="rtl"] .clear-right {
  clear: left;
}

/* Responsive RTL utilities */
@media (min-width: 640px) {
  [dir="rtl"] .sm\\:ml-4 { margin-left: unset; margin-right: 1rem; }
  [dir="rtl"] .sm\\:mr-4 { margin-right: unset; margin-left: 1rem; }
  [dir="rtl"] .sm\\:pl-4 { padding-left: unset; padding-right: 1rem; }
  [dir="rtl"] .sm\\:pr-4 { padding-right: unset; padding-left: 1rem; }
}

@media (min-width: 768px) {
  [dir="rtl"] .md\\:ml-6 { margin-left: unset; margin-right: 1.5rem; }
  [dir="rtl"] .md\\:mr-6 { margin-right: unset; margin-left: 1.5rem; }
  [dir="rtl"] .md\\:pl-6 { padding-left: unset; padding-right: 1.5rem; }
  [dir="rtl"] .md\\:pr-6 { padding-right: unset; padding-left: 1.5rem; }
}

@media (min-width: 1024px) {
  [dir="rtl"] .lg\\:ml-8 { margin-left: unset; margin-right: 2rem; }
  [dir="rtl"] .lg\\:mr-8 { margin-right: unset; margin-left: 2rem; }
  [dir="rtl"] .lg\\:pl-8 { padding-left: unset; padding-right: 2rem; }
  [dir="rtl"] .lg\\:pr-8 { padding-right: unset; padding-left: 2rem; }
}
