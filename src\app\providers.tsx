'use client';

import { ReactNode, useEffect, Suspense } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { ThemeProvider } from 'next-themes';

import { useLanguageStore } from '../stores/languageStore';
import { useAuthStore } from '../stores/authStore';
import { useCartStore } from '../stores/cartStore';
import { Locale } from '../lib/i18n';

// Import components directly
import { AuthModalProvider } from '../components/auth/AuthModalProvider';
import { ABTestingProvider } from '../components/marketing/ABTestingProvider';

// Optimized Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

interface ProvidersProps {
  children: ReactNode;
  locale?: string;
}

function Providers({ children, locale }: ProvidersProps) {
  const { setLanguage } = useLanguageStore();
  const initializeAuth = useAuthStore((state) => state.initialize);
  const initializeCart = useCartStore((state) => state.initialize);

  // Initialize language based on locale prop
  useEffect(() => {
    if (locale && (locale === 'ar' || locale === 'en')) {
      setLanguage(locale as Locale);
    }
  }, [locale, setLanguage]);

  // Initialize stores on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      initializeAuth();
      initializeCart();
    }
  }, [initializeAuth, initializeCart]);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        storageKey="ecommercepro-theme"
        disableTransitionOnChange
      >
        <ABTestingProvider>
          <AuthModalProvider>
            {children}
          </AuthModalProvider>
        </ABTestingProvider>

        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'var(--toast-bg)',
              color: 'var(--toast-color)',
              border: '1px solid var(--toast-border)',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#10B981',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#EF4444',
                secondary: '#fff',
              },
            },
            loading: {
              duration: Infinity,
            },
          }}
        />
      </ThemeProvider>

      {/* React Query Devtools - only in development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}

export default Providers;
