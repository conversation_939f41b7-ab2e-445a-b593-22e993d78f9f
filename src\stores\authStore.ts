import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '../types';
import { signIn, signUp, signOut, getAuthStatus, refreshSession } from '../lib/auth';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isLocked: boolean;
  failedAttempts: number;
  lastFailedAttempt: string | null;
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ error: any | null }>;
  signOut: () => Promise<void>;
  setUser: (user: User | null) => void;
  checkAuth: () => boolean;
  refreshUserSession: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => {
      // التحقق من حالة المصادقة عند تهيئة المخزن
      const { isAuthenticated, user } = getAuthStatus();

      console.log('Initializing auth store, authenticated:', isAuthenticated);

      return {
        user: isAuthenticated ? user : null,
        isLoading: false,
        isAuthenticated,
        isLocked: false,
        failedAttempts: 0,
        lastFailedAttempt: null,

        signIn: async (email, password) => {
          console.log('Auth store: signing in user');
          set({ isLoading: true });

          try {
            // Check if account is locked
            if (get().isLocked) {
              throw new Error('Account is locked. Please try again later.');
            }

            const { user, error } = await signIn(email, password);

            if (error) {
              console.error('Auth store: sign in error', error);
              
              // Track failed attempts
              const currentState = get();
              const newFailedAttempts = currentState.failedAttempts + 1;
              
              set({
                failedAttempts: newFailedAttempts,
                lastFailedAttempt: new Date().toISOString(),
                isLoading: false
              });
              
              // Lock account after 5 failed attempts
              if (newFailedAttempts >= 5) {
                set({ isLocked: true });
              }
              
              return { error };
            }

            if (user) {
              console.log('Auth store: sign in successful');
              set({
                user,
                isAuthenticated: true,
                isLoading: false,
                failedAttempts: 0,
                isLocked: false,
                lastFailedAttempt: null
              });
            }

            return { error: null };
          } catch (error) {
            console.error('Auth store: unexpected sign in error', error);
            return { error };
          } finally {
            set({ isLoading: false });
          }
        },

        signUp: async (email, password, userData) => {
          console.log('Auth store: signing up user');
          set({ isLoading: true });

          try {
            const { user, error } = await signUp(email, password, userData);

            if (error) {
              console.error('Auth store: sign up error', error);
              return { error };
            }

            if (user) {
              console.log('Auth store: sign up successful');
              set({
                user,
                isAuthenticated: true,
                isLoading: false,
              });
            }

            return { error: null };
          } catch (error) {
            console.error('Auth store: unexpected sign up error', error);
            return { error };
          } finally {
            set({ isLoading: false });
          }
        },

        signOut: async () => {
          console.log('Auth store: signing out user');
          await signOut();
          set({ user: null, isAuthenticated: false });
        },

        setUser: (user) => {
          console.log('Auth store: setting user', user?.email);
          set({ user, isAuthenticated: !!user });

          // تجديد الجلسة عند تحديث بيانات المستخدم
          if (user) {
            refreshSession();
          }
        },

        checkAuth: () => {
          console.log('Auth store: checking authentication');
          const { isAuthenticated, user } = getAuthStatus();

          // تحديث حالة المخزن إذا تغيرت حالة المصادقة
          const currentAuth = get().isAuthenticated;
          if (currentAuth !== isAuthenticated) {
            console.log('Auth store: authentication status changed', isAuthenticated);
            set({ isAuthenticated, user: isAuthenticated ? user : null });
          }

          return isAuthenticated;
        },

        refreshUserSession: () => {
          console.log('Auth store: refreshing user session');
          const success = refreshSession();
          return success;
        },
      };
    },
    {
      name: 'auth-storage',
      // استخدام وظائف مخصصة للتخزين المستمر
      // لتجنب تخزين بيانات المستخدم مرتين
      // (مرة في المخزن ومرة في التخزين المحلي المشفر)
      partialize: (state) => ({ isLoading: state.isLoading, isAuthenticated: state.isAuthenticated }),
    }
  )
);