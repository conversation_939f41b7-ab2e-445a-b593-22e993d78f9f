import { NextRequest, NextResponse } from 'next/server';
import { withSecurity, generateTokens, loginAttemptMiddleware, logAuditEvent } from '../../../../middleware/security';
import { userRepository } from '../../../../lib/database-secure';
import { loginSchema } from '../../../../lib/validation-enhanced';
import { generateCSRFToken } from '../../../../lib/security';

async function loginHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { email, password, rememberMe } = (request as any).validatedData;

    // Authenticate user
    const user = await userRepository.authenticateUser(email, password);

    if (!user) {
      // Track failed login attempt
      const attemptResponse = loginAttemptMiddleware(request, false, email);
      if (attemptResponse) return attemptResponse;

      logAuditEvent(null, 'LOGIN_FAILED', 'USER', email, null, null, request);

      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check if user is active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated' },
        { status: 403 }
      );
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Generate CSRF token
    const csrfToken = generateCSRFToken(user.id);

    // Track successful login
    loginAttemptMiddleware(request, true, email);

    // Log successful login
    logAuditEvent(user.id, 'LOGIN_SUCCESS', 'USER', user.id, null, null, request);

    // Create response
    const response = NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        emailVerified: user.emailVerified,
      },
      csrfToken,
      message: 'Login successful'
    });

    // Set secure HTTP-only cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/',
    };

    response.cookies.set('auth-token', accessToken, {
      ...cookieOptions,
      maxAge: rememberMe ? 60 * 60 * 24 * 30 : 60 * 60 * 24, // 30 days or 1 day
    });

    response.cookies.set('refresh-token', refreshToken, {
      ...cookieOptions,
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });

    response.cookies.set('session-id', user.id, {
      ...cookieOptions,
      maxAge: 60 * 60 * 24, // 1 day
    });

    return response;
  } catch (error) {
    console.error('Login API error:', error);
    logAuditEvent(null, 'LOGIN_ERROR', 'SYSTEM', null, null, { error: error.message }, request);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const POST = withSecurity(loginHandler, {
  requireAuth: false,
  skipCSRF: true, // Skip CSRF for login endpoint
  validationSchema: loginSchema,
  validationSource: 'body',
});
