export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  avatar?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  language: 'ar' | 'en';
  timezone: string;
  role: UserRole;
  status: UserStatus;
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  preferences: UserPreferences;
  addresses: Address[];
  paymentMethods: PaymentMethod[];
  loyaltyPoints: number;
  totalOrders: number;
  totalSpent: number;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'customer' | 'admin' | 'moderator' | 'vendor' | 'support';

export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending_verification';

export interface UserPreferences {
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  marketing: MarketingPreferences;
  display: DisplayPreferences;
}

export interface NotificationPreferences {
  email: {
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
    securityAlerts: boolean;
    productUpdates: boolean;
  };
  push: {
    orderUpdates: boolean;
    promotions: boolean;
    newProducts: boolean;
    priceDrops: boolean;
  };
  sms: {
    orderUpdates: boolean;
    securityAlerts: boolean;
    deliveryUpdates: boolean;
  };
}

export interface PrivacyPreferences {
  profileVisibility: 'public' | 'private' | 'friends';
  showOnlineStatus: boolean;
  allowDataCollection: boolean;
  allowPersonalization: boolean;
  allowThirdPartySharing: boolean;
}

export interface MarketingPreferences {
  allowEmailMarketing: boolean;
  allowSmsMarketing: boolean;
  allowPushMarketing: boolean;
  allowPersonalizedAds: boolean;
  interests: string[];
  preferredCategories: string[];
}

export interface DisplayPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'ar' | 'en';
  currency: string;
  timezone: string;
  dateFormat: string;
  numberFormat: string;
}

export interface Address {
  id: string;
  type: 'home' | 'work' | 'other';
  isDefault: boolean;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  instructions?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'bank_account' | 'digital_wallet';
  isDefault: boolean;
  provider: string;
  last4: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName: string;
  billingAddress: Address;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserSession {
  id: string;
  userId: string;
  deviceId: string;
  deviceName: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browser: string;
  os: string;
  ipAddress: string;
  location?: {
    country: string;
    city: string;
    region: string;
  };
  isActive: boolean;
  lastActivityAt: string;
  createdAt: string;
}

export interface UserActivity {
  id: string;
  userId: string;
  type: ActivityType;
  description: string;
  metadata?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export type ActivityType = 
  | 'login'
  | 'logout'
  | 'password_change'
  | 'profile_update'
  | 'order_placed'
  | 'order_cancelled'
  | 'payment_added'
  | 'address_added'
  | 'wishlist_item_added'
  | 'review_posted'
  | 'account_verification'
  | 'security_alert';

export interface UserStats {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  loyaltyPoints: number;
  reviewsCount: number;
  averageRating: number;
  wishlistItems: number;
  accountAge: number;
  lastOrderDate?: string;
  favoriteCategories: string[];
  preferredBrands: string[];
}

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  language?: 'ar' | 'en';
  acceptTerms: boolean;
  acceptMarketing?: boolean;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  language?: 'ar' | 'en';
  timezone?: string;
  preferences?: Partial<UserPreferences>;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface VerifyPhoneRequest {
  phone: string;
  code: string;
}

export interface UserSearchFilters {
  role?: UserRole;
  status?: UserStatus;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  createdAfter?: string;
  createdBefore?: string;
  lastLoginAfter?: string;
  lastLoginBefore?: string;
  totalSpentMin?: number;
  totalSpentMax?: number;
  totalOrdersMin?: number;
  totalOrdersMax?: number;
  search?: string;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Utility types
export type PublicUser = Omit<User, 'email' | 'phone' | 'addresses' | 'paymentMethods' | 'preferences'>;

export type UserProfile = Pick<User, 
  | 'id' 
  | 'firstName' 
  | 'lastName' 
  | 'displayName' 
  | 'avatar' 
  | 'language' 
  | 'loyaltyPoints' 
  | 'totalOrders' 
  | 'createdAt'
>;

export type UserSummary = Pick<User,
  | 'id'
  | 'email'
  | 'firstName'
  | 'lastName'
  | 'role'
  | 'status'
  | 'emailVerified'
  | 'lastLoginAt'
  | 'createdAt'
>;
