import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface AppSettings {
  // General settings
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  contactEmail: string;
  contactPhone: string;
  
  // Localization
  defaultLanguage: 'ar' | 'en';
  supportedLanguages: string[];
  defaultCurrency: string;
  supportedCurrencies: string[];
  timezone: string;
  
  // E-commerce settings
  taxRate: number;
  shippingFee: number;
  freeShippingThreshold: number;
  allowGuestCheckout: boolean;
  requireEmailVerification: boolean;
  
  // Payment settings
  enabledPaymentMethods: string[];
  paymentGatewaySettings: Record<string, any>;
  
  // Shipping settings
  enabledShippingMethods: string[];
  shippingZones: ShippingZone[];
  
  // Inventory settings
  trackInventory: boolean;
  allowBackorders: boolean;
  lowStockThreshold: number;
  
  // SEO settings
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string[];
  enableSitemap: boolean;
  enableRobotsTxt: boolean;
  
  // Social media
  socialMediaLinks: Record<string, string>;
  
  // Analytics
  googleAnalyticsId?: string;
  facebookPixelId?: string;
  enableAnalytics: boolean;
  
  // Security
  enableTwoFactorAuth: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  
  // Performance
  enableCaching: boolean;
  cacheTimeout: number;
  enableCompression: boolean;
  
  // Notifications
  emailNotifications: EmailNotificationSettings;
  pushNotifications: PushNotificationSettings;
  
  // Maintenance
  maintenanceMode: boolean;
  maintenanceMessage: string;
}

export interface ShippingZone {
  id: string;
  name: string;
  countries: string[];
  methods: ShippingMethod[];
}

export interface ShippingMethod {
  id: string;
  name: string;
  cost: number;
  estimatedDays: number;
  enabled: boolean;
}

export interface EmailNotificationSettings {
  orderConfirmation: boolean;
  orderStatusUpdate: boolean;
  passwordReset: boolean;
  newsletter: boolean;
  promotions: boolean;
}

export interface PushNotificationSettings {
  orderUpdates: boolean;
  promotions: boolean;
  newProducts: boolean;
  lowStock: boolean;
}

export interface SettingsState {
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  updateSettings: (settings: Partial<AppSettings>) => void;
  updateSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => void;
  resetSettings: () => void;
  loadSettings: () => Promise<void>;
  saveSettings: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const defaultSettings: AppSettings = {
  // General settings
  siteName: 'Commerce Pro',
  siteDescription: 'Professional E-commerce Platform',
  siteUrl: 'https://commercepro.com',
  contactEmail: '<EMAIL>',
  contactPhone: '+966 50 123 4567',
  
  // Localization
  defaultLanguage: 'ar',
  supportedLanguages: ['ar', 'en'],
  defaultCurrency: 'SAR',
  supportedCurrencies: ['SAR', 'USD', 'EUR'],
  timezone: 'Asia/Riyadh',
  
  // E-commerce settings
  taxRate: 0.15, // 15% VAT
  shippingFee: 25,
  freeShippingThreshold: 200,
  allowGuestCheckout: true,
  requireEmailVerification: false,
  
  // Payment settings
  enabledPaymentMethods: ['cash_on_delivery', 'bank_transfer'],
  paymentGatewaySettings: {},
  
  // Shipping settings
  enabledShippingMethods: ['standard', 'express'],
  shippingZones: [
    {
      id: 'saudi-arabia',
      name: 'Saudi Arabia',
      countries: ['SA'],
      methods: [
        {
          id: 'standard',
          name: 'Standard Shipping',
          cost: 25,
          estimatedDays: 3,
          enabled: true
        },
        {
          id: 'express',
          name: 'Express Shipping',
          cost: 50,
          estimatedDays: 1,
          enabled: true
        }
      ]
    }
  ],
  
  // Inventory settings
  trackInventory: true,
  allowBackorders: false,
  lowStockThreshold: 10,
  
  // SEO settings
  metaTitle: 'Commerce Pro - Professional E-commerce Platform',
  metaDescription: 'Discover quality products and services with Commerce Pro',
  metaKeywords: ['ecommerce', 'online shopping', 'products', 'services'],
  enableSitemap: true,
  enableRobotsTxt: true,
  
  // Social media
  socialMediaLinks: {
    facebook: '',
    twitter: '',
    instagram: '',
    linkedin: '',
    youtube: ''
  },
  
  // Analytics
  enableAnalytics: false,
  
  // Security
  enableTwoFactorAuth: false,
  sessionTimeout: 3600, // 1 hour
  maxLoginAttempts: 5,
  
  // Performance
  enableCaching: true,
  cacheTimeout: 3600,
  enableCompression: true,
  
  // Notifications
  emailNotifications: {
    orderConfirmation: true,
    orderStatusUpdate: true,
    passwordReset: true,
    newsletter: false,
    promotions: false
  },
  
  pushNotifications: {
    orderUpdates: true,
    promotions: false,
    newProducts: false,
    lowStock: true
  },
  
  // Maintenance
  maintenanceMode: false,
  maintenanceMessage: 'We are currently performing maintenance. Please check back soon.'
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      isLoading: false,
      error: null,

      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        })),

      updateSetting: (key, value) =>
        set((state) => ({
          settings: { ...state.settings, [key]: value }
        })),

      resetSettings: () =>
        set({ settings: defaultSettings }),

      loadSettings: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // In a real app, this would fetch from an API
          // For now, we'll use the persisted settings
          set({ isLoading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load settings',
            isLoading: false 
          });
        }
      },

      saveSettings: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // In a real app, this would save to an API
          // For now, persistence is handled by zustand middleware
          set({ isLoading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to save settings',
            isLoading: false 
          });
        }
      },

      setLoading: (loading) =>
        set({ isLoading: loading }),

      setError: (error) =>
        set({ error })
    }),
    {
      name: 'settings-store',
      partialize: (state) => ({ settings: state.settings })
    }
  )
);
