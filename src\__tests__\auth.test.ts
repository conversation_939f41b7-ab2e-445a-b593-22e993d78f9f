/**
 * Authentication System Tests
 * Comprehensive test suite for authentication and security features
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { userRepository } from '../lib/database-secure';
import { hashPassword, verifyPassword } from '../lib/auth';
import { validateInput, loginSchema, registerSchema } from '../lib/validation-enhanced';
import { 
  generateCSRFToken, 
  validateCSRFToken, 
  checkRateLimit, 
  trackLoginAttempt 
} from '../lib/security';

// Mock database
jest.mock('../lib/database-secure');

describe('Authentication System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Password Hashing', () => {
    it('should hash passwords securely', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50);
    });

    it('should verify passwords correctly', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      const isValid = await verifyPassword(password, hash);
      const isInvalid = await verifyPassword('WrongPassword', hash);
      
      expect(isValid).toBe(true);
      expect(isInvalid).toBe(false);
    });

    it('should generate different hashes for same password', async () => {
      const password = 'TestPassword123!';
      const hash1 = await hashPassword(password);
      const hash2 = await hashPassword(password);
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('Input Validation', () => {
    describe('Login Validation', () => {
      it('should validate correct login data', () => {
        const validLogin = {
          email: '<EMAIL>',
          password: 'TestPassword123!',
          rememberMe: true,
        };

        const result = validateInput(loginSchema, validLogin);
        expect(result.success).toBe(true);
        expect(result.data).toEqual({
          email: '<EMAIL>',
          password: 'TestPassword123!',
          rememberMe: true,
        });
      });

      it('should reject invalid email format', () => {
        const invalidLogin = {
          email: 'invalid-email',
          password: 'TestPassword123!',
        };

        const result = validateInput(loginSchema, invalidLogin);
        expect(result.success).toBe(false);
        expect(result.errors).toContain('email: Invalid email format');
      });

      it('should reject empty password', () => {
        const invalidLogin = {
          email: '<EMAIL>',
          password: '',
        };

        const result = validateInput(loginSchema, invalidLogin);
        expect(result.success).toBe(false);
        expect(result.errors).toContain('password: Password is required');
      });
    });

    describe('Registration Validation', () => {
      it('should validate correct registration data', () => {
        const validRegistration = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
          phone: '+1234567890',
          language: 'en' as const,
          acceptTerms: true,
        };

        const result = validateInput(registerSchema, validRegistration);
        expect(result.success).toBe(true);
      });

      it('should reject weak passwords', () => {
        const invalidRegistration = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'weak',
          confirmPassword: 'weak',
          acceptTerms: true,
        };

        const result = validateInput(registerSchema, invalidRegistration);
        expect(result.success).toBe(false);
        expect(result.errors?.some(error => error.includes('Password must'))).toBe(true);
      });

      it('should reject mismatched passwords', () => {
        const invalidRegistration = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          confirmPassword: 'DifferentPassword123!',
          acceptTerms: true,
        };

        const result = validateInput(registerSchema, invalidRegistration);
        expect(result.success).toBe(false);
        expect(result.errors).toContain('confirmPassword: Passwords do not match');
      });

      it('should require terms acceptance', () => {
        const invalidRegistration = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
          acceptTerms: false,
        };

        const result = validateInput(registerSchema, invalidRegistration);
        expect(result.success).toBe(false);
        expect(result.errors).toContain('acceptTerms: You must accept the terms and conditions');
      });
    });
  });

  describe('CSRF Protection', () => {
    it('should generate valid CSRF tokens', () => {
      const sessionId = 'test-session-123';
      const token = generateCSRFToken(sessionId);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBe(64); // 32 bytes in hex
    });

    it('should validate correct CSRF tokens', () => {
      const sessionId = 'test-session-123';
      const token = generateCSRFToken(sessionId);
      
      const isValid = validateCSRFToken(sessionId, token);
      expect(isValid).toBe(true);
    });

    it('should reject invalid CSRF tokens', () => {
      const sessionId = 'test-session-123';
      generateCSRFToken(sessionId);
      
      const isValid = validateCSRFToken(sessionId, 'invalid-token');
      expect(isValid).toBe(false);
    });

    it('should reject tokens for different sessions', () => {
      const sessionId1 = 'test-session-123';
      const sessionId2 = 'test-session-456';
      const token = generateCSRFToken(sessionId1);
      
      const isValid = validateCSRFToken(sessionId2, token);
      expect(isValid).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within limit', () => {
      const identifier = 'test-ip-1';
      
      for (let i = 0; i < 50; i++) {
        const result = checkRateLimit(identifier);
        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(100 - i - 1);
      }
    });

    it('should block requests exceeding limit', () => {
      const identifier = 'test-ip-2';
      
      // Exhaust the rate limit
      for (let i = 0; i < 100; i++) {
        checkRateLimit(identifier);
      }
      
      // Next request should be blocked
      const result = checkRateLimit(identifier);
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });

    it('should reset rate limit after window expires', () => {
      const identifier = 'test-ip-3';
      
      // Mock time to simulate window expiry
      const originalNow = Date.now;
      Date.now = jest.fn(() => originalNow() + 16 * 60 * 1000); // 16 minutes later
      
      const result = checkRateLimit(identifier);
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(99);
      
      // Restore original Date.now
      Date.now = originalNow;
    });
  });

  describe('Login Attempt Tracking', () => {
    it('should track failed login attempts', () => {
      const identifier = '<EMAIL>';
      
      const result1 = trackLoginAttempt(identifier, false);
      expect(result1.allowed).toBe(true);
      expect(result1.attemptsRemaining).toBe(4);
      
      const result2 = trackLoginAttempt(identifier, false);
      expect(result2.allowed).toBe(true);
      expect(result2.attemptsRemaining).toBe(3);
    });

    it('should lock account after max failed attempts', () => {
      const identifier = '<EMAIL>';
      
      // Exhaust login attempts
      for (let i = 0; i < 5; i++) {
        trackLoginAttempt(identifier, false);
      }
      
      const result = trackLoginAttempt(identifier, false);
      expect(result.allowed).toBe(false);
      expect(result.attemptsRemaining).toBe(0);
      expect(result.lockedUntil).toBeDefined();
    });

    it('should reset attempts on successful login', () => {
      const identifier = '<EMAIL>';
      
      // Make some failed attempts
      trackLoginAttempt(identifier, false);
      trackLoginAttempt(identifier, false);
      
      // Successful login should reset
      const result = trackLoginAttempt(identifier, true);
      expect(result.allowed).toBe(true);
      expect(result.attemptsRemaining).toBe(5);
    });
  });

  describe('User Repository', () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      isActive: true,
      emailVerified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    beforeEach(() => {
      (userRepository.getUserByEmail as jest.Mock).mockReset();
      (userRepository.createUser as jest.Mock).mockReset();
      (userRepository.authenticateUser as jest.Mock).mockReset();
    });

    it('should create user with hashed password', async () => {
      (userRepository.createUser as jest.Mock).mockResolvedValue(mockUser);
      
      const userData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user' as const,
      };
      
      const result = await userRepository.createUser(userData, 'TestPassword123!');
      
      expect(result).toEqual(mockUser);
      expect(userRepository.createUser).toHaveBeenCalledWith(
        userData,
        'TestPassword123!'
      );
    });

    it('should authenticate user with correct credentials', async () => {
      (userRepository.authenticateUser as jest.Mock).mockResolvedValue(mockUser);
      
      const result = await userRepository.authenticateUser(
        '<EMAIL>',
        'TestPassword123!'
      );
      
      expect(result).toEqual(mockUser);
    });

    it('should reject authentication with incorrect credentials', async () => {
      (userRepository.authenticateUser as jest.Mock).mockResolvedValue(null);
      
      const result = await userRepository.authenticateUser(
        '<EMAIL>',
        'WrongPassword'
      );
      
      expect(result).toBeNull();
    });

    it('should find user by email', async () => {
      (userRepository.getUserByEmail as jest.Mock).mockReturnValue(mockUser);
      
      const result = userRepository.getUserByEmail('<EMAIL>');
      
      expect(result).toEqual(mockUser);
    });

    it('should return null for non-existent user', async () => {
      (userRepository.getUserByEmail as jest.Mock).mockReturnValue(null);
      
      const result = userRepository.getUserByEmail('<EMAIL>');
      
      expect(result).toBeNull();
    });
  });

  describe('Security Edge Cases', () => {
    it('should handle SQL injection attempts', () => {
      const maliciousInput = {
        email: "'; DROP TABLE users; --",
        password: 'TestPassword123!',
      };

      const result = validateInput(loginSchema, maliciousInput);
      expect(result.success).toBe(false);
    });

    it('should handle XSS attempts in input', () => {
      const maliciousInput = {
        firstName: '<script>alert("xss")</script>',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        acceptTerms: true,
      };

      const result = validateInput(registerSchema, maliciousInput);
      expect(result.success).toBe(false);
    });

    it('should handle extremely long inputs', () => {
      const longString = 'a'.repeat(1000);
      const maliciousInput = {
        firstName: longString,
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        acceptTerms: true,
      };

      const result = validateInput(registerSchema, maliciousInput);
      expect(result.success).toBe(false);
    });
  });
});
