/**
 * Admin Dashboard API
 * Provides comprehensive analytics and management data
 */

import { NextRequest, NextResponse } from 'next/server';
import { withSecurity } from '../../../../middleware/security';
import { getDatabase } from '../../../../lib/database-secure';

interface DashboardStats {
  overview: {
    totalUsers: number;
    totalOrders: number;
    totalRevenue: number;
    totalProducts: number;
    pendingOrders: number;
    lowStockProducts: number;
  };
  recentOrders: any[];
  topProducts: any[];
  salesChart: {
    labels: string[];
    data: number[];
  };
  userGrowth: {
    labels: string[];
    data: number[];
  };
  orderStatusDistribution: {
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
  };
  revenueByMonth: {
    labels: string[];
    data: number[];
  };
}

async function dashboardHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const db = getDatabase();
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Overview Statistics
    const totalUsers = db.prepare('SELECT COUNT(*) as count FROM users WHERE is_active = 1').get() as any;
    const totalOrders = db.prepare('SELECT COUNT(*) as count FROM orders').get() as any;
    const totalRevenue = db.prepare('SELECT SUM(total) as sum FROM orders WHERE payment_status = "paid"').get() as any;
    const totalProducts = db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = 1').get() as any;
    const pendingOrders = db.prepare('SELECT COUNT(*) as count FROM orders WHERE status = "pending"').get() as any;
    const lowStockProducts = db.prepare('SELECT COUNT(*) as count FROM products WHERE stock_quantity <= low_stock_threshold AND is_active = 1').get() as any;

    // Recent Orders
    const recentOrders = db.prepare(`
      SELECT o.*, u.first_name, u.last_name, u.email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 10
    `).all();

    // Top Products (by sales)
    const topProducts = db.prepare(`
      SELECT p.name, p.price, SUM(oi.quantity) as total_sold, SUM(oi.total_price) as revenue
      FROM products p
      JOIN order_items oi ON p.id = oi.product_id
      JOIN orders o ON oi.order_id = o.id
      WHERE o.payment_status = 'paid' AND o.created_at >= ?
      GROUP BY p.id
      ORDER BY total_sold DESC
      LIMIT 10
    `).all(startDate.toISOString());

    // Sales Chart (last 30 days)
    const salesData = db.prepare(`
      SELECT DATE(created_at) as date, SUM(total) as revenue, COUNT(*) as orders
      FROM orders
      WHERE payment_status = 'paid' AND created_at >= ?
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `).all(startDate.toISOString());

    const salesChart = {
      labels: salesData.map((item: any) => new Date(item.date).toLocaleDateString()),
      data: salesData.map((item: any) => item.revenue || 0),
    };

    // User Growth (last 12 months)
    const userGrowthData = db.prepare(`
      SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as new_users
      FROM users
      WHERE created_at >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY month ASC
    `).all();

    const userGrowth = {
      labels: userGrowthData.map((item: any) => item.month),
      data: userGrowthData.map((item: any) => item.new_users),
    };

    // Order Status Distribution
    const orderStatuses = db.prepare(`
      SELECT status, COUNT(*) as count
      FROM orders
      WHERE created_at >= ?
      GROUP BY status
    `).all(startDate.toISOString());

    const orderStatusDistribution = {
      pending: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
    };

    orderStatuses.forEach((status: any) => {
      if (status.status in orderStatusDistribution) {
        orderStatusDistribution[status.status as keyof typeof orderStatusDistribution] = status.count;
      }
    });

    // Revenue by Month (last 12 months)
    const revenueData = db.prepare(`
      SELECT strftime('%Y-%m', created_at) as month, SUM(total) as revenue
      FROM orders
      WHERE payment_status = 'paid' AND created_at >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY month ASC
    `).all();

    const revenueByMonth = {
      labels: revenueData.map((item: any) => item.month),
      data: revenueData.map((item: any) => item.revenue || 0),
    };

    const dashboardStats: DashboardStats = {
      overview: {
        totalUsers: totalUsers.count,
        totalOrders: totalOrders.count,
        totalRevenue: totalRevenue.sum || 0,
        totalProducts: totalProducts.count,
        pendingOrders: pendingOrders.count,
        lowStockProducts: lowStockProducts.count,
      },
      recentOrders: recentOrders.map((order: any) => ({
        id: order.id,
        orderNumber: order.order_number,
        customerName: `${order.first_name || ''} ${order.last_name || ''}`.trim(),
        customerEmail: order.email,
        total: order.total,
        status: order.status,
        paymentStatus: order.payment_status,
        createdAt: order.created_at,
      })),
      topProducts: topProducts.map((product: any) => ({
        name: product.name,
        price: product.price,
        totalSold: product.total_sold,
        revenue: product.revenue,
      })),
      salesChart,
      userGrowth,
      orderStatusDistribution,
      revenueByMonth,
    };

    return NextResponse.json({
      success: true,
      data: dashboardStats,
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}

// Analytics endpoint for specific metrics
async function analyticsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const db = getDatabase();
    const { searchParams } = new URL(request.url);
    const metric = searchParams.get('metric');
    const period = searchParams.get('period') || '30';
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    let data: any = {};

    switch (metric) {
      case 'conversion':
        // Calculate conversion rate (orders / unique visitors)
        const visitors = db.prepare(`
          SELECT COUNT(DISTINCT ip_address) as count 
          FROM audit_logs 
          WHERE action = 'PAGE_VIEW' AND created_at >= ?
        `).get(startDate.toISOString()) as any;
        
        const orders = db.prepare(`
          SELECT COUNT(*) as count 
          FROM orders 
          WHERE created_at >= ?
        `).get(startDate.toISOString()) as any;

        data = {
          visitors: visitors.count || 0,
          orders: orders.count || 0,
          conversionRate: visitors.count > 0 ? ((orders.count / visitors.count) * 100).toFixed(2) : 0,
        };
        break;

      case 'average-order-value':
        const avgOrder = db.prepare(`
          SELECT AVG(total) as avg, COUNT(*) as count
          FROM orders 
          WHERE payment_status = 'paid' AND created_at >= ?
        `).get(startDate.toISOString()) as any;

        data = {
          averageOrderValue: avgOrder.avg || 0,
          totalOrders: avgOrder.count || 0,
        };
        break;

      case 'customer-lifetime-value':
        const clv = db.prepare(`
          SELECT 
            AVG(customer_total) as avg_clv,
            COUNT(*) as total_customers
          FROM (
            SELECT user_id, SUM(total) as customer_total
            FROM orders 
            WHERE payment_status = 'paid'
            GROUP BY user_id
          ) customer_totals
        `).get() as any;

        data = {
          averageLifetimeValue: clv.avg_clv || 0,
          totalCustomers: clv.total_customers || 0,
        };
        break;

      case 'product-performance':
        const productPerf = db.prepare(`
          SELECT 
            p.name,
            p.price,
            SUM(oi.quantity) as units_sold,
            SUM(oi.total_price) as revenue,
            COUNT(DISTINCT o.user_id) as unique_buyers
          FROM products p
          JOIN order_items oi ON p.id = oi.product_id
          JOIN orders o ON oi.order_id = o.id
          WHERE o.payment_status = 'paid' AND o.created_at >= ?
          GROUP BY p.id
          ORDER BY revenue DESC
          LIMIT 20
        `).all(startDate.toISOString());

        data = { products: productPerf };
        break;

      case 'inventory-alerts':
        const lowStock = db.prepare(`
          SELECT name, sku, stock_quantity, low_stock_threshold
          FROM products 
          WHERE stock_quantity <= low_stock_threshold AND is_active = 1
          ORDER BY stock_quantity ASC
        `).all();

        const outOfStock = db.prepare(`
          SELECT name, sku, stock_quantity
          FROM products 
          WHERE stock_quantity = 0 AND is_active = 1
        `).all();

        data = {
          lowStock,
          outOfStock,
          totalAlerts: lowStock.length + outOfStock.length,
        };
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid metric requested' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      metric,
      period,
      data,
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

// Export handlers with security middleware
export const GET = withSecurity(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');
  
  if (endpoint === 'analytics') {
    return analyticsHandler(request);
  }
  
  return dashboardHandler(request);
}, {
  requireAuth: true,
  requiredRoles: ['admin', 'manager'],
  skipCSRF: true,
});

// Health check endpoint
export const HEAD = withSecurity(async (request: NextRequest) => {
  return new NextResponse(null, { status: 200 });
}, {
  requireAuth: true,
  requiredRoles: ['admin', 'manager'],
  skipCSRF: true,
  skipRateLimit: true,
});
