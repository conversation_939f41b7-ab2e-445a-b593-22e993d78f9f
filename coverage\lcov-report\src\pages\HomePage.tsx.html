
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/pages/HomePage.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/pages</a> HomePage.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/162</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/374</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/58</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/154</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a>
<a name='L1273'></a><a href='#L1273'>1273</a>
<a name='L1274'></a><a href='#L1274'>1274</a>
<a name='L1275'></a><a href='#L1275'>1275</a>
<a name='L1276'></a><a href='#L1276'>1276</a>
<a name='L1277'></a><a href='#L1277'>1277</a>
<a name='L1278'></a><a href='#L1278'>1278</a>
<a name='L1279'></a><a href='#L1279'>1279</a>
<a name='L1280'></a><a href='#L1280'>1280</a>
<a name='L1281'></a><a href='#L1281'>1281</a>
<a name='L1282'></a><a href='#L1282'>1282</a>
<a name='L1283'></a><a href='#L1283'>1283</a>
<a name='L1284'></a><a href='#L1284'>1284</a>
<a name='L1285'></a><a href='#L1285'>1285</a>
<a name='L1286'></a><a href='#L1286'>1286</a>
<a name='L1287'></a><a href='#L1287'>1287</a>
<a name='L1288'></a><a href='#L1288'>1288</a>
<a name='L1289'></a><a href='#L1289'>1289</a>
<a name='L1290'></a><a href='#L1290'>1290</a>
<a name='L1291'></a><a href='#L1291'>1291</a>
<a name='L1292'></a><a href='#L1292'>1292</a>
<a name='L1293'></a><a href='#L1293'>1293</a>
<a name='L1294'></a><a href='#L1294'>1294</a>
<a name='L1295'></a><a href='#L1295'>1295</a>
<a name='L1296'></a><a href='#L1296'>1296</a>
<a name='L1297'></a><a href='#L1297'>1297</a>
<a name='L1298'></a><a href='#L1298'>1298</a>
<a name='L1299'></a><a href='#L1299'>1299</a>
<a name='L1300'></a><a href='#L1300'>1300</a>
<a name='L1301'></a><a href='#L1301'>1301</a>
<a name='L1302'></a><a href='#L1302'>1302</a>
<a name='L1303'></a><a href='#L1303'>1303</a>
<a name='L1304'></a><a href='#L1304'>1304</a>
<a name='L1305'></a><a href='#L1305'>1305</a>
<a name='L1306'></a><a href='#L1306'>1306</a>
<a name='L1307'></a><a href='#L1307'>1307</a>
<a name='L1308'></a><a href='#L1308'>1308</a>
<a name='L1309'></a><a href='#L1309'>1309</a>
<a name='L1310'></a><a href='#L1310'>1310</a>
<a name='L1311'></a><a href='#L1311'>1311</a>
<a name='L1312'></a><a href='#L1312'>1312</a>
<a name='L1313'></a><a href='#L1313'>1313</a>
<a name='L1314'></a><a href='#L1314'>1314</a>
<a name='L1315'></a><a href='#L1315'>1315</a>
<a name='L1316'></a><a href='#L1316'>1316</a>
<a name='L1317'></a><a href='#L1317'>1317</a>
<a name='L1318'></a><a href='#L1318'>1318</a>
<a name='L1319'></a><a href='#L1319'>1319</a>
<a name='L1320'></a><a href='#L1320'>1320</a>
<a name='L1321'></a><a href='#L1321'>1321</a>
<a name='L1322'></a><a href='#L1322'>1322</a>
<a name='L1323'></a><a href='#L1323'>1323</a>
<a name='L1324'></a><a href='#L1324'>1324</a>
<a name='L1325'></a><a href='#L1325'>1325</a>
<a name='L1326'></a><a href='#L1326'>1326</a>
<a name='L1327'></a><a href='#L1327'>1327</a>
<a name='L1328'></a><a href='#L1328'>1328</a>
<a name='L1329'></a><a href='#L1329'>1329</a>
<a name='L1330'></a><a href='#L1330'>1330</a>
<a name='L1331'></a><a href='#L1331'>1331</a>
<a name='L1332'></a><a href='#L1332'>1332</a>
<a name='L1333'></a><a href='#L1333'>1333</a>
<a name='L1334'></a><a href='#L1334'>1334</a>
<a name='L1335'></a><a href='#L1335'>1335</a>
<a name='L1336'></a><a href='#L1336'>1336</a>
<a name='L1337'></a><a href='#L1337'>1337</a>
<a name='L1338'></a><a href='#L1338'>1338</a>
<a name='L1339'></a><a href='#L1339'>1339</a>
<a name='L1340'></a><a href='#L1340'>1340</a>
<a name='L1341'></a><a href='#L1341'>1341</a>
<a name='L1342'></a><a href='#L1342'>1342</a>
<a name='L1343'></a><a href='#L1343'>1343</a>
<a name='L1344'></a><a href='#L1344'>1344</a>
<a name='L1345'></a><a href='#L1345'>1345</a>
<a name='L1346'></a><a href='#L1346'>1346</a>
<a name='L1347'></a><a href='#L1347'>1347</a>
<a name='L1348'></a><a href='#L1348'>1348</a>
<a name='L1349'></a><a href='#L1349'>1349</a>
<a name='L1350'></a><a href='#L1350'>1350</a>
<a name='L1351'></a><a href='#L1351'>1351</a>
<a name='L1352'></a><a href='#L1352'>1352</a>
<a name='L1353'></a><a href='#L1353'>1353</a>
<a name='L1354'></a><a href='#L1354'>1354</a>
<a name='L1355'></a><a href='#L1355'>1355</a>
<a name='L1356'></a><a href='#L1356'>1356</a>
<a name='L1357'></a><a href='#L1357'>1357</a>
<a name='L1358'></a><a href='#L1358'>1358</a>
<a name='L1359'></a><a href='#L1359'>1359</a>
<a name='L1360'></a><a href='#L1360'>1360</a>
<a name='L1361'></a><a href='#L1361'>1361</a>
<a name='L1362'></a><a href='#L1362'>1362</a>
<a name='L1363'></a><a href='#L1363'>1363</a>
<a name='L1364'></a><a href='#L1364'>1364</a>
<a name='L1365'></a><a href='#L1365'>1365</a>
<a name='L1366'></a><a href='#L1366'>1366</a>
<a name='L1367'></a><a href='#L1367'>1367</a>
<a name='L1368'></a><a href='#L1368'>1368</a>
<a name='L1369'></a><a href='#L1369'>1369</a>
<a name='L1370'></a><a href='#L1370'>1370</a>
<a name='L1371'></a><a href='#L1371'>1371</a>
<a name='L1372'></a><a href='#L1372'>1372</a>
<a name='L1373'></a><a href='#L1373'>1373</a>
<a name='L1374'></a><a href='#L1374'>1374</a>
<a name='L1375'></a><a href='#L1375'>1375</a>
<a name='L1376'></a><a href='#L1376'>1376</a>
<a name='L1377'></a><a href='#L1377'>1377</a>
<a name='L1378'></a><a href='#L1378'>1378</a>
<a name='L1379'></a><a href='#L1379'>1379</a>
<a name='L1380'></a><a href='#L1380'>1380</a>
<a name='L1381'></a><a href='#L1381'>1381</a>
<a name='L1382'></a><a href='#L1382'>1382</a>
<a name='L1383'></a><a href='#L1383'>1383</a>
<a name='L1384'></a><a href='#L1384'>1384</a>
<a name='L1385'></a><a href='#L1385'>1385</a>
<a name='L1386'></a><a href='#L1386'>1386</a>
<a name='L1387'></a><a href='#L1387'>1387</a>
<a name='L1388'></a><a href='#L1388'>1388</a>
<a name='L1389'></a><a href='#L1389'>1389</a>
<a name='L1390'></a><a href='#L1390'>1390</a>
<a name='L1391'></a><a href='#L1391'>1391</a>
<a name='L1392'></a><a href='#L1392'>1392</a>
<a name='L1393'></a><a href='#L1393'>1393</a>
<a name='L1394'></a><a href='#L1394'>1394</a>
<a name='L1395'></a><a href='#L1395'>1395</a>
<a name='L1396'></a><a href='#L1396'>1396</a>
<a name='L1397'></a><a href='#L1397'>1397</a>
<a name='L1398'></a><a href='#L1398'>1398</a>
<a name='L1399'></a><a href='#L1399'>1399</a>
<a name='L1400'></a><a href='#L1400'>1400</a>
<a name='L1401'></a><a href='#L1401'>1401</a>
<a name='L1402'></a><a href='#L1402'>1402</a>
<a name='L1403'></a><a href='#L1403'>1403</a>
<a name='L1404'></a><a href='#L1404'>1404</a>
<a name='L1405'></a><a href='#L1405'>1405</a>
<a name='L1406'></a><a href='#L1406'>1406</a>
<a name='L1407'></a><a href='#L1407'>1407</a>
<a name='L1408'></a><a href='#L1408'>1408</a>
<a name='L1409'></a><a href='#L1409'>1409</a>
<a name='L1410'></a><a href='#L1410'>1410</a>
<a name='L1411'></a><a href='#L1411'>1411</a>
<a name='L1412'></a><a href='#L1412'>1412</a>
<a name='L1413'></a><a href='#L1413'>1413</a>
<a name='L1414'></a><a href='#L1414'>1414</a>
<a name='L1415'></a><a href='#L1415'>1415</a>
<a name='L1416'></a><a href='#L1416'>1416</a>
<a name='L1417'></a><a href='#L1417'>1417</a>
<a name='L1418'></a><a href='#L1418'>1418</a>
<a name='L1419'></a><a href='#L1419'>1419</a>
<a name='L1420'></a><a href='#L1420'>1420</a>
<a name='L1421'></a><a href='#L1421'>1421</a>
<a name='L1422'></a><a href='#L1422'>1422</a>
<a name='L1423'></a><a href='#L1423'>1423</a>
<a name='L1424'></a><a href='#L1424'>1424</a>
<a name='L1425'></a><a href='#L1425'>1425</a>
<a name='L1426'></a><a href='#L1426'>1426</a>
<a name='L1427'></a><a href='#L1427'>1427</a>
<a name='L1428'></a><a href='#L1428'>1428</a>
<a name='L1429'></a><a href='#L1429'>1429</a>
<a name='L1430'></a><a href='#L1430'>1430</a>
<a name='L1431'></a><a href='#L1431'>1431</a>
<a name='L1432'></a><a href='#L1432'>1432</a>
<a name='L1433'></a><a href='#L1433'>1433</a>
<a name='L1434'></a><a href='#L1434'>1434</a>
<a name='L1435'></a><a href='#L1435'>1435</a>
<a name='L1436'></a><a href='#L1436'>1436</a>
<a name='L1437'></a><a href='#L1437'>1437</a>
<a name='L1438'></a><a href='#L1438'>1438</a>
<a name='L1439'></a><a href='#L1439'>1439</a>
<a name='L1440'></a><a href='#L1440'>1440</a>
<a name='L1441'></a><a href='#L1441'>1441</a>
<a name='L1442'></a><a href='#L1442'>1442</a>
<a name='L1443'></a><a href='#L1443'>1443</a>
<a name='L1444'></a><a href='#L1444'>1444</a>
<a name='L1445'></a><a href='#L1445'>1445</a>
<a name='L1446'></a><a href='#L1446'>1446</a>
<a name='L1447'></a><a href='#L1447'>1447</a>
<a name='L1448'></a><a href='#L1448'>1448</a>
<a name='L1449'></a><a href='#L1449'>1449</a>
<a name='L1450'></a><a href='#L1450'>1450</a>
<a name='L1451'></a><a href='#L1451'>1451</a>
<a name='L1452'></a><a href='#L1452'>1452</a>
<a name='L1453'></a><a href='#L1453'>1453</a>
<a name='L1454'></a><a href='#L1454'>1454</a>
<a name='L1455'></a><a href='#L1455'>1455</a>
<a name='L1456'></a><a href='#L1456'>1456</a>
<a name='L1457'></a><a href='#L1457'>1457</a>
<a name='L1458'></a><a href='#L1458'>1458</a>
<a name='L1459'></a><a href='#L1459'>1459</a>
<a name='L1460'></a><a href='#L1460'>1460</a>
<a name='L1461'></a><a href='#L1461'>1461</a>
<a name='L1462'></a><a href='#L1462'>1462</a>
<a name='L1463'></a><a href='#L1463'>1463</a>
<a name='L1464'></a><a href='#L1464'>1464</a>
<a name='L1465'></a><a href='#L1465'>1465</a>
<a name='L1466'></a><a href='#L1466'>1466</a>
<a name='L1467'></a><a href='#L1467'>1467</a>
<a name='L1468'></a><a href='#L1468'>1468</a>
<a name='L1469'></a><a href='#L1469'>1469</a>
<a name='L1470'></a><a href='#L1470'>1470</a>
<a name='L1471'></a><a href='#L1471'>1471</a>
<a name='L1472'></a><a href='#L1472'>1472</a>
<a name='L1473'></a><a href='#L1473'>1473</a>
<a name='L1474'></a><a href='#L1474'>1474</a>
<a name='L1475'></a><a href='#L1475'>1475</a>
<a name='L1476'></a><a href='#L1476'>1476</a>
<a name='L1477'></a><a href='#L1477'>1477</a>
<a name='L1478'></a><a href='#L1478'>1478</a>
<a name='L1479'></a><a href='#L1479'>1479</a>
<a name='L1480'></a><a href='#L1480'>1480</a>
<a name='L1481'></a><a href='#L1481'>1481</a>
<a name='L1482'></a><a href='#L1482'>1482</a>
<a name='L1483'></a><a href='#L1483'>1483</a>
<a name='L1484'></a><a href='#L1484'>1484</a>
<a name='L1485'></a><a href='#L1485'>1485</a>
<a name='L1486'></a><a href='#L1486'>1486</a>
<a name='L1487'></a><a href='#L1487'>1487</a>
<a name='L1488'></a><a href='#L1488'>1488</a>
<a name='L1489'></a><a href='#L1489'>1489</a>
<a name='L1490'></a><a href='#L1490'>1490</a>
<a name='L1491'></a><a href='#L1491'>1491</a>
<a name='L1492'></a><a href='#L1492'>1492</a>
<a name='L1493'></a><a href='#L1493'>1493</a>
<a name='L1494'></a><a href='#L1494'>1494</a>
<a name='L1495'></a><a href='#L1495'>1495</a>
<a name='L1496'></a><a href='#L1496'>1496</a>
<a name='L1497'></a><a href='#L1497'>1497</a>
<a name='L1498'></a><a href='#L1498'>1498</a>
<a name='L1499'></a><a href='#L1499'>1499</a>
<a name='L1500'></a><a href='#L1500'>1500</a>
<a name='L1501'></a><a href='#L1501'>1501</a>
<a name='L1502'></a><a href='#L1502'>1502</a>
<a name='L1503'></a><a href='#L1503'>1503</a>
<a name='L1504'></a><a href='#L1504'>1504</a>
<a name='L1505'></a><a href='#L1505'>1505</a>
<a name='L1506'></a><a href='#L1506'>1506</a>
<a name='L1507'></a><a href='#L1507'>1507</a>
<a name='L1508'></a><a href='#L1508'>1508</a>
<a name='L1509'></a><a href='#L1509'>1509</a>
<a name='L1510'></a><a href='#L1510'>1510</a>
<a name='L1511'></a><a href='#L1511'>1511</a>
<a name='L1512'></a><a href='#L1512'>1512</a>
<a name='L1513'></a><a href='#L1513'>1513</a>
<a name='L1514'></a><a href='#L1514'>1514</a>
<a name='L1515'></a><a href='#L1515'>1515</a>
<a name='L1516'></a><a href='#L1516'>1516</a>
<a name='L1517'></a><a href='#L1517'>1517</a>
<a name='L1518'></a><a href='#L1518'>1518</a>
<a name='L1519'></a><a href='#L1519'>1519</a>
<a name='L1520'></a><a href='#L1520'>1520</a>
<a name='L1521'></a><a href='#L1521'>1521</a>
<a name='L1522'></a><a href='#L1522'>1522</a>
<a name='L1523'></a><a href='#L1523'>1523</a>
<a name='L1524'></a><a href='#L1524'>1524</a>
<a name='L1525'></a><a href='#L1525'>1525</a>
<a name='L1526'></a><a href='#L1526'>1526</a>
<a name='L1527'></a><a href='#L1527'>1527</a>
<a name='L1528'></a><a href='#L1528'>1528</a>
<a name='L1529'></a><a href='#L1529'>1529</a>
<a name='L1530'></a><a href='#L1530'>1530</a>
<a name='L1531'></a><a href='#L1531'>1531</a>
<a name='L1532'></a><a href='#L1532'>1532</a>
<a name='L1533'></a><a href='#L1533'>1533</a>
<a name='L1534'></a><a href='#L1534'>1534</a>
<a name='L1535'></a><a href='#L1535'>1535</a>
<a name='L1536'></a><a href='#L1536'>1536</a>
<a name='L1537'></a><a href='#L1537'>1537</a>
<a name='L1538'></a><a href='#L1538'>1538</a>
<a name='L1539'></a><a href='#L1539'>1539</a>
<a name='L1540'></a><a href='#L1540'>1540</a>
<a name='L1541'></a><a href='#L1541'>1541</a>
<a name='L1542'></a><a href='#L1542'>1542</a>
<a name='L1543'></a><a href='#L1543'>1543</a>
<a name='L1544'></a><a href='#L1544'>1544</a>
<a name='L1545'></a><a href='#L1545'>1545</a>
<a name='L1546'></a><a href='#L1546'>1546</a>
<a name='L1547'></a><a href='#L1547'>1547</a>
<a name='L1548'></a><a href='#L1548'>1548</a>
<a name='L1549'></a><a href='#L1549'>1549</a>
<a name='L1550'></a><a href='#L1550'>1550</a>
<a name='L1551'></a><a href='#L1551'>1551</a>
<a name='L1552'></a><a href='#L1552'>1552</a>
<a name='L1553'></a><a href='#L1553'>1553</a>
<a name='L1554'></a><a href='#L1554'>1554</a>
<a name='L1555'></a><a href='#L1555'>1555</a>
<a name='L1556'></a><a href='#L1556'>1556</a>
<a name='L1557'></a><a href='#L1557'>1557</a>
<a name='L1558'></a><a href='#L1558'>1558</a>
<a name='L1559'></a><a href='#L1559'>1559</a>
<a name='L1560'></a><a href='#L1560'>1560</a>
<a name='L1561'></a><a href='#L1561'>1561</a>
<a name='L1562'></a><a href='#L1562'>1562</a>
<a name='L1563'></a><a href='#L1563'>1563</a>
<a name='L1564'></a><a href='#L1564'>1564</a>
<a name='L1565'></a><a href='#L1565'>1565</a>
<a name='L1566'></a><a href='#L1566'>1566</a>
<a name='L1567'></a><a href='#L1567'>1567</a>
<a name='L1568'></a><a href='#L1568'>1568</a>
<a name='L1569'></a><a href='#L1569'>1569</a>
<a name='L1570'></a><a href='#L1570'>1570</a>
<a name='L1571'></a><a href='#L1571'>1571</a>
<a name='L1572'></a><a href='#L1572'>1572</a>
<a name='L1573'></a><a href='#L1573'>1573</a>
<a name='L1574'></a><a href='#L1574'>1574</a>
<a name='L1575'></a><a href='#L1575'>1575</a>
<a name='L1576'></a><a href='#L1576'>1576</a>
<a name='L1577'></a><a href='#L1577'>1577</a>
<a name='L1578'></a><a href='#L1578'>1578</a>
<a name='L1579'></a><a href='#L1579'>1579</a>
<a name='L1580'></a><a href='#L1580'>1580</a>
<a name='L1581'></a><a href='#L1581'>1581</a>
<a name='L1582'></a><a href='#L1582'>1582</a>
<a name='L1583'></a><a href='#L1583'>1583</a>
<a name='L1584'></a><a href='#L1584'>1584</a>
<a name='L1585'></a><a href='#L1585'>1585</a>
<a name='L1586'></a><a href='#L1586'>1586</a>
<a name='L1587'></a><a href='#L1587'>1587</a>
<a name='L1588'></a><a href='#L1588'>1588</a>
<a name='L1589'></a><a href='#L1589'>1589</a>
<a name='L1590'></a><a href='#L1590'>1590</a>
<a name='L1591'></a><a href='#L1591'>1591</a>
<a name='L1592'></a><a href='#L1592'>1592</a>
<a name='L1593'></a><a href='#L1593'>1593</a>
<a name='L1594'></a><a href='#L1594'>1594</a>
<a name='L1595'></a><a href='#L1595'>1595</a>
<a name='L1596'></a><a href='#L1596'>1596</a>
<a name='L1597'></a><a href='#L1597'>1597</a>
<a name='L1598'></a><a href='#L1598'>1598</a>
<a name='L1599'></a><a href='#L1599'>1599</a>
<a name='L1600'></a><a href='#L1600'>1600</a>
<a name='L1601'></a><a href='#L1601'>1601</a>
<a name='L1602'></a><a href='#L1602'>1602</a>
<a name='L1603'></a><a href='#L1603'>1603</a>
<a name='L1604'></a><a href='#L1604'>1604</a>
<a name='L1605'></a><a href='#L1605'>1605</a>
<a name='L1606'></a><a href='#L1606'>1606</a>
<a name='L1607'></a><a href='#L1607'>1607</a>
<a name='L1608'></a><a href='#L1608'>1608</a>
<a name='L1609'></a><a href='#L1609'>1609</a>
<a name='L1610'></a><a href='#L1610'>1610</a>
<a name='L1611'></a><a href='#L1611'>1611</a>
<a name='L1612'></a><a href='#L1612'>1612</a>
<a name='L1613'></a><a href='#L1613'>1613</a>
<a name='L1614'></a><a href='#L1614'>1614</a>
<a name='L1615'></a><a href='#L1615'>1615</a>
<a name='L1616'></a><a href='#L1616'>1616</a>
<a name='L1617'></a><a href='#L1617'>1617</a>
<a name='L1618'></a><a href='#L1618'>1618</a>
<a name='L1619'></a><a href='#L1619'>1619</a>
<a name='L1620'></a><a href='#L1620'>1620</a>
<a name='L1621'></a><a href='#L1621'>1621</a>
<a name='L1622'></a><a href='#L1622'>1622</a>
<a name='L1623'></a><a href='#L1623'>1623</a>
<a name='L1624'></a><a href='#L1624'>1624</a>
<a name='L1625'></a><a href='#L1625'>1625</a>
<a name='L1626'></a><a href='#L1626'>1626</a>
<a name='L1627'></a><a href='#L1627'>1627</a>
<a name='L1628'></a><a href='#L1628'>1628</a>
<a name='L1629'></a><a href='#L1629'>1629</a>
<a name='L1630'></a><a href='#L1630'>1630</a>
<a name='L1631'></a><a href='#L1631'>1631</a>
<a name='L1632'></a><a href='#L1632'>1632</a>
<a name='L1633'></a><a href='#L1633'>1633</a>
<a name='L1634'></a><a href='#L1634'>1634</a>
<a name='L1635'></a><a href='#L1635'>1635</a>
<a name='L1636'></a><a href='#L1636'>1636</a>
<a name='L1637'></a><a href='#L1637'>1637</a>
<a name='L1638'></a><a href='#L1638'>1638</a>
<a name='L1639'></a><a href='#L1639'>1639</a>
<a name='L1640'></a><a href='#L1640'>1640</a>
<a name='L1641'></a><a href='#L1641'>1641</a>
<a name='L1642'></a><a href='#L1642'>1642</a>
<a name='L1643'></a><a href='#L1643'>1643</a>
<a name='L1644'></a><a href='#L1644'>1644</a>
<a name='L1645'></a><a href='#L1645'>1645</a>
<a name='L1646'></a><a href='#L1646'>1646</a>
<a name='L1647'></a><a href='#L1647'>1647</a>
<a name='L1648'></a><a href='#L1648'>1648</a>
<a name='L1649'></a><a href='#L1649'>1649</a>
<a name='L1650'></a><a href='#L1650'>1650</a>
<a name='L1651'></a><a href='#L1651'>1651</a>
<a name='L1652'></a><a href='#L1652'>1652</a>
<a name='L1653'></a><a href='#L1653'>1653</a>
<a name='L1654'></a><a href='#L1654'>1654</a>
<a name='L1655'></a><a href='#L1655'>1655</a>
<a name='L1656'></a><a href='#L1656'>1656</a>
<a name='L1657'></a><a href='#L1657'>1657</a>
<a name='L1658'></a><a href='#L1658'>1658</a>
<a name='L1659'></a><a href='#L1659'>1659</a>
<a name='L1660'></a><a href='#L1660'>1660</a>
<a name='L1661'></a><a href='#L1661'>1661</a>
<a name='L1662'></a><a href='#L1662'>1662</a>
<a name='L1663'></a><a href='#L1663'>1663</a>
<a name='L1664'></a><a href='#L1664'>1664</a>
<a name='L1665'></a><a href='#L1665'>1665</a>
<a name='L1666'></a><a href='#L1666'>1666</a>
<a name='L1667'></a><a href='#L1667'>1667</a>
<a name='L1668'></a><a href='#L1668'>1668</a>
<a name='L1669'></a><a href='#L1669'>1669</a>
<a name='L1670'></a><a href='#L1670'>1670</a>
<a name='L1671'></a><a href='#L1671'>1671</a>
<a name='L1672'></a><a href='#L1672'>1672</a>
<a name='L1673'></a><a href='#L1673'>1673</a>
<a name='L1674'></a><a href='#L1674'>1674</a>
<a name='L1675'></a><a href='#L1675'>1675</a>
<a name='L1676'></a><a href='#L1676'>1676</a>
<a name='L1677'></a><a href='#L1677'>1677</a>
<a name='L1678'></a><a href='#L1678'>1678</a>
<a name='L1679'></a><a href='#L1679'>1679</a>
<a name='L1680'></a><a href='#L1680'>1680</a>
<a name='L1681'></a><a href='#L1681'>1681</a>
<a name='L1682'></a><a href='#L1682'>1682</a>
<a name='L1683'></a><a href='#L1683'>1683</a>
<a name='L1684'></a><a href='#L1684'>1684</a>
<a name='L1685'></a><a href='#L1685'>1685</a>
<a name='L1686'></a><a href='#L1686'>1686</a>
<a name='L1687'></a><a href='#L1687'>1687</a>
<a name='L1688'></a><a href='#L1688'>1688</a>
<a name='L1689'></a><a href='#L1689'>1689</a>
<a name='L1690'></a><a href='#L1690'>1690</a>
<a name='L1691'></a><a href='#L1691'>1691</a>
<a name='L1692'></a><a href='#L1692'>1692</a>
<a name='L1693'></a><a href='#L1693'>1693</a>
<a name='L1694'></a><a href='#L1694'>1694</a>
<a name='L1695'></a><a href='#L1695'>1695</a>
<a name='L1696'></a><a href='#L1696'>1696</a>
<a name='L1697'></a><a href='#L1697'>1697</a>
<a name='L1698'></a><a href='#L1698'>1698</a>
<a name='L1699'></a><a href='#L1699'>1699</a>
<a name='L1700'></a><a href='#L1700'>1700</a>
<a name='L1701'></a><a href='#L1701'>1701</a>
<a name='L1702'></a><a href='#L1702'>1702</a>
<a name='L1703'></a><a href='#L1703'>1703</a>
<a name='L1704'></a><a href='#L1704'>1704</a>
<a name='L1705'></a><a href='#L1705'>1705</a>
<a name='L1706'></a><a href='#L1706'>1706</a>
<a name='L1707'></a><a href='#L1707'>1707</a>
<a name='L1708'></a><a href='#L1708'>1708</a>
<a name='L1709'></a><a href='#L1709'>1709</a>
<a name='L1710'></a><a href='#L1710'>1710</a>
<a name='L1711'></a><a href='#L1711'>1711</a>
<a name='L1712'></a><a href='#L1712'>1712</a>
<a name='L1713'></a><a href='#L1713'>1713</a>
<a name='L1714'></a><a href='#L1714'>1714</a>
<a name='L1715'></a><a href='#L1715'>1715</a>
<a name='L1716'></a><a href='#L1716'>1716</a>
<a name='L1717'></a><a href='#L1717'>1717</a>
<a name='L1718'></a><a href='#L1718'>1718</a>
<a name='L1719'></a><a href='#L1719'>1719</a>
<a name='L1720'></a><a href='#L1720'>1720</a>
<a name='L1721'></a><a href='#L1721'>1721</a>
<a name='L1722'></a><a href='#L1722'>1722</a>
<a name='L1723'></a><a href='#L1723'>1723</a>
<a name='L1724'></a><a href='#L1724'>1724</a>
<a name='L1725'></a><a href='#L1725'>1725</a>
<a name='L1726'></a><a href='#L1726'>1726</a>
<a name='L1727'></a><a href='#L1727'>1727</a>
<a name='L1728'></a><a href='#L1728'>1728</a>
<a name='L1729'></a><a href='#L1729'>1729</a>
<a name='L1730'></a><a href='#L1730'>1730</a>
<a name='L1731'></a><a href='#L1731'>1731</a>
<a name='L1732'></a><a href='#L1732'>1732</a>
<a name='L1733'></a><a href='#L1733'>1733</a>
<a name='L1734'></a><a href='#L1734'>1734</a>
<a name='L1735'></a><a href='#L1735'>1735</a>
<a name='L1736'></a><a href='#L1736'>1736</a>
<a name='L1737'></a><a href='#L1737'>1737</a>
<a name='L1738'></a><a href='#L1738'>1738</a>
<a name='L1739'></a><a href='#L1739'>1739</a>
<a name='L1740'></a><a href='#L1740'>1740</a>
<a name='L1741'></a><a href='#L1741'>1741</a>
<a name='L1742'></a><a href='#L1742'>1742</a>
<a name='L1743'></a><a href='#L1743'>1743</a>
<a name='L1744'></a><a href='#L1744'>1744</a>
<a name='L1745'></a><a href='#L1745'>1745</a>
<a name='L1746'></a><a href='#L1746'>1746</a>
<a name='L1747'></a><a href='#L1747'>1747</a>
<a name='L1748'></a><a href='#L1748'>1748</a>
<a name='L1749'></a><a href='#L1749'>1749</a>
<a name='L1750'></a><a href='#L1750'>1750</a>
<a name='L1751'></a><a href='#L1751'>1751</a>
<a name='L1752'></a><a href='#L1752'>1752</a>
<a name='L1753'></a><a href='#L1753'>1753</a>
<a name='L1754'></a><a href='#L1754'>1754</a>
<a name='L1755'></a><a href='#L1755'>1755</a>
<a name='L1756'></a><a href='#L1756'>1756</a>
<a name='L1757'></a><a href='#L1757'>1757</a>
<a name='L1758'></a><a href='#L1758'>1758</a>
<a name='L1759'></a><a href='#L1759'>1759</a>
<a name='L1760'></a><a href='#L1760'>1760</a>
<a name='L1761'></a><a href='#L1761'>1761</a>
<a name='L1762'></a><a href='#L1762'>1762</a>
<a name='L1763'></a><a href='#L1763'>1763</a>
<a name='L1764'></a><a href='#L1764'>1764</a>
<a name='L1765'></a><a href='#L1765'>1765</a>
<a name='L1766'></a><a href='#L1766'>1766</a>
<a name='L1767'></a><a href='#L1767'>1767</a>
<a name='L1768'></a><a href='#L1768'>1768</a>
<a name='L1769'></a><a href='#L1769'>1769</a>
<a name='L1770'></a><a href='#L1770'>1770</a>
<a name='L1771'></a><a href='#L1771'>1771</a>
<a name='L1772'></a><a href='#L1772'>1772</a>
<a name='L1773'></a><a href='#L1773'>1773</a>
<a name='L1774'></a><a href='#L1774'>1774</a>
<a name='L1775'></a><a href='#L1775'>1775</a>
<a name='L1776'></a><a href='#L1776'>1776</a>
<a name='L1777'></a><a href='#L1777'>1777</a>
<a name='L1778'></a><a href='#L1778'>1778</a>
<a name='L1779'></a><a href='#L1779'>1779</a>
<a name='L1780'></a><a href='#L1780'>1780</a>
<a name='L1781'></a><a href='#L1781'>1781</a>
<a name='L1782'></a><a href='#L1782'>1782</a>
<a name='L1783'></a><a href='#L1783'>1783</a>
<a name='L1784'></a><a href='#L1784'>1784</a>
<a name='L1785'></a><a href='#L1785'>1785</a>
<a name='L1786'></a><a href='#L1786'>1786</a>
<a name='L1787'></a><a href='#L1787'>1787</a>
<a name='L1788'></a><a href='#L1788'>1788</a>
<a name='L1789'></a><a href='#L1789'>1789</a>
<a name='L1790'></a><a href='#L1790'>1790</a>
<a name='L1791'></a><a href='#L1791'>1791</a>
<a name='L1792'></a><a href='#L1792'>1792</a>
<a name='L1793'></a><a href='#L1793'>1793</a>
<a name='L1794'></a><a href='#L1794'>1794</a>
<a name='L1795'></a><a href='#L1795'>1795</a>
<a name='L1796'></a><a href='#L1796'>1796</a>
<a name='L1797'></a><a href='#L1797'>1797</a>
<a name='L1798'></a><a href='#L1798'>1798</a>
<a name='L1799'></a><a href='#L1799'>1799</a>
<a name='L1800'></a><a href='#L1800'>1800</a>
<a name='L1801'></a><a href='#L1801'>1801</a>
<a name='L1802'></a><a href='#L1802'>1802</a>
<a name='L1803'></a><a href='#L1803'>1803</a>
<a name='L1804'></a><a href='#L1804'>1804</a>
<a name='L1805'></a><a href='#L1805'>1805</a>
<a name='L1806'></a><a href='#L1806'>1806</a>
<a name='L1807'></a><a href='#L1807'>1807</a>
<a name='L1808'></a><a href='#L1808'>1808</a>
<a name='L1809'></a><a href='#L1809'>1809</a>
<a name='L1810'></a><a href='#L1810'>1810</a>
<a name='L1811'></a><a href='#L1811'>1811</a>
<a name='L1812'></a><a href='#L1812'>1812</a>
<a name='L1813'></a><a href='#L1813'>1813</a>
<a name='L1814'></a><a href='#L1814'>1814</a>
<a name='L1815'></a><a href='#L1815'>1815</a>
<a name='L1816'></a><a href='#L1816'>1816</a>
<a name='L1817'></a><a href='#L1817'>1817</a>
<a name='L1818'></a><a href='#L1818'>1818</a>
<a name='L1819'></a><a href='#L1819'>1819</a>
<a name='L1820'></a><a href='#L1820'>1820</a>
<a name='L1821'></a><a href='#L1821'>1821</a>
<a name='L1822'></a><a href='#L1822'>1822</a>
<a name='L1823'></a><a href='#L1823'>1823</a>
<a name='L1824'></a><a href='#L1824'>1824</a>
<a name='L1825'></a><a href='#L1825'>1825</a>
<a name='L1826'></a><a href='#L1826'>1826</a>
<a name='L1827'></a><a href='#L1827'>1827</a>
<a name='L1828'></a><a href='#L1828'>1828</a>
<a name='L1829'></a><a href='#L1829'>1829</a>
<a name='L1830'></a><a href='#L1830'>1830</a>
<a name='L1831'></a><a href='#L1831'>1831</a>
<a name='L1832'></a><a href='#L1832'>1832</a>
<a name='L1833'></a><a href='#L1833'>1833</a>
<a name='L1834'></a><a href='#L1834'>1834</a>
<a name='L1835'></a><a href='#L1835'>1835</a>
<a name='L1836'></a><a href='#L1836'>1836</a>
<a name='L1837'></a><a href='#L1837'>1837</a>
<a name='L1838'></a><a href='#L1838'>1838</a>
<a name='L1839'></a><a href='#L1839'>1839</a>
<a name='L1840'></a><a href='#L1840'>1840</a>
<a name='L1841'></a><a href='#L1841'>1841</a>
<a name='L1842'></a><a href='#L1842'>1842</a>
<a name='L1843'></a><a href='#L1843'>1843</a>
<a name='L1844'></a><a href='#L1844'>1844</a>
<a name='L1845'></a><a href='#L1845'>1845</a>
<a name='L1846'></a><a href='#L1846'>1846</a>
<a name='L1847'></a><a href='#L1847'>1847</a>
<a name='L1848'></a><a href='#L1848'>1848</a>
<a name='L1849'></a><a href='#L1849'>1849</a>
<a name='L1850'></a><a href='#L1850'>1850</a>
<a name='L1851'></a><a href='#L1851'>1851</a>
<a name='L1852'></a><a href='#L1852'>1852</a>
<a name='L1853'></a><a href='#L1853'>1853</a>
<a name='L1854'></a><a href='#L1854'>1854</a>
<a name='L1855'></a><a href='#L1855'>1855</a>
<a name='L1856'></a><a href='#L1856'>1856</a>
<a name='L1857'></a><a href='#L1857'>1857</a>
<a name='L1858'></a><a href='#L1858'>1858</a>
<a name='L1859'></a><a href='#L1859'>1859</a>
<a name='L1860'></a><a href='#L1860'>1860</a>
<a name='L1861'></a><a href='#L1861'>1861</a>
<a name='L1862'></a><a href='#L1862'>1862</a>
<a name='L1863'></a><a href='#L1863'>1863</a>
<a name='L1864'></a><a href='#L1864'>1864</a>
<a name='L1865'></a><a href='#L1865'>1865</a>
<a name='L1866'></a><a href='#L1866'>1866</a>
<a name='L1867'></a><a href='#L1867'>1867</a>
<a name='L1868'></a><a href='#L1868'>1868</a>
<a name='L1869'></a><a href='#L1869'>1869</a>
<a name='L1870'></a><a href='#L1870'>1870</a>
<a name='L1871'></a><a href='#L1871'>1871</a>
<a name='L1872'></a><a href='#L1872'>1872</a>
<a name='L1873'></a><a href='#L1873'>1873</a>
<a name='L1874'></a><a href='#L1874'>1874</a>
<a name='L1875'></a><a href='#L1875'>1875</a>
<a name='L1876'></a><a href='#L1876'>1876</a>
<a name='L1877'></a><a href='#L1877'>1877</a>
<a name='L1878'></a><a href='#L1878'>1878</a>
<a name='L1879'></a><a href='#L1879'>1879</a>
<a name='L1880'></a><a href='#L1880'>1880</a>
<a name='L1881'></a><a href='#L1881'>1881</a>
<a name='L1882'></a><a href='#L1882'>1882</a>
<a name='L1883'></a><a href='#L1883'>1883</a>
<a name='L1884'></a><a href='#L1884'>1884</a>
<a name='L1885'></a><a href='#L1885'>1885</a>
<a name='L1886'></a><a href='#L1886'>1886</a>
<a name='L1887'></a><a href='#L1887'>1887</a>
<a name='L1888'></a><a href='#L1888'>1888</a>
<a name='L1889'></a><a href='#L1889'>1889</a>
<a name='L1890'></a><a href='#L1890'>1890</a>
<a name='L1891'></a><a href='#L1891'>1891</a>
<a name='L1892'></a><a href='#L1892'>1892</a>
<a name='L1893'></a><a href='#L1893'>1893</a>
<a name='L1894'></a><a href='#L1894'>1894</a>
<a name='L1895'></a><a href='#L1895'>1895</a>
<a name='L1896'></a><a href='#L1896'>1896</a>
<a name='L1897'></a><a href='#L1897'>1897</a>
<a name='L1898'></a><a href='#L1898'>1898</a>
<a name='L1899'></a><a href='#L1899'>1899</a>
<a name='L1900'></a><a href='#L1900'>1900</a>
<a name='L1901'></a><a href='#L1901'>1901</a>
<a name='L1902'></a><a href='#L1902'>1902</a>
<a name='L1903'></a><a href='#L1903'>1903</a>
<a name='L1904'></a><a href='#L1904'>1904</a>
<a name='L1905'></a><a href='#L1905'>1905</a>
<a name='L1906'></a><a href='#L1906'>1906</a>
<a name='L1907'></a><a href='#L1907'>1907</a>
<a name='L1908'></a><a href='#L1908'>1908</a>
<a name='L1909'></a><a href='#L1909'>1909</a>
<a name='L1910'></a><a href='#L1910'>1910</a>
<a name='L1911'></a><a href='#L1911'>1911</a>
<a name='L1912'></a><a href='#L1912'>1912</a>
<a name='L1913'></a><a href='#L1913'>1913</a>
<a name='L1914'></a><a href='#L1914'>1914</a>
<a name='L1915'></a><a href='#L1915'>1915</a>
<a name='L1916'></a><a href='#L1916'>1916</a>
<a name='L1917'></a><a href='#L1917'>1917</a>
<a name='L1918'></a><a href='#L1918'>1918</a>
<a name='L1919'></a><a href='#L1919'>1919</a>
<a name='L1920'></a><a href='#L1920'>1920</a>
<a name='L1921'></a><a href='#L1921'>1921</a>
<a name='L1922'></a><a href='#L1922'>1922</a>
<a name='L1923'></a><a href='#L1923'>1923</a>
<a name='L1924'></a><a href='#L1924'>1924</a>
<a name='L1925'></a><a href='#L1925'>1925</a>
<a name='L1926'></a><a href='#L1926'>1926</a>
<a name='L1927'></a><a href='#L1927'>1927</a>
<a name='L1928'></a><a href='#L1928'>1928</a>
<a name='L1929'></a><a href='#L1929'>1929</a>
<a name='L1930'></a><a href='#L1930'>1930</a>
<a name='L1931'></a><a href='#L1931'>1931</a>
<a name='L1932'></a><a href='#L1932'>1932</a>
<a name='L1933'></a><a href='#L1933'>1933</a>
<a name='L1934'></a><a href='#L1934'>1934</a>
<a name='L1935'></a><a href='#L1935'>1935</a>
<a name='L1936'></a><a href='#L1936'>1936</a>
<a name='L1937'></a><a href='#L1937'>1937</a>
<a name='L1938'></a><a href='#L1938'>1938</a>
<a name='L1939'></a><a href='#L1939'>1939</a>
<a name='L1940'></a><a href='#L1940'>1940</a>
<a name='L1941'></a><a href='#L1941'>1941</a>
<a name='L1942'></a><a href='#L1942'>1942</a>
<a name='L1943'></a><a href='#L1943'>1943</a>
<a name='L1944'></a><a href='#L1944'>1944</a>
<a name='L1945'></a><a href='#L1945'>1945</a>
<a name='L1946'></a><a href='#L1946'>1946</a>
<a name='L1947'></a><a href='#L1947'>1947</a>
<a name='L1948'></a><a href='#L1948'>1948</a>
<a name='L1949'></a><a href='#L1949'>1949</a>
<a name='L1950'></a><a href='#L1950'>1950</a>
<a name='L1951'></a><a href='#L1951'>1951</a>
<a name='L1952'></a><a href='#L1952'>1952</a>
<a name='L1953'></a><a href='#L1953'>1953</a>
<a name='L1954'></a><a href='#L1954'>1954</a>
<a name='L1955'></a><a href='#L1955'>1955</a>
<a name='L1956'></a><a href='#L1956'>1956</a>
<a name='L1957'></a><a href='#L1957'>1957</a>
<a name='L1958'></a><a href='#L1958'>1958</a>
<a name='L1959'></a><a href='#L1959'>1959</a>
<a name='L1960'></a><a href='#L1960'>1960</a>
<a name='L1961'></a><a href='#L1961'>1961</a>
<a name='L1962'></a><a href='#L1962'>1962</a>
<a name='L1963'></a><a href='#L1963'>1963</a>
<a name='L1964'></a><a href='#L1964'>1964</a>
<a name='L1965'></a><a href='#L1965'>1965</a>
<a name='L1966'></a><a href='#L1966'>1966</a>
<a name='L1967'></a><a href='#L1967'>1967</a>
<a name='L1968'></a><a href='#L1968'>1968</a>
<a name='L1969'></a><a href='#L1969'>1969</a>
<a name='L1970'></a><a href='#L1970'>1970</a>
<a name='L1971'></a><a href='#L1971'>1971</a>
<a name='L1972'></a><a href='#L1972'>1972</a>
<a name='L1973'></a><a href='#L1973'>1973</a>
<a name='L1974'></a><a href='#L1974'>1974</a>
<a name='L1975'></a><a href='#L1975'>1975</a>
<a name='L1976'></a><a href='#L1976'>1976</a>
<a name='L1977'></a><a href='#L1977'>1977</a>
<a name='L1978'></a><a href='#L1978'>1978</a>
<a name='L1979'></a><a href='#L1979'>1979</a>
<a name='L1980'></a><a href='#L1980'>1980</a>
<a name='L1981'></a><a href='#L1981'>1981</a>
<a name='L1982'></a><a href='#L1982'>1982</a>
<a name='L1983'></a><a href='#L1983'>1983</a>
<a name='L1984'></a><a href='#L1984'>1984</a>
<a name='L1985'></a><a href='#L1985'>1985</a>
<a name='L1986'></a><a href='#L1986'>1986</a>
<a name='L1987'></a><a href='#L1987'>1987</a>
<a name='L1988'></a><a href='#L1988'>1988</a>
<a name='L1989'></a><a href='#L1989'>1989</a>
<a name='L1990'></a><a href='#L1990'>1990</a>
<a name='L1991'></a><a href='#L1991'>1991</a>
<a name='L1992'></a><a href='#L1992'>1992</a>
<a name='L1993'></a><a href='#L1993'>1993</a>
<a name='L1994'></a><a href='#L1994'>1994</a>
<a name='L1995'></a><a href='#L1995'>1995</a>
<a name='L1996'></a><a href='#L1996'>1996</a>
<a name='L1997'></a><a href='#L1997'>1997</a>
<a name='L1998'></a><a href='#L1998'>1998</a>
<a name='L1999'></a><a href='#L1999'>1999</a>
<a name='L2000'></a><a href='#L2000'>2000</a>
<a name='L2001'></a><a href='#L2001'>2001</a>
<a name='L2002'></a><a href='#L2002'>2002</a>
<a name='L2003'></a><a href='#L2003'>2003</a>
<a name='L2004'></a><a href='#L2004'>2004</a>
<a name='L2005'></a><a href='#L2005'>2005</a>
<a name='L2006'></a><a href='#L2006'>2006</a>
<a name='L2007'></a><a href='#L2007'>2007</a>
<a name='L2008'></a><a href='#L2008'>2008</a>
<a name='L2009'></a><a href='#L2009'>2009</a>
<a name='L2010'></a><a href='#L2010'>2010</a>
<a name='L2011'></a><a href='#L2011'>2011</a>
<a name='L2012'></a><a href='#L2012'>2012</a>
<a name='L2013'></a><a href='#L2013'>2013</a>
<a name='L2014'></a><a href='#L2014'>2014</a>
<a name='L2015'></a><a href='#L2015'>2015</a>
<a name='L2016'></a><a href='#L2016'>2016</a>
<a name='L2017'></a><a href='#L2017'>2017</a>
<a name='L2018'></a><a href='#L2018'>2018</a>
<a name='L2019'></a><a href='#L2019'>2019</a>
<a name='L2020'></a><a href='#L2020'>2020</a>
<a name='L2021'></a><a href='#L2021'>2021</a>
<a name='L2022'></a><a href='#L2022'>2022</a>
<a name='L2023'></a><a href='#L2023'>2023</a>
<a name='L2024'></a><a href='#L2024'>2024</a>
<a name='L2025'></a><a href='#L2025'>2025</a>
<a name='L2026'></a><a href='#L2026'>2026</a>
<a name='L2027'></a><a href='#L2027'>2027</a>
<a name='L2028'></a><a href='#L2028'>2028</a>
<a name='L2029'></a><a href='#L2029'>2029</a>
<a name='L2030'></a><a href='#L2030'>2030</a>
<a name='L2031'></a><a href='#L2031'>2031</a>
<a name='L2032'></a><a href='#L2032'>2032</a>
<a name='L2033'></a><a href='#L2033'>2033</a>
<a name='L2034'></a><a href='#L2034'>2034</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use client';
&nbsp;
import { Suspense, lazy } from 'react';
import Link from <span class="cstat-no" title="statement not covered" >'next/link';</span>
import { useState, useEffect } from <span class="cstat-no" title="statement not covered" >'react';</span>
import { ArrowRight, ShoppingCart, Users, Package, Truck, FileText, Tag, BarChart as ChartBar, Globe, Clock, Shield, CheckCircle, ArrowUpRight, Heart, Star, Eye, Settings, Gauge, PenTool as Tool, Calendar, FileCheck, Search, Building2, Play, MapPin, X, Send, Box, Factory, Bell } from <span class="cstat-no" title="statement not covered" >'lucide-react';</span>
import { Button } from <span class="cstat-no" title="statement not covered" >'../components/ui/Button';</span>
import { HeroButton } from <span class="cstat-no" title="statement not covered" >'../components/ui/HeroButton';</span>
import { Card, CardContent, CardHeader, CardTitle } from <span class="cstat-no" title="statement not covered" >'../components/ui/Card';</span>
import { LazyImage } from '../components/ui/LazyImage';
import { EnhancedImage } from <span class="cstat-no" title="statement not covered" >'../components/ui/EnhancedImage';</span>
import { useCartStore } from <span class="cstat-no" title="statement not covered" >'../stores/cartStore';</span>
import { useWishlistStore } from <span class="cstat-no" title="statement not covered" >'../stores/wishlistStore';</span>
import { useAuthStore } from <span class="cstat-no" title="statement not covered" >'../stores/authStore';</span>
import { useLanguageStore } from <span class="cstat-no" title="statement not covered" >'../stores/languageStore';</span>
import { useTranslation } from <span class="cstat-no" title="statement not covered" >'../translations';</span>
import { useAuthModalStore } from <span class="cstat-no" title="statement not covered" >'../stores/authModalStore';</span>
import { WholesaleQuoteForm } from <span class="cstat-no" title="statement not covered" >'../components/forms/WholesaleQuoteForm';</span>
import { NewsletterForm } from <span class="cstat-no" title="statement not covered" >'../components/forms/NewsletterForm';</span>
import { QuickView } from <span class="cstat-no" title="statement not covered" >'../components/shop/QuickView';</span>
import { ExitIntentPopup } from <span class="cstat-no" title="statement not covered" >'../components/marketing/ExitIntentPopup';</span>
import { useABTesting } from <span class="cstat-no" title="statement not covered" >'../components/marketing/ABTestingProvider';</span>
import { SmoothTransition } from <span class="cstat-no" title="statement not covered" >'../components/ui/animations/SmoothTransition';</span>
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';
import { motion } from 'framer-motion';
import { clearanceItems } from <span class="cstat-no" title="statement not covered" >'../data/clearanceItems';</span>
import { productionLines } from <span class="cstat-no" title="statement not covered" >'../data/productionLines';</span>
import { services } from <span class="cstat-no" title="statement not covered" >'../data/services';</span>
import { blogPosts } from <span class="cstat-no" title="statement not covered" >'../data/blogPosts';</span>
import { formatCurrency } from <span class="cstat-no" title="statement not covered" >'../lib/utils';</span>
import { getDbInstance } from '../lib/sqlite';
import { Product, ClearanceItem, Service } from '../types/index';
import { WishlistButton } from '../components/shop/WishlistButton';
import { QuickViewButton } from '../components/shop/QuickViewButton';
import { AddToCartButton } from '../components/shop/AddToCartButton';
import { ProductCard } from '../components/product/ProductCard';
&nbsp;
// صور القسم الرئيسي حسب اللغة - تم تحديثها لتكون أكثر تعبيراً عن المحتوى النصي
const heroImagesData = <span class="cstat-no" title="statement not covered" >{</span>
  en: [
    {
      url: 'https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1664&amp;q=80',
      alt: 'Business solutions showcase',
      title: 'Business Solutions',
      subtitle: 'Comprehensive solutions for your business needs'
    },
    {
      url: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80',
      alt: 'Professional services',
      title: 'Professional Services',
      subtitle: 'Expert services to support your business growth'
    },
    {
      url: 'https://images.unsplash.com/photo-1494412574643-ff11b0a5c1c3?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80',
      alt: 'Quality products',
      title: 'Quality Products',
      subtitle: 'Premium products with quality guarantee'
    }
  ],
  ar: [
    {
      url: 'https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1664&amp;q=80',
      alt: 'عرض حلول الأعمال',
      title: 'حلول الأعمال',
      subtitle: 'حلول شاملة لاحتياجات عملك'
    },
    {
      url: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80',
      alt: 'خدمات احترافية',
      title: 'خدمات احترافية',
      subtitle: 'خدمات خبيرة لدعم نمو أعمالك'
    },
    {
      url: 'https://images.unsplash.com/photo-1494412574643-ff11b0a5c1c3?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80',
      alt: 'منتجات عالية الجودة',
      title: 'منتجات عالية الجودة',
      subtitle: 'منتجات متميزة مع ضمان الجودة'
    }
  ]
};
&nbsp;
// حلول الأعمال حسب اللغة
const solutionsData = <span class="cstat-no" title="statement not covered" >{</span>
  en: [
    {
      icon: &lt;ShoppingCart size={24} /&gt;,
      title: "Retail (B2C)",
      description: "Shop our extensive catalog of products with competitive pricing and fast delivery options.",
      features: [
        "Premium product selection",
        "Fast worldwide shipping",
        "Secure payment processing",
        "Customer satisfaction guarantee"
      ],
      image: "https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg"
    },
    {
      icon: &lt;Users size={24} /&gt;,
      title: "Wholesale (B2B)",
      description: "Request quotes for bulk orders with special pricing available for business customers.",
      features: [
        "Volume discounts",
        "Dedicated account manager",
        "Flexible payment terms",
        "Custom packaging options"
      ],
      image: "https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg"
    },
    {
      icon: &lt;Package size={24} /&gt;,
      title: "Production Lines",
      description: "Browse our selection of turnkey manufacturing solutions with detailed specifications.",
      features: [
        "Automated systems",
        "Quality control integration",
        "Technical support",
        "Installation services"
      ],
      image: "https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg"
    },
    {
      icon: &lt;Truck size={24} /&gt;,
      title: "Business Services",
      description: "Inspection, storage, shipping, certification, and consulting services to support your operations.",
      features: [
        "Professional consulting",
        "Quality inspections",
        "Logistics solutions",
        "Certification support"
      ],
      image: "https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg"
    },
    {
      icon: &lt;Tag size={24} /&gt;,
      title: "Clearance Sales",
      description: "Take advantage of special deals on bulk liquidation items and overstock products.",
      features: [
        "Exclusive discounts",
        "Regular new arrivals",
        "Bulk opportunities",
        "Quick shipping"
      ],
      image: "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg"
    },
    {
      icon: &lt;FileText size={24} /&gt;,
      title: "Industry Insights",
      description: "Stay informed with our blog featuring the latest trends, tips, and best practices.",
      features: [
        "Expert analysis",
        "Market trends",
        "Success stories",
        "Industry updates"
      ],
      image: "https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg"
    }
  ],
  ar: [
    {
      icon: &lt;ShoppingCart size={24} /&gt;,
      title: "البيع بالتجزئة (B2C)",
      description: "تسوق من كتالوج منتجاتنا الواسع بأسعار تنافسية وخيارات توصيل سريعة.",
      features: [
        "اختيار منتجات متميزة",
        "شحن عالمي سريع",
        "معالجة آمنة للمدفوعات",
        "ضمان رضا العملاء"
      ],
      image: "https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg"
    },
    {
      icon: &lt;Users size={24} /&gt;,
      title: "البيع بالجملة (B2B)",
      description: "اطلب عروض أسعار للطلبات الكبيرة مع أسعار خاصة متاحة لعملاء الأعمال.",
      features: [
        "خصومات على الكميات",
        "مدير حساب مخصص",
        "شروط دفع مرنة",
        "خيارات تغليف مخصصة"
      ],
      image: "https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg"
    },
    {
      icon: &lt;Package size={24} /&gt;,
      title: "خطوط الإنتاج",
      description: "تصفح مجموعتنا من حلول التصنيع الجاهزة مع مواصفات مفصلة.",
      features: [
        "أنظمة آلية",
        "تكامل مراقبة الجودة",
        "دعم فني",
        "خدمات التركيب"
      ],
      image: "https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg"
    },
    {
      icon: &lt;Truck size={24} /&gt;,
      title: "خدمات الأعمال",
      description: "خدمات الفحص والتخزين والشحن والشهادات والاستشارات لدعم عملياتك.",
      features: [
        "استشارات احترافية",
        "فحوصات الجودة",
        "حلول لوجستية",
        "دعم الشهادات"
      ],
      image: "https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg"
    },
    {
      icon: &lt;Tag size={24} /&gt;,
      title: "تصفية المبيعات",
      description: "استفد من عروضنا المحدودة والصفقات الحصرية على الحلول الصناعية المتميزة.",
      features: [
        "خصومات حصرية",
        "وصول منتجات جديدة بانتظام",
        "فرص للشراء بالجملة",
        "شحن سريع"
      ],
      image: "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg"
    },
    {
      icon: &lt;FileText size={24} /&gt;,
      title: "رؤى الصناعة",
      description: "ابق على اطلاع من خلال مدونتنا التي تعرض أحدث الاتجاهات والنصائح وأفضل الممارسات.",
      features: [
        "تحليل الخبراء",
        "اتجاهات السوق",
        "قصص النجاح",
        "تحديثات الصناعة"
      ],
      image: "https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg"
    }
  ]
};
&nbsp;
function <span class="fstat-no" title="function not covered" >HomePage() {</span>
  const { locale } = <span class="cstat-no" title="statement not covered" >useTranslation();</span>
  const { language, direction } = <span class="cstat-no" title="statement not covered" >useLanguageStore(); /</span>/ Get direction here
  const { t } = <span class="cstat-no" title="statement not covered" >useTranslation();</span>
&nbsp;
  // استخدام البيانات المناسبة حسب اللغة المحددة
  const currentLanguage = <span class="cstat-no" title="statement not covered" >(locale as 'ar' | 'en') || language;</span>
  const heroImages = <span class="cstat-no" title="statement not covered" >heroImagesData[currentLanguage];</span>
  const currentSolutions = <span class="cstat-no" title="statement not covered" >solutionsData[currentLanguage];</span>
&nbsp;
  const [currentImageIndex, setCurrentImageIndex] = <span class="cstat-no" title="statement not covered" >useState(0);</span>
  const [hoveredSolution, setHoveredSolution] = <span class="cstat-no" title="statement not covered" >useState&lt;number | null&gt;(null);</span>
  const [showWholesaleForm, setShowWholesaleForm] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const [selectedProduct, setSelectedProduct] = <span class="cstat-no" title="statement not covered" >useState&lt;Product | null&gt;(null);</span>
  const [showQuickView, setShowQuickView] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const [selectedClearanceItem, setSelectedClearanceItem] = <span class="cstat-no" title="statement not covered" >useState&lt;ClearanceItem | null&gt;(null);</span>
  const [showClearanceQuoteForm, setShowClearanceQuoteForm] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const [selectedService, setSelectedService] = <span class="cstat-no" title="statement not covered" >useState&lt;Service | null&gt;(null);</span>
&nbsp;
  // فتح نافذة العرض السريع للمنتج
  const handleQuickView = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(p</span>roduct: Product) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSelectedProduct(product);</span>
<span class="cstat-no" title="statement not covered" >    setShowQuickView(true);</span>
  };
&nbsp;
  // فتح نموذج طلب عرض سعر للجملة
  const handleWholesaleQuote = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(p</span>roduct: Product) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSelectedProduct(product);</span>
<span class="cstat-no" title="statement not covered" >    setShowWholesaleForm(true);</span>
  };
&nbsp;
  // التحقق من تسجيل دخول المستخدم قبل إضافة المنتج إلى السلة أو المفضلة
  const { openModal } = <span class="cstat-no" title="statement not covered" >useAuthModalStore();</span>
  const { user } = <span class="cstat-no" title="statement not covered" >useAuthStore();</span>
&nbsp;
  const handleUnauthenticated = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >    openModal('sign-in');</span>
  };
&nbsp;
  // إضافة المنتج إلى السلة - يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول
  const handleAddToCart = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(p</span>roduct: Product) =&gt; {</span>
    // التحقق من توفر المنتج في المخزون
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (!product.inStock &amp;&amp; product.stock &lt;= 0) {</span>
      // يمكن إضافة إشعار هنا بأن المنتج غير متوفر
<span class="cstat-no" title="statement not covered" >      console.warn(`المنتج ${product.name} غير متوفر في المخزون`);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
    // تجهيز بيانات المنتج للإضافة إلى السلة
    const cartItem = <span class="cstat-no" title="statement not covered" >{</span>
      id: product.id,
      name: product.name,
      name_ar: product.name_ar || product.name,
      price: product.price,
      image: product.images &amp;&amp; product.images.length &gt; 0 ? product.images[0] : '/images/product-placeholder-light.svg',
      quantity: 1,
    };
&nbsp;
    // إضافة المنتج إلى السلة باستخدام getState للحصول على حالة المتجر
    const cart = <span class="cstat-no" title="statement not covered" >useCartStore.getState();</span>
<span class="cstat-no" title="statement not covered" >    cart.addItem(cartItem, 1);</span>
&nbsp;
    // إظهار إشعار بنجاح الإضافة (يمكن تحسين هذه الوظيفة لاحقاً بإضافة نظام إشعارات)
<span class="cstat-no" title="statement not covered" >    console.log(`تمت إضافة ${product.name} إلى السلة`);</span>
&nbsp;
    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم
    // trackEvent('add_to_cart', { product_id: product.id, product_name: product.name });
  };
&nbsp;
  // إضافة المنتج إلى المفضلة - لا يتطلب تسجيل الدخول
  const handleAddToWishlist = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(p</span>roduct: Product) =&gt; {</span>
    const wishlist = <span class="cstat-no" title="statement not covered" >useWishlistStore.getState();</span>
&nbsp;
    // التحقق مما إذا كان المنتج موجوداً بالفعل في المفضلة
<span class="cstat-no" title="statement not covered" >    if (wishlist.isInWishlist(product.id)) {</span>
      // إزالة المنتج من المفضلة
<span class="cstat-no" title="statement not covered" >      wishlist.removeItem(product.id);</span>
<span class="cstat-no" title="statement not covered" >      console.log(`تمت إزالة ${product.name} من المفضلة`);</span>
    } else {
      // إضافة المنتج إلى المفضلة
<span class="cstat-no" title="statement not covered" >      wishlist.addItem(product);</span>
<span class="cstat-no" title="statement not covered" >      console.log(`تمت إضافة ${product.name} إلى المفضلة`);</span>
    }
&nbsp;
    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم
    // trackEvent('toggle_wishlist', { product_id: product.id, product_name: product.name });
  };
&nbsp;
  const [featuredProducts, setFeaturedProducts] = <span class="cstat-no" title="statement not covered" >useState&lt;Product[]&gt;([]);</span>
  const [isLoadingProducts, setIsLoadingProducts] = <span class="cstat-no" title="statement not covered" >useState(true);</span>
  const [showExitIntentPopup, setShowExitIntentPopup] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
&nbsp;
  // تحميل المنتجات المميزة
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
    const loadProducts = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async () =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >      setIsLoadingProducts(true);</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
        // استخدام البيانات الثابتة بدلاً من قاعدة البيانات
        // في المستقبل يمكن استبدال هذا بالاتصال بقاعدة البيانات
<span class="cstat-no" title="statement not covered" ><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >        import('</span>../data/products').t</span>hen(<span class="fstat-no" title="function not covered" >module =&gt; {</span></span>
          const productsData = <span class="cstat-no" title="statement not covered" >module.products || [];</span>
&nbsp;
          // اختيار المنتجات المميزة فقط
          const featured = <span class="cstat-no" title="statement not covered" >productsData.filter(<span class="fstat-no" title="function not covered" >p =&gt; <span class="cstat-no" title="statement not covered" >p</span>.featured)</span>.slice(0, 8);</span>
&nbsp;
          // إذا لم توجد منتجات مميزة، استخدم أول 8 منتجات
          const productsToShow = <span class="cstat-no" title="statement not covered" >featured.length &gt; 0 ? featured : productsData.slice(0, 8);</span>
&nbsp;
          // تأكد من أن جميع المنتجات تحتوي على الحقول المطلوبة
          const validatedProducts = <span class="cstat-no" title="statement not covered" >productsToShow.map(<span class="fstat-no" title="function not covered" >product =&gt; (<span class="cstat-no" title="statement not covered" >{</span></span></span>
            ...product,
            // تأكد من وجود صور للمنتج
            images: product.images &amp;&amp; product.images.length &gt; 0
              ? product.images
              : ['/images/product-placeholder-light.svg'],
            // تأكد من وجود سعر للمنتج
            price: product.price || 0,
            // تأكد من وجود مخزون للمنتج
            stock: typeof product.stock === 'number' ? product.stock : 10,
            // تأكد من وجود تقييم للمنتج
            rating: product.rating || 0,
            reviewCount: product.reviewCount || 0,
            // إضافة حقل inStock لتسهيل التحقق من توفر المنتج
            inStock: typeof product.stock === 'number' ? product.stock &gt; 0 : true,
            // إضافة حقل discount لحساب نسبة الخصم
            discount: product.compareAtPrice &amp;&amp; product.compareAtPrice &gt; product.price
              ? Math.round((1 - product.price / product.compareAtPrice) * 100)
              : 0,
            // إضافة حقول الترجمة إذا لم تكن موجودة
            name_ar: product.name_ar || product.name,
            description_ar: product.description_ar || product.description
          }));
&nbsp;
<span class="cstat-no" title="statement not covered" >          setFeaturedProducts(validatedProducts);</span>
<span class="cstat-no" title="statement not covered" >          setIsLoadingProducts(false);</span>
        }).catch(<span class="fstat-no" title="function not covered" >error =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          console.error("Failed to load products data:", error);</span>
<span class="cstat-no" title="statement not covered" >          setIsLoadingProducts(false);</span>
        });
      } catch (error) {
<span class="cstat-no" title="statement not covered" >        console.error("Failed to load products:", error);</span>
<span class="cstat-no" title="statement not covered" >        setIsLoadingProducts(false);</span>
      }
    };
<span class="cstat-no" title="statement not covered" >    loadProducts();</span>
  }, []);
&nbsp;
  // تحسين مدة الانتقال بين الصور وإضافة تأثيرات انتقالية أكثر سلاسة
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
    // زيادة مدة عرض كل صورة إلى 7 ثوانٍ لإعطاء المستخدم وقتًا كافيًا لقراءة المحتوى
    const interval = <span class="cstat-no" title="statement not covered" >setInterval(<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
      // تغيير الصورة مباشرة بدون تأثير انتقالي على الصفحة بأكملها
<span class="cstat-no" title="statement not covered" >      setCurrentImageIndex(<span class="fstat-no" title="function not covered" >(p</span>rev) =&gt; <span class="cstat-no" title="statement not covered" >(prev + 1) % heroImages.length)</span>;</span>
    }, 7000);
&nbsp;
<span class="cstat-no" title="statement not covered" >    return <span class="fstat-no" title="function not covered" >() =&gt; <span class="cstat-no" title="statement not covered" >c</span>learInterval(interval);</span></span>
  }, [heroImages.length]);
&nbsp;
  // استخدام A/B Testing للصور البارزة
  const { getVariant, trackConversion } = <span class="cstat-no" title="statement not covered" >useABTesting();</span>
  const heroVariant = <span class="cstat-no" title="statement not covered" >getVariant('heroImage');</span>
&nbsp;
  // تتبع التحويل عند النقر على زر "استكشف المنتجات"
  const handleExploreClick = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >    trackConversion('heroImage');</span>
  };
&nbsp;
  // Exit-intent popup logic
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
    const handleMouseLeave = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(e</span>vent: MouseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      <span class="missing-if-branch" title="if path not taken" >I</span>if (event.clientY &lt;= 0 &amp;&amp; !sessionStorage.getItem('exitIntentShown')) {</span>
<span class="cstat-no" title="statement not covered" >        setShowExitIntentPopup(true);</span>
<span class="cstat-no" title="statement not covered" >        sessionStorage.setItem('exitIntentShown', 'true');</span>
      }
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    document.addEventListener('mouseleave', handleMouseLeave);</span>
<span class="cstat-no" title="statement not covered" >    return <span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >      document.removeEventListener('mouseleave', handleMouseLeave);</span>
    };
  }, []);
&nbsp;
  // Adapter function to make ClearanceItem compatible with Product-expecting components
  const clearanceProductAdapter = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(i</span>tem: ClearanceItem): Product =&gt; (<span class="cstat-no" title="statement not covered" >{</span></span>
    id: item.id,
    name: item.name,
    slug: `/clearance/${item.id}`, // Generate a relevant slug
    description: item.description,
    price: item.clearancePrice,
    compareAtPrice: item.originalPrice,
    images: item.image ? [item.image] : [],
    category: item.category,
    tags: [item.category], // Simple tagging based on category
    stock: item.availableQuantity,
    featured: false, // Clearance items are generally not featured
    specifications: {}, // No detailed specs for clearance items
    createdAt: new Date().toISOString(),
    // Optional fields from Product, can be undefined or have default values
    reviews: [],
    rating: 0,
    reviewCount: 0,
    relatedProducts: [],
  });
&nbsp;
  return (
    &lt;div&gt;
      {/* نافذة منبثقة عند محاولة المغادرة */}
      {showExitIntentPopup &amp;&amp; featuredProducts.length &gt; 0 &amp;&amp; (
        &lt;ExitIntentPopup
          onClose={<span class="fstat-no" title="function not covered" >() =&gt; <span class="cstat-no" title="statement not covered" >s</span>etShowExitIntentPopup(false)}</span>
          product={featuredProducts[Math.floor(Math.random() * featuredProducts.length)]} // Use a random product from fetched list
        /&gt;
      )}
&nbsp;
      {/* Hero Section - تصميم محسن مع عناصر تفاعلية متقدمة */}
      &lt;section className="relative h-[90vh] overflow-hidden bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900 text-white" dir={direction}&gt;
        {/* طبقة التراكب الأساسية مع تأثيرات محسنة */}
        &lt;div className="absolute inset-0 bg-gradient-to-br from-primary-900/90 via-black/50 to-secondary-900/80 z-10 mix-blend-multiply" /&gt;
&nbsp;
        {/* تأثير الجسيمات المتحركة */}
        &lt;div className="absolute inset-0 z-5 opacity-30"&gt;
          &lt;div className="absolute inset-0 overflow-hidden"&gt;
            &lt;div className="particles-container absolute inset-0"&gt;
              {[...Array(20)].map(<span class="fstat-no" title="function not covered" >(_</span>, i) =&gt; (
<span class="cstat-no" title="statement not covered" >                &lt;div</span>
                  key={`particle-${i}`}
                  className={`absolute rounded-full bg-white/20 animate-pulse`}
                  style={{
                    width: `${Math.random() * 5 + 2}px`,
                    height: `${Math.random() * 5 + 2}px`,
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                    animationDuration: `${Math.random() * 8 + 2}s`,
                    animationDelay: `${Math.random() * 5}s`
                  }}
                /&gt;
              ))}
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
&nbsp;
        {/* أنماط هندسية متحركة في الخلفية */}
        &lt;div className="absolute inset-0 z-5 opacity-20"&gt;
          &lt;div className="absolute top-0 left-0 w-full h-full"&gt;
            &lt;svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none"&gt;
              &lt;defs&gt;
                &lt;linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%"&gt;
                  &lt;stop offset="0%" stopColor="var(--color-primary-300)" stopOpacity="0.4" /&gt;
                  &lt;stop offset="100%" stopColor="var(--color-secondary-300)" stopOpacity="0.4" /&gt;
                &lt;/linearGradient&gt;
                &lt;pattern id="dots-pattern" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"&gt;
                  &lt;circle cx="5" cy="5" r="0.5" fill="rgba(255,255,255,0.2)" /&gt;
                &lt;/pattern&gt;
              &lt;/defs&gt;
              &lt;path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#grid-gradient)" /&gt;
              &lt;rect x="0" y="0" width="100" height="100" fill="url(#dots-pattern)" /&gt;
&nbsp;
              {/* خطوط شبكية متحركة محسنة */}
              {[...Array(12)].map(<span class="fstat-no" title="function not covered" >(_</span>, i) =&gt; (
<span class="cstat-no" title="statement not covered" >                &lt;line</span>
                  key={`h-${i}`}
                  x1="0"
                  y1={i * 8.33}
                  x2="100"
                  y2={i * 8.33}
                  stroke="rgba(255,255,255,0.15)"
                  strokeWidth="0.1"
                  strokeDasharray="0.5,3"
                /&gt;
              ))}
              {[...Array(12)].map(<span class="fstat-no" title="function not covered" >(_</span>, i) =&gt; (
<span class="cstat-no" title="statement not covered" >                &lt;line</span>
                  key={`v-${i}`}
                  x1={i * 8.33}
                  y1="0"
                  x2={i * 8.33}
                  y2="100"
                  stroke="rgba(255,255,255,0.15)"
                  strokeWidth="0.1"
                  strokeDasharray="0.5,3"
                /&gt;
              ))}
            &lt;/svg&gt;
          &lt;/div&gt;
        &lt;/div&gt;
&nbsp;
        {/* صور الخلفية المتحركة - تحسين أبعاد الصورة */}
        &lt;div className="absolute inset-0 z-0"&gt;
          {heroImages.map(<span class="fstat-no" title="function not covered" >(i</span>mage, index) =&gt; (
<span class="cstat-no" title="statement not covered" >            &lt;div</span>
              key={index}
              className={`absolute inset-0 transition-all duration-2000 ${
                index === currentImageIndex
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-110'
              }`}
            &gt;
              &lt;div className="absolute inset-0 bg-gradient-to-t from-primary-900/80 via-primary-900/40 to-transparent z-10" /&gt;
              &lt;EnhancedImage
                src={image.url}
                alt={image.alt}
                fill={true}
                objectFit="cover"
                progressive={true}
                placeholder="shimmer"
                className="object-center filter brightness-[0.8] contrast-[1.1]"
                containerClassName="w-full h-full"
                sizes="(max-width: 768px) 100vw, 100vw"
                priority={index === 0}
                quality={95}
              /&gt;
&nbsp;
&nbsp;
            &lt;/div&gt;
          ))}
        &lt;/div&gt;
&nbsp;
        {/* المحتوى الرئيسي - مركز مع تأثيرات محسنة */}
        &lt;div className="container-custom relative z-20 h-full flex items-center"&gt;
          &lt;div className="flex flex-col items-center justify-center h-full py-16 w-full"&gt;
            {/* المحتوى النصي المركز */}
            &lt;div className="flex flex-col justify-center text-center w-full max-w-4xl mx-auto"&gt;
              {/* شعار الشركة المتحرك مع تأثيرات محسنة */}
              &lt;SmoothTransition type="fade" duration={0.5} delay={0.1}&gt;
                &lt;div className="flex items-center justify-center mb-8 relative"&gt;
                  &lt;div className="absolute -inset-4 bg-gradient-to-r from-accent-500/0 via-accent-500/20 to-accent-500/0 rounded-full blur-xl opacity-70 animate-pulse"&gt;&lt;/div&gt;
                  &lt;div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500/40 to-primary-500/40 backdrop-blur-md flex items-center justify-center shadow-lg shadow-primary-500/20 relative overflow-hidden group"&gt;
                    &lt;div className="absolute inset-0 bg-gradient-to-br from-accent-500/0 to-primary-500/0 group-hover:from-accent-500/20 group-hover:to-primary-500/20 transition-all duration-700"&gt;&lt;/div&gt;
                    &lt;svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-accent-300 relative z-10"
                    &gt;
                      &lt;path d="M12 2L2 7l10 5 10-5-10-5z"&gt;&lt;/path&gt;
                      &lt;path d="M2 17l10 5 10-5"&gt;&lt;/path&gt;
                      &lt;path d="M2 12l10 5 10-5"&gt;&lt;/path&gt;
                    &lt;/svg&gt;
                  &lt;/div&gt;
                  &lt;span className={`text-3xl font-bold ${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} bg-clip-text text-transparent bg-gradient-to-r from-accent-300 to-white relative`}&gt;
                    {currentLanguage === 'ar' ? 'ارتال' : 'ARTAL'}
                    &lt;span className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-accent-500/0 via-accent-500/80 to-accent-500/0"&gt;&lt;/span&gt;
                  &lt;/span&gt;
                &lt;/div&gt;
              &lt;/SmoothTransition&gt;
&nbsp;
              {/* العنوان الرئيسي مع تأثيرات محسنة */}
              &lt;SmoothTransition type="slide" direction="up" duration={0.7} delay={0.2}&gt;
                &lt;h1 className="text-4xl md:text-6xl lg:text-7xl font-extrabold mb-8 leading-tight"&gt;
                  &lt;span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-100 to-white relative inline-block"&gt;
                    {currentLanguage === 'ar'
                      ? 'حلول أعمال متكاملة'
                      : 'Integrated Business Solutions'}
                  &lt;/span&gt;
                  &lt;br /&gt;
                  &lt;span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-300 via-white to-accent-300 relative inline-block mt-2"&gt;
                    {currentLanguage === 'ar'
                      ? 'من ارتال'
                      : 'by ARTAL'}
                    &lt;div className="absolute -bottom-2 left-1/4 right-1/4 h-1 bg-gradient-to-r from-accent-500/0 via-accent-500 to-accent-500/0 rounded-full"&gt;&lt;/div&gt;
                  &lt;/span&gt;
                &lt;/h1&gt;
              &lt;/SmoothTransition&gt;
&nbsp;
              {/* العنوان الفرعي مع تأثيرات محسنة */}
              &lt;SmoothTransition type="slide" direction="up" duration={0.7} delay={0.3}&gt;
                &lt;p className="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed"&gt;
                  {currentLanguage === 'ar'
                    ? 'نقدم حلولاً متكاملة للتجارة الإلكترونية والخدمات اللوجستية وخطوط الإنتاج لتعزيز نمو أعمالك'
                    : 'We offer integrated solutions for e-commerce, logistics, and production lines to enhance your business growth'}
                &lt;/p&gt;
              &lt;/SmoothTransition&gt;
&nbsp;
              {/* أزرار الدعوة للعمل مع تأثيرات محسنة */}
              &lt;SmoothTransition type="slide" direction="up" duration={0.7} delay={0.5}&gt;
                &lt;div className="flex flex-wrap gap-6 mb-16 justify-center"&gt;
                  &lt;HeroButton
                    href={`/${currentLanguage}/shop`}
                    variant="accent"
                    className="flex items-center py-4 px-10 relative overflow-hidden shadow-xl hover:shadow-accent-500/30 group rounded-xl text-lg font-medium transition-all duration-300"
                    onClick={handleExploreClick}
                  &gt;
                    &lt;span className="absolute inset-0 bg-gradient-to-r from-accent-600 to-accent-500 z-0"&gt;&lt;/span&gt;
                    &lt;span className="absolute inset-0 bg-gradient-to-r from-accent-500 to-accent-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0"&gt;&lt;/span&gt;
                    &lt;span className="relative z-10"&gt;{currentLanguage === 'ar' ? 'استكشف حلولنا' : 'Explore Our Solutions'}&lt;/span&gt;
                    &lt;ArrowRight className={`w-5 h-5 ${currentLanguage === 'ar' ? 'mr-3 rotate-180' : 'ml-3'} relative z-10 transition-transform duration-300 group-hover:translate-x-1`} /&gt;
                  &lt;/HeroButton&gt;
                  &lt;HeroButton
                    href={`/${currentLanguage}/contact`}
                    variant="outline"
                    className="py-4 px-10 relative overflow-hidden border-2 backdrop-blur-sm rounded-xl hover:bg-white/10 transition-all duration-300 text-lg font-medium group"
                  &gt;
                    &lt;span className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover:from-white/5 group-hover:to-white/10 transition-all duration-300 z-0"&gt;&lt;/span&gt;
                    &lt;span className="relative z-10"&gt;{currentLanguage === 'ar' ? 'طلب استشارة مجانية' : 'Free Consultation'}&lt;/span&gt;
                  &lt;/HeroButton&gt;
                &lt;/div&gt;
              &lt;/SmoothTransition&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
&nbsp;
        {/* مؤشرات الشرائح مع تصميم محسن */}
        &lt;div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"&gt;
          &lt;div className="flex justify-center gap-4"&gt;
            {heroImages.map(<span class="fstat-no" title="function not covered" >(_</span>, index) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;button</span>
                key={index}
                onClick={<span class="fstat-no" title="function not covered" >() =&gt; <span class="cstat-no" title="statement not covered" >s</span>etCurrentImageIndex(index)}</span>
                className={`relative h-3 rounded-full transition-all duration-500 shadow-lg overflow-hidden ${
                  index === currentImageIndex
                    ? 'bg-accent-500 w-12'
                    : 'bg-white/40 hover:bg-white/60 w-3'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              &gt;
                {<span class="branch-0 cbranch-no" title="branch not covered" >index === currentImageIndex &amp;&amp; (</span>
                  &lt;span className="absolute inset-0 bg-gradient-to-r from-accent-500/0 via-white/30 to-accent-500/0 animate-slide-right"&gt;&lt;/span&gt;
                )}
              &lt;/button&gt;
            ))}
          &lt;/div&gt;
        &lt;/div&gt;
&nbsp;
        {/* إحصائيات الخدمات - أسفل قسم Hero - تصميم محسن ومتطور بحجم أصغر */}
        &lt;div className="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-primary-900/90 via-secondary-900/90 to-primary-900/90 backdrop-blur-xl py-3 border-t border-white/20"&gt;
          &lt;div className="container-custom"&gt;
            &lt;div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center relative"&gt;
              {/* زخرفة خلفية متطورة */}
              &lt;div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.15)_0%,transparent_70%)] opacity-60"&gt;&lt;/div&gt;
              &lt;div className="absolute inset-0 overflow-hidden"&gt;
                &lt;div className="absolute w-full h-full opacity-10"&gt;
                  &lt;svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none"&gt;
                    &lt;defs&gt;
                      &lt;pattern id="stats-grid" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"&gt;
                        &lt;path d="M10 0H0V10" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" /&gt;
                      &lt;/pattern&gt;
                    &lt;/defs&gt;
                    &lt;rect x="0" y="0" width="100" height="100" fill="url(#stats-grid)" /&gt;
                  &lt;/svg&gt;
                &lt;/div&gt;
              &lt;/div&gt;
&nbsp;
              {/* إحصائية 1 - خدمات احترافية */}
              &lt;div className="group relative"&gt;
                &lt;div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"&gt;&lt;/div&gt;
                &lt;div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]"&gt;
                  &lt;div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/40 to-purple-700/40 flex items-center justify-center mb-2 shadow-md shadow-purple-500/20 group-hover:shadow-purple-500/40 transition-all duration-500 relative overflow-hidden"&gt;
                    &lt;div className="absolute inset-0 bg-[conic-gradient(from_0deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"&gt;&lt;/div&gt;
                    &lt;div className="absolute inset-0 bg-gradient-to-br from-purple-500/0 to-purple-700/0 group-hover:from-purple-500/20 group-hover:to-purple-700/20 transition-all duration-500"&gt;&lt;/div&gt;
                    &lt;Box className="w-6 h-6 text-purple-300 group-hover:text-purple-200 transition-colors duration-300 relative z-10" /&gt;
                  &lt;/div&gt;
                  &lt;div className="relative"&gt;
                    &lt;div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-purple-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300"&gt;6+&lt;/div&gt;
                    &lt;div className="absolute -inset-1 bg-purple-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"&gt;&lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div className="text-xs md:text-sm text-white/90 font-medium relative"&gt;
                    &lt;span className="relative inline-block"&gt;
                      {currentLanguage === 'ar' ? 'خدمات احترافية' : 'Professional Services'}
                      &lt;span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-purple-400 group-hover:w-full transition-all duration-300"&gt;&lt;/span&gt;
                    &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
&nbsp;
              {/* إحصائية 2 - دعم العملاء */}
              &lt;div className="group relative"&gt;
                &lt;div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"&gt;&lt;/div&gt;
                &lt;div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]"&gt;
                  &lt;div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/40 to-blue-700/40 flex items-center justify-center mb-2 shadow-md shadow-blue-500/20 group-hover:shadow-blue-500/40 transition-all duration-500 relative overflow-hidden"&gt;
                    &lt;div className="absolute inset-0 bg-[conic-gradient(from_90deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"&gt;&lt;/div&gt;
                    &lt;div className="absolute inset-0 bg-gradient-to-br from-blue-500/0 to-blue-700/0 group-hover:from-blue-500/20 group-hover:to-blue-700/20 transition-all duration-500"&gt;&lt;/div&gt;
                    &lt;Clock className="w-6 h-6 text-blue-300 group-hover:text-blue-200 transition-colors duration-300 relative z-10" /&gt;
                  &lt;/div&gt;
                  &lt;div className="relative"&gt;
                    &lt;div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300"&gt;24/7&lt;/div&gt;
                    &lt;div className="absolute -inset-1 bg-blue-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"&gt;&lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div className="text-xs md:text-sm text-white/90 font-medium relative"&gt;
                    &lt;span className="relative inline-block"&gt;
                      {currentLanguage === 'ar' ? 'دعم العملاء' : 'Customer Support'}
                      &lt;span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-blue-400 group-hover:w-full transition-all duration-300"&gt;&lt;/span&gt;
                    &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
&nbsp;
              {/* إحصائية 3 - عملاء راضون */}
              &lt;div className="group relative"&gt;
                &lt;div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-amber-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"&gt;&lt;/div&gt;
                &lt;div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]"&gt;
                  &lt;div className="w-12 h-12 rounded-xl bg-gradient-to-br from-amber-500/40 to-amber-700/40 flex items-center justify-center mb-2 shadow-md shadow-amber-500/20 group-hover:shadow-amber-500/40 transition-all duration-500 relative overflow-hidden"&gt;
                    &lt;div className="absolute inset-0 bg-[conic-gradient(from_180deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"&gt;&lt;/div&gt;
                    &lt;div className="absolute inset-0 bg-gradient-to-br from-amber-500/0 to-amber-700/0 group-hover:from-amber-500/20 group-hover:to-amber-700/20 transition-all duration-500"&gt;&lt;/div&gt;
                    &lt;Star className="w-6 h-6 text-amber-300 group-hover:text-amber-200 transition-colors duration-300 relative z-10" /&gt;
                  &lt;/div&gt;
                  &lt;div className="relative"&gt;
                    &lt;div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300"&gt;500+&lt;/div&gt;
                    &lt;div className="absolute -inset-1 bg-amber-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"&gt;&lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div className="text-xs md:text-sm text-white/90 font-medium relative"&gt;
                    &lt;span className="relative inline-block"&gt;
                      {currentLanguage === 'ar' ? 'عملاء راضون' : 'Satisfied Clients'}
                      &lt;span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-amber-400 group-hover:w-full transition-all duration-300"&gt;&lt;/span&gt;
                    &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
&nbsp;
              {/* إحصائية 4 - ضمان الجودة */}
              &lt;div className="group relative"&gt;
                &lt;div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-green-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"&gt;&lt;/div&gt;
                &lt;div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]"&gt;
                  &lt;div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/40 to-green-700/40 flex items-center justify-center mb-2 shadow-md shadow-green-500/20 group-hover:shadow-green-500/40 transition-all duration-500 relative overflow-hidden"&gt;
                    &lt;div className="absolute inset-0 bg-[conic-gradient(from_270deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"&gt;&lt;/div&gt;
                    &lt;div className="absolute inset-0 bg-gradient-to-br from-green-500/0 to-green-700/0 group-hover:from-green-500/20 group-hover:to-green-700/20 transition-all duration-500"&gt;&lt;/div&gt;
                    &lt;Shield className="w-6 h-6 text-green-300 group-hover:text-green-200 transition-colors duration-300 relative z-10" /&gt;
                  &lt;/div&gt;
                  &lt;div className="relative"&gt;
                    &lt;div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-green-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300"&gt;100%&lt;/div&gt;
                    &lt;div className="absolute -inset-1 bg-green-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"&gt;&lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div className="text-xs md:text-sm text-white/90 font-medium relative"&gt;
                    &lt;span className="relative inline-block"&gt;
                      {currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Guarantee'}
                      &lt;span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-green-400 group-hover:w-full transition-all duration-300"&gt;&lt;/span&gt;
                    &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
&nbsp;
&nbsp;
      {/* Our Solutions Section - تحسين قسم الحلول بشكل احترافي */}
      &lt;section id="solutions" className="py-24 bg-gradient-to-b from-slate-50 via-white to-slate-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900" dir={direction}&gt;
        &lt;div className="container-custom"&gt;
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          &lt;div className="text-center mb-20 relative"&gt;
            {/* خط زخرفي علوي */}
            &lt;div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full opacity-80"&gt;&lt;/div&gt;
&nbsp;
            {/* أيقونة القسم */}
            &lt;div className="inline-flex items-center justify-center w-16 h-16 mb-6 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400"&gt;
              &lt;Settings className="w-8 h-8" /&gt;
            &lt;/div&gt;
&nbsp;
            {/* العنوان الرئيسي */}
            &lt;h2 className="text-3xl md:text-5xl font-bold tracking-tight text-gray-900 dark:text-white mb-6 relative inline-block"&gt;
              &lt;span className="bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-400 dark:to-accent-400"&gt;
                {t('solutions.title')}
              &lt;/span&gt;
              &lt;div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"&gt;&lt;/div&gt;
            &lt;/h2&gt;
&nbsp;
            {/* العنوان الفرعي */}
            &lt;p className="mt-4 text-lg md:text-xl leading-8 text-slate-600 dark:text-slate-300 max-w-3xl mx-auto"&gt;
              {t('solutions.subtitle')}
            &lt;/p&gt;
          &lt;/div&gt;
&nbsp;
          {/* شبكة الحلول */}
          &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10"&gt;
            {currentSolutions.map(<span class="fstat-no" title="function not covered" >(s</span>olution, index) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;div</span>
                key={index}
                className="group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800/90 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
              &gt;
                {/* خط علوي متحرك */}
                &lt;div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-500 scale-x-0 group-hover:scale-x-100"&gt;&lt;/div&gt;
&nbsp;
                {/* رأس البطاقة مع الأيقونة والعنوان */}
                &lt;div className="relative p-6 pb-0"&gt;
                  &lt;div className="flex items-center mb-4"&gt;
                    &lt;div className="p-3 bg-primary-50 dark:bg-primary-900/30 rounded-xl text-primary-600 dark:text-primary-400 shadow-sm"&gt;
                      {solution.icon}
                    &lt;/div&gt;
                    &lt;h3 className={`text-xl font-bold ${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} text-slate-900 dark:text-white`}&gt;
                      {solution.title}
                    &lt;/h3&gt;
                  &lt;/div&gt;
&nbsp;
                  {/* وصف الحل */}
                  &lt;p className="text-base text-slate-600 dark:text-slate-300 mb-6 leading-relaxed"&gt;
                    {solution.description}
                  &lt;/p&gt;
                &lt;/div&gt;
&nbsp;
                {/* محتوى الحل والميزات */}
                &lt;div className="p-6 pt-0"&gt;
                  {solution.features &amp;&amp; solution.features.length &gt; 0 &amp;&amp; (
                    &lt;div className="mt-2"&gt;
                      &lt;h4 className="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-4 flex items-center"&gt;
                        &lt;Star size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} text-accent-500`} /&gt;
                        {t('solutions.features')}
                      &lt;/h4&gt;
                      &lt;ul className="space-y-3 text-sm text-slate-600 dark:text-slate-300"&gt;
                        {solution.features.map(<span class="fstat-no" title="function not covered" >(f</span>eature, fIndex) =&gt; (
<span class="cstat-no" title="statement not covered" >                          &lt;li key={fIndex} c</span>lassName="flex items-start"&gt;
                            &lt;CheckCircle size={16} className={`flex-shrink-0 w-4 h-4 text-primary-500 dark:text-primary-400 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} mt-0.5`} /&gt;
                            &lt;span&gt;{feature}&lt;/span&gt;
                          &lt;/li&gt;
                        ))}
                      &lt;/ul&gt;
                    &lt;/div&gt;
                  )}
&nbsp;
                  {/* زر التفاصيل مع رابط صحيح */}
                  &lt;div className="mt-6 pt-4 border-t border-slate-100 dark:border-slate-700/50"&gt;
                    &lt;Link
                      href={`/${currentLanguage}/${solution.title === "Retail (B2C)" || solution.title === "البيع بالتجزئة (B2C)" ? 'shop' :
                             solution.title === "Wholesale (B2B)" || solution.title === "البيع بالجملة (B2B)" ? 'wholesale' :
                             solution.title === "Production Lines" || solution.title === "خطوط الإنتاج" ? 'production-lines' :
                             solution.title === "Business Services" || solution.title === "خدمات الأعمال" ? 'services' :
                             solution.title === "Clearance Sales" || solution.title === "تصفية المبيعات" ? 'clearance' :
                             solution.title === "Industry Insights" || solution.title === "رؤى الصناعة" ? 'blog' : 'solutions'}`}
                      className="flex items-center justify-between text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300 transition-colors group"
                      aria-label={`${currentLanguage === 'ar' ? 'اكتشف المزيد عن' : 'Discover more about'} ${solution.title}`}
                    &gt;
                      &lt;span&gt;{currentLanguage === 'ar' ? 'اكتشف المزيد' : 'Discover More'}&lt;/span&gt;
                      &lt;ArrowRight className={`w-4 h-4 transform transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                    &lt;/Link&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
&nbsp;
                {/* خط سفلي متحرك */}
                &lt;div className="absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-500 scale-x-0 group-hover:scale-x-100"&gt;&lt;/div&gt;
              &lt;/div&gt;
            ))}
          &lt;/div&gt;
&nbsp;
&nbsp;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
      {/* Featured Products - قسم المنتجات المميزة */}
      &lt;section id="featured-products" className="py-16 md:py-24 bg-gradient-to-b from-background via-background to-slate-50 dark:from-background dark:via-background dark:to-slate-900/20" dir={direction}&gt;
        &lt;div className="container-custom"&gt;
          {/* عنوان القسم مع تأثيرات بصرية */}
          &lt;div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12"&gt;
            &lt;div className="relative"&gt;
              &lt;div className={`absolute ${currentLanguage === 'ar' ? '-right-3' : '-left-3'} top-0 h-12 w-1 bg-gradient-to-b from-primary-500 to-primary-700 rounded-full hidden md:block`}&gt;&lt;/div&gt;
              &lt;h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white"&gt;
                {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
              &lt;/h2&gt;
              &lt;p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl"&gt;
                {currentLanguage === 'ar' ? 'اكتشف أحدث ابتكاراتنا وأفضل الحلول الصناعية مبيعاً' : 'Discover our latest innovations and best-selling industrial solutions'}
              &lt;/p&gt;
            &lt;/div&gt;
            &lt;div className="flex flex-wrap items-center gap-4 mt-6 md:mt-0"&gt;
              &lt;Link href={`/${currentLanguage}/shop`}&gt;
                &lt;Button
                  variant="outline"
                  className="flex items-center gap-2 shadow-sm hover:shadow transition-shadow"
                  aria-label={currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'View all products'}
                &gt;
                  {currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}
                  &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                &lt;/Button&gt;
              &lt;/Link&gt;
              &lt;Link href={`/${currentLanguage}/shop/new-arrivals`}&gt;
                &lt;Button
                  variant="primary"
                  className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                  aria-label={currentLanguage === 'ar' ? 'تصفح المنتجات الجديدة' : 'Browse new arrivals'}
                &gt;
                  {currentLanguage === 'ar' ? 'واصل حديثاً' : 'New Arrivals'}
                  &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                &lt;/Button&gt;
              &lt;/Link&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* فلاتر المنتجات */}
          &lt;div className="flex flex-wrap items-center gap-2 mb-8 bg-slate-50 dark:bg-slate-800/50 p-4 rounded-lg shadow-sm"&gt;
            &lt;span className={`text-sm font-medium text-slate-700 dark:text-slate-300 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`}&gt;
              {currentLanguage === 'ar' ? 'تصفية حسب:' : 'Filter by:'}
            &lt;/span&gt;
            &lt;Button
              variant="ghost"
              size="sm"
              className="rounded-full bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-900/50"
              aria-pressed="true"
              onClick={<span class="fstat-no" title="function not covered" >() =&gt; {/* يمكن إضافة وظيفة تصفية هنا */}</span>}
            &gt;
              {currentLanguage === 'ar' ? 'الكل' : 'All'}
            &lt;/Button&gt;
            &lt;Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/30 dark:hover:text-primary-300"
              aria-pressed="false"
              onClick={<span class="fstat-no" title="function not covered" >() =&gt; {/* يمكن إضافة وظيفة تصفية هنا */}</span>}
            &gt;
              {currentLanguage === 'ar' ? 'الأكثر مبيعاً' : 'Best Sellers'}
            &lt;/Button&gt;
            &lt;Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/30 dark:hover:text-primary-300"
              aria-pressed="false"
              onClick={<span class="fstat-no" title="function not covered" >() =&gt; {/* يمكن إضافة وظيفة تصفية هنا */}</span>}
            &gt;
              {currentLanguage === 'ar' ? 'واصل حديثاً' : 'New Arrivals'}
            &lt;/Button&gt;
            &lt;Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/30 dark:hover:text-primary-300"
              aria-pressed="false"
              onClick={<span class="fstat-no" title="function not covered" >() =&gt; {/* يمكن إضافة وظيفة تصفية هنا */}</span>}
            &gt;
              {currentLanguage === 'ar' ? 'العروض' : 'On Sale'}
            &lt;/Button&gt;
          &lt;/div&gt;
&nbsp;
          {/* عرض المنتجات مع حالة التحميل */}
          {isLoadingProducts ? (
            // حالة التحميل - عرض بطاقات نبضية
<span class="branch-0 cbranch-no" title="branch not covered" >            &lt;div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"&gt;</span>
              {[...Array(4)].map(<span class="fstat-no" title="function not covered" >(_</span>, index) =&gt; (
<span class="cstat-no" title="statement not covered" >                &lt;Card key={index} c</span>lassName="flex flex-col h-full overflow-hidden bg-white dark:bg-slate-800 shadow-lg"&gt;
                  &lt;div className="relative w-full aspect-square bg-slate-200 dark:bg-slate-700 animate-pulse"&gt;&lt;/div&gt;
                  &lt;div className="p-4 flex flex-col flex-grow"&gt;
                    &lt;div className="h-6 bg-slate-200 dark:bg-slate-700 rounded mb-2 animate-pulse w-3/4"&gt;&lt;/div&gt;
                    &lt;div className="h-4 bg-slate-200 dark:bg-slate-700 rounded mb-4 animate-pulse w-1/2"&gt;&lt;/div&gt;
                    &lt;div className="h-8 bg-slate-200 dark:bg-slate-700 rounded animate-pulse w-full mt-auto"&gt;&lt;/div&gt;
                  &lt;/div&gt;
                &lt;/Card&gt;
              ))}
            &lt;/div&gt;
          ) : featuredProducts.length &gt; 0 ? (
            // حالة وجود منتجات مميزة
<span class="branch-0 cbranch-no" title="branch not covered" >            &lt;&gt;</span>
              &lt;div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"&gt;
                {featuredProducts.map(<span class="fstat-no" title="function not covered" >(p</span>roduct, index) =&gt; (
<span class="cstat-no" title="statement not covered" >                  &lt;div key={product.id} c</span>lassName="group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full"&gt;
                    {/* شارة المنتج المميز */}
                    &lt;div className="absolute top-3 left-3 z-10"&gt;
                      &lt;span className="bg-amber-500 text-white text-xs font-bold px-2.5 py-1 rounded-full"&gt;
                        {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                      &lt;/span&gt;
                    &lt;/div&gt;
&nbsp;
                    {/* شارة الخصم (إذا كان هناك خصم) */}
                    {product.compareAtPrice &amp;&amp; product.compareAtPrice &gt; product.price &amp;&amp; (
                      &lt;div className="absolute top-3 right-3 z-10"&gt;
                        &lt;span className="bg-red-500 text-white text-xs font-bold px-2.5 py-1 rounded-full"&gt;
                          {`${product.discount}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        &lt;/span&gt;
                      &lt;/div&gt;
                    )}
&nbsp;
                    {/* صورة المنتج مع رابط للتفاصيل */}
                    &lt;Link href={`/${currentLanguage}/shop/${product.slug}`} className="block relative overflow-hidden"&gt;
                      &lt;div className="relative w-full aspect-square overflow-hidden"&gt;
                        &lt;EnhancedImage
                          src={product.images &amp;&amp; product.images.length &gt; 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                          alt={product.name}
                          fill={true}
                          objectFit="cover"
                          progressive={true}
                          placeholder="shimmer"
                          className="transition-transform duration-500 group-hover:scale-105"
                          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                          priority={index &lt; 4}
                        /&gt;
                      &lt;/div&gt;
&nbsp;
                      {/* أزرار الإجراءات السريعة */}
                      &lt;div className="absolute top-12 right-3 flex flex-col gap-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"&gt;
                        {/* زر المفضلة */}
                        &lt;Button
                          variant="icon"
                          size="sm"
                          className={`p-2 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110 ${
                            useWishlistStore.getState().isInWishlist(product.id)
                              ? "bg-primary-500 text-white"
                              : "bg-white text-slate-700 dark:bg-slate-700 dark:text-white"
                          }`}
                          onClick={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                            e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >                            e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >                            handleAddToWishlist(product);</span>
                          }}
                          aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}
                        &gt;
                          &lt;Heart
                            className={`h-5 w-5 ${useWishlistStore.getState().isInWishlist(product.id) &amp;&amp; "fill-current"}`}
                          /&gt;
                        &lt;/Button&gt;
&nbsp;
                        {/* زر العرض السريع */}
                        &lt;Button
                          variant="icon"
                          size="sm"
                          className="p-2 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110"
                          onClick={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                            e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >                            e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >                            handleQuickView(product);</span>
                          }}
                          aria-label={currentLanguage === 'ar' ? 'عرض سريع' : 'Quick view'}
                        &gt;
                          &lt;Eye className="h-5 w-5" /&gt;
                        &lt;/Button&gt;
                      &lt;/div&gt;
                    &lt;/Link&gt;
&nbsp;
                    {/* معلومات المنتج */}
                    &lt;div className="p-4 flex flex-col flex-grow"&gt;
                      {/* التصنيف والتقييم */}
                      &lt;div className="flex items-center justify-between mb-2"&gt;
                        {<span class="branch-0 cbranch-no" title="branch not covered" >product.category </span>&amp;&amp; (
                          &lt;span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full"&gt;
                            {product.category}
                          &lt;/span&gt;
                        )}
&nbsp;
                        &lt;div className="flex items-center text-sm text-slate-500 dark:text-slate-400"&gt;
                          &lt;Star className={`h-4 w-4 text-yellow-400 ${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'}`} /&gt;
                          &lt;span&gt;
                            {product.rating?.toFixed(1) ?? 'N/A'}
                            {product.reviewCount ? `(${product.reviewCount})` : ''}
                          &lt;/span&gt;
                        &lt;/div&gt;
                      &lt;/div&gt;
&nbsp;
                      {/* اسم المنتج */}
                      &lt;Link href={`/${currentLanguage}/shop/${product.slug}`} className="block"&gt;
                        &lt;h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1"&gt;
                          {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                        &lt;/h3&gt;
                      &lt;/Link&gt;
&nbsp;
                      {/* وصف المنتج */}
                      &lt;p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm"&gt;
                        {currentLanguage === 'ar'
                          ? (product.description_ar || product.description)
                          : product.description}
                      &lt;/p&gt;
&nbsp;
                      {/* السعر والمخزون */}
                      &lt;div className="flex items-center justify-between mb-3 mt-auto"&gt;
                        &lt;div className="flex items-baseline gap-1"&gt;
                          &lt;span className="text-lg font-bold text-slate-900 dark:text-white"&gt;
                            {formatCurrency(product.price)}
                          &lt;/span&gt;
                          {product.compareAtPrice &amp;&amp; product.compareAtPrice &gt; product.price &amp;&amp; (
                            &lt;span className="text-sm text-slate-500 line-through"&gt;
                              {formatCurrency(product.compareAtPrice)}
                            &lt;/span&gt;
                          )}
                        &lt;/div&gt;
&nbsp;
                        &lt;div className="text-xs font-medium"&gt;
                          {product.stock &gt; 0 ? (
<span class="branch-0 cbranch-no" title="branch not covered" >                            &lt;span className="text-green-600 dark:text-green-400"&gt;</span>
                              {currentLanguage === 'ar' ? 'متوفر' : 'In Stock'}
                            &lt;/span&gt;
                          ) : (
                            &lt;span className="text-red-600 dark:text-red-400"&gt;
                              {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}
                            &lt;/span&gt;
                          )}
                        &lt;/div&gt;
                      &lt;/div&gt;
&nbsp;
                      {/* أزرار الإجراءات */}
                      &lt;div className="flex gap-2"&gt;
                        {/* زر إضافة إلى السلة */}
                        &lt;Button
                          variant="primary"
                          size="sm"
                          className="flex-1 rounded-md"
                          onClick={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                            e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >                            e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >                            handleAddToCart(product);</span>
                          }}
                          disabled={product.stock &lt;= 0}
                          aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to cart'}
                        &gt;
                          &lt;ShoppingCart className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} /&gt;
                          &lt;span&gt;{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}&lt;/span&gt;
                        &lt;/Button&gt;
&nbsp;
                        {/* زر طلب عرض سعر للجملة */}
                        &lt;Button
                          variant="outline"
                          size="sm"
                          className="rounded-md flex items-center gap-1"
                          onClick={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                            e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >                            e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >                            setSelectedProduct(product);</span>
<span class="cstat-no" title="statement not covered" >                            setShowWholesaleForm(true);</span>
                          }}
                          aria-label={currentLanguage === 'ar' ? 'طلب عرض سعر جملة' : 'Request wholesale quote'}
                        &gt;
                          &lt;Users className="h-4 w-4" /&gt;
                          &lt;span&gt;{currentLanguage === 'ar' ? 'طلب سعر جملة' : 'Wholesale Quote'}&lt;/span&gt;
                        &lt;/Button&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;
                ))}
              &lt;/div&gt;
&nbsp;
              {/* نافذة العرض السريع للمنتج */}
              {showQuickView &amp;&amp; selectedProduct &amp;&amp; (
                &lt;QuickView
                  product={selectedProduct}
                  isOpen={showQuickView}
                  onClose={<span class="fstat-no" title="function not covered" >() =&gt; <span class="cstat-no" title="statement not covered" >s</span>etShowQuickView(false)}</span>
                /&gt;
              )}
&nbsp;
              {/* نموذج طلب عرض سعر للجملة */}
              {showWholesaleForm &amp;&amp; selectedProduct &amp;&amp; (
                &lt;div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50"&gt;
                  &lt;div className="max-w-2xl w-full"&gt;
                    &lt;WholesaleQuoteForm
                      onClose={<span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                        setShowWholesaleForm(false);</span>
<span class="cstat-no" title="statement not covered" >                        setSelectedProduct(null);</span>
                      }}
                      isCustomProduct={false}
                      selectedProduct={selectedProduct}
                    /&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              )}
            &lt;/&gt;
          ) : (
            // حالة عدم وجود منتجات مميزة
            &lt;div className="text-center py-12 bg-slate-50 dark:bg-slate-800/30 rounded-lg"&gt;
              &lt;Package className="w-12 h-12 mx-auto text-slate-400 dark:text-slate-500 mb-4" /&gt;
              &lt;p className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2"&gt;
                {currentLanguage === 'ar' ? 'لا توجد منتجات مميزة حالياً' : 'No featured products available'}
              &lt;/p&gt;
              &lt;p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6"&gt;
                {currentLanguage === 'ar'
                  ? 'لم يتم العثور على منتجات مميزة. يرجى التحقق مرة أخرى لاحقاً أو تصفح جميع المنتجات.'
                  : 'No featured products found. Please check back later or browse all products.'}
              &lt;/p&gt;
              &lt;Link href={`/${currentLanguage}/shop`}&gt;
                &lt;Button variant="primary"&gt;
                  {currentLanguage === 'ar' ? 'تصفح جميع المنتجات' : 'Browse all products'}
                &lt;/Button&gt;
              &lt;/Link&gt;
            &lt;/div&gt;
          )}
&nbsp;
          {/* زر عرض جميع المنتجات */}
          {<span class="branch-0 cbranch-no" title="branch not covered" >featuredProducts.length &gt; 0 &amp;&amp; (</span>
            &lt;div className="mt-12 text-center"&gt;
              &lt;Link href={`/${currentLanguage}/shop?featured=true`}&gt;
                &lt;Button
                  variant="outline"
                  size="lg"
                  className="px-8 shadow-sm hover:shadow transition-shadow group"
                  aria-label={currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة في المتجر' : 'View all featured products in shop'}
                &gt;
                  {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}
                  &lt;ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} /&gt;
                &lt;/Button&gt;
              &lt;/Link&gt;
            &lt;/div&gt;
          )}
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
&nbsp;
&nbsp;
      {/* أحدث خطوط الإنتاج - Latest Production Lines */}
      &lt;section className="py-16 md:py-24 bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800" dir={direction}&gt;
        &lt;div className="container-custom"&gt;
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          &lt;div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12"&gt;
            &lt;div className="relative"&gt;
              {/* شارة القسم */}
              &lt;div className="inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-4 shadow-sm"&gt;
                &lt;Factory className="w-4 h-4 mr-2 text-accent-500" /&gt;
                {currentLanguage === 'ar' ? 'تقنيات متطورة' : 'Advanced Technology'}
              &lt;/div&gt;
&nbsp;
              &lt;h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white"&gt;
                {currentLanguage === 'ar' ? 'أحدث خطوط الإنتاج' : 'Latest Production Lines'}
              &lt;/h2&gt;
              &lt;p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl"&gt;
                {currentLanguage === 'ar'
                  ? 'استكشف أحدث خطوط الإنتاج المتطورة لتعزيز كفاءة عملياتك الصناعية وزيادة الإنتاجية.'
                  : 'Explore our cutting-edge production lines to enhance your industrial operations and increase productivity.'}
              &lt;/p&gt;
            &lt;/div&gt;
            &lt;div className="flex flex-wrap items-center gap-4 mt-6 md:mt-0"&gt;
              &lt;Link href={`/${currentLanguage}/production-lines`}&gt;
                &lt;Button
                  variant="outline"
                  className="flex items-center gap-2 shadow-sm hover:shadow transition-shadow"
                &gt;
                  {currentLanguage === 'ar' ? 'عرض جميع خطوط الإنتاج' : 'View All Production Lines'}
                  &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                &lt;/Button&gt;
              &lt;/Link&gt;
              &lt;Link href={`/${currentLanguage}/contact?subject=production-line-inquiry`}&gt;
                &lt;Button
                  variant="primary"
                  className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                &gt;
                  {currentLanguage === 'ar' ? 'طلب خط إنتاج مخصص' : 'Request Custom Line'}
                  &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                &lt;/Button&gt;
              &lt;/Link&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* عرض خطوط الإنتاج */}
          &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"&gt;
            {productionLines.slice(0, 3).map(<span class="fstat-no" title="function not covered" >(l</span>ine) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;div key={line.id} c</span>lassName="group relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full"&gt;
                {/* شارة الفئة */}
                &lt;div className="absolute top-4 left-4 z-10 bg-primary-500/90 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm"&gt;
                  {line.category}
                &lt;/div&gt;
&nbsp;
                {/* صورة خط الإنتاج */}
                &lt;div className="relative aspect-video overflow-hidden"&gt;
                  &lt;EnhancedImage
                    src={line.images[0]}
                    alt={line.name}
                    fill={true}
                    objectFit="cover"
                    progressive={true}
                    placeholder="shimmer"
                    className="transition-transform duration-500 group-hover:scale-105"
                    containerClassName="w-full h-full"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  /&gt;
                  &lt;div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0" /&gt;
                &lt;/div&gt;
&nbsp;
                &lt;div className="p-6 flex-grow flex flex-col"&gt;
                  {/* اسم خط الإنتاج */}
                  &lt;Link href={`/${currentLanguage}/production-lines/${line.slug}`} className="block"&gt;
                    &lt;h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors"&gt;
                      {line.name}
                    &lt;/h3&gt;
                  &lt;/Link&gt;
&nbsp;
                  {/* وصف خط الإنتاج */}
                  &lt;p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2"&gt;
                    {line.description}
                  &lt;/p&gt;
&nbsp;
                  {/* سعة الإنتاج */}
                  &lt;div className="flex items-center text-sm text-slate-700 dark:text-slate-300 mb-4"&gt;
                    &lt;Gauge className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0 w-4 h-4 text-primary-500`} /&gt;
                    &lt;span&gt;
                      {currentLanguage === 'ar' ? `سعة الإنتاج: ${line.capacity}` : `Capacity: ${line.capacity}`}
                    &lt;/span&gt;
                  &lt;/div&gt;
&nbsp;
                  {/* المميزات الرئيسية */}
                  &lt;div className="space-y-2 mb-6"&gt;
                    {line.features.slice(0, 3).map(<span class="fstat-no" title="function not covered" >(f</span>eature, index) =&gt; (
<span class="cstat-no" title="statement not covered" >                      &lt;div key={index} c</span>lassName="flex items-start text-sm"&gt;
                        &lt;div className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} mt-0.5 text-accent-500`}&gt;
                          &lt;CheckCircle className="w-4 h-4" /&gt;
                        &lt;/div&gt;
                        &lt;span className="text-slate-600 dark:text-slate-300"&gt;{feature}&lt;/span&gt;
                      &lt;/div&gt;
                    ))}
                  &lt;/div&gt;
&nbsp;
                  {/* أزرار الإجراءات */}
                  &lt;div className="mt-auto space-y-3"&gt;
                    &lt;Link href={`/${currentLanguage}/production-lines/${line.slug}`} className="block w-full"&gt;
                      &lt;Button
                        variant="outline"
                        className="w-full flex items-center justify-center gap-2 group"
                      &gt;
                        &lt;span&gt;{currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'}&lt;/span&gt;
                        &lt;ArrowRight className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                      &lt;/Button&gt;
                    &lt;/Link&gt;
                    &lt;Link href={`/${currentLanguage}/production-lines/${line.slug}?action=quote`} className="block w-full"&gt;
                      &lt;Button
                        variant="primary"
                        className="w-full flex items-center justify-center gap-2"
                      &gt;
                        {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
                      &lt;/Button&gt;
                    &lt;/Link&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            ))}
          &lt;/div&gt;
&nbsp;
          {/* زر عرض جميع خطوط الإنتاج */}
          &lt;div className="mt-12 text-center"&gt;
            &lt;Link href={`/${currentLanguage}/production-lines`}&gt;
              &lt;Button
                variant="outline"
                size="lg"
                className="px-8 shadow-sm hover:shadow transition-shadow group"
              &gt;
                {currentLanguage === 'ar' ? 'استكشاف جميع خطوط الإنتاج' : 'Explore All Production Lines'}
                &lt;ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} /&gt;
              &lt;/Button&gt;
            &lt;/Link&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
      {/* تصفية المخزون وأفضل العروض */}
      &lt;section className="py-16 md:py-24 bg-gradient-to-b from-background to-primary-50" dir={direction}&gt;
        &lt;div className="container-custom"&gt;
          &lt;div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12"&gt;
            &lt;div&gt;
              &lt;h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4"&gt;
                {language === 'ar' ? 'تصفية المخزون بأسعار زهيدة' : 'Clearance Stock at Low Prices'}
              &lt;/h2&gt;
              &lt;p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl"&gt;
                {language === 'ar'
                  ? 'فرصة ذهبية للحصول على منتجات بكميات كبيرة وبأسعار مخفضة. مثالية للشركات التي تبحث عن توفير التكاليف.'
                  : 'Golden opportunity to get products in bulk quantities at discounted prices. Perfect for businesses looking to save costs.'}
              &lt;/p&gt;
            &lt;/div&gt;
            &lt;div className="flex items-center gap-4 mt-6 md:mt-0"&gt;
              &lt;Link href={`/${currentLanguage}/clearance`}&gt;
                &lt;Button
                  variant="primary"
                  className="flex items-center gap-2"
                &gt;
                  {currentLanguage === 'ar' ? 'عرض جميع منتجات التصفية' : 'View All Clearance Items'}
                  &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                &lt;/Button&gt;
              &lt;/Link&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"&gt;
            {clearanceItems.slice(0, 3).map(<span class="fstat-no" title="function not covered" >(i</span>tem: ClearanceItem) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;div key={item.id} c</span>lassName="group relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"&gt;
                {/* شارة الخصم */}
                &lt;div className="absolute top-4 left-4 z-10 bg-error-500 text-white px-3 py-1 rounded-full text-sm font-bold"&gt;
                  {currentLanguage === 'ar'
                    ? `خصم ${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}%`
                    : `${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}% OFF`}
                &lt;/div&gt;
&nbsp;
                {/* شارة تصفية المخزون */}
                &lt;div className="absolute top-4 right-4 z-10 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-bold"&gt;
                  {currentLanguage === 'ar' ? 'تصفية' : 'Clearance'}
                &lt;/div&gt;
&nbsp;
                {/* صورة المنتج */}
                &lt;div className="relative aspect-video overflow-hidden"&gt;
                  &lt;EnhancedImage
                    src={item.image}
                    alt={item.name}
                    fill={true}
                    objectFit="cover"
                    progressive={true}
                    placeholder="shimmer"
                    className="transition-transform duration-500 group-hover:scale-105"
                    containerClassName="w-full h-full"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  /&gt;
                  &lt;div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0" /&gt;
                &lt;/div&gt;
&nbsp;
                &lt;div className="p-6"&gt;
                  {/* تصنيف المنتج وحالته */}
                  &lt;div className="flex items-center justify-between mb-3"&gt;
                    &lt;span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300"&gt;
                      {item.category}
                    &lt;/span&gt;
                    &lt;span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${
                      item.condition === 'new' ? 'bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300' :
                      item.condition === 'like-new' ? 'bg-info-50 dark:bg-info-900/30 text-info-700 dark:text-info-300' :
                      'bg-warning-50 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300'
                    }`}&gt;
                      {currentLanguage === 'ar'
                        ? (item.condition === 'new' ? 'جديد' : item.condition === 'like-new' ? 'كالجديد' : 'مستعمل')
                        : item.condition}
                    &lt;/span&gt;
                  &lt;/div&gt;
&nbsp;
                  {/* اسم المنتج */}
                  &lt;Link
                    href={`/${currentLanguage}/clearance/${item.id}`}
                    className="block"
                  &gt;
                    &lt;h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors"&gt;
                      {item.name}
                    &lt;/h3&gt;
                  &lt;/Link&gt;
&nbsp;
                  {/* وصف المنتج */}
                  &lt;p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2"&gt;
                    {item.description}
                  &lt;/p&gt;
&nbsp;
                  {/* معلومات إضافية */}
                  &lt;div className="space-y-2 mb-4"&gt;
                    &lt;div className="flex items-center text-sm text-slate-600 dark:text-slate-400"&gt;
                      &lt;Package size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} /&gt;
                      {currentLanguage === 'ar'
                        ? `الحد الأدنى للطلب: ${item.minOrder} وحدة`
                        : `Min. Order: ${item.minOrder} units`}
                    &lt;/div&gt;
                    &lt;div className="flex items-center text-sm text-slate-600 dark:text-slate-400"&gt;
                      &lt;MapPin size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} /&gt;
                      {item.location}
                    &lt;/div&gt;
                  &lt;/div&gt;
&nbsp;
                  {/* السعر والكمية المتوفرة */}
                  &lt;div className="flex items-end justify-between mb-4"&gt;
                    &lt;div&gt;
                      &lt;div className="text-2xl font-bold text-slate-900 dark:text-white"&gt;
                        {formatCurrency(item.clearancePrice)}
                      &lt;/div&gt;
                      &lt;div className="text-sm text-slate-500 dark:text-slate-400 line-through"&gt;
                        {formatCurrency(item.originalPrice)}
                      &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div className="text-right"&gt;
                      &lt;div className="text-xs text-slate-600 dark:text-slate-400"&gt;
                        {currentLanguage === 'ar' ? 'الكمية المتوفرة:' : 'Available:'}
                      &lt;/div&gt;
                      &lt;div className="font-medium text-slate-900 dark:text-white"&gt;
                        {currentLanguage === 'ar'
                          ? `${item.availableQuantity} وحدة`
                          : `${item.availableQuantity} units`}
                      &lt;/div&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;
&nbsp;
                  {/* أزرار الإجراءات */}
                  &lt;div className="space-y-2"&gt;
                    &lt;Button
                      variant="primary"
                      className="w-full flex items-center justify-center gap-2"
                      onClick={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                        e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >                        setSelectedClearanceItem(item);</span>
<span class="cstat-no" title="statement not covered" >                        setShowClearanceQuoteForm(true);</span>
                      }}
                    &gt;
                      &lt;Send size={16} /&gt;
                      {currentLanguage === 'ar' ? 'طلب عرض سعر للكمية' : 'Request Bulk Quote'}
                    &lt;/Button&gt;
                    &lt;Link
                      href={`/${currentLanguage}/clearance/${item.id}`}
                      className="block text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                    &gt;
                      {currentLanguage === 'ar' ? 'عرض التفاصيل الكاملة' : 'View Full Details'}
                    &lt;/Link&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            ))}
          &lt;/div&gt;
&nbsp;
          &lt;div className="mt-12 text-center"&gt;
            &lt;Link href={`/${currentLanguage}/clearance`}&gt;
              &lt;Button
                variant="outline"
                size="lg"
                className="px-8 shadow-sm hover:shadow transition-shadow group"
              &gt;
                {currentLanguage === 'ar' ? 'عرض جميع منتجات التصفية' : 'View All Clearance Items'}
                &lt;ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} /&gt;
              &lt;/Button&gt;
            &lt;/Link&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
      {/* نموذج طلب عرض سعر لمنتجات التصفية */}
      {showClearanceQuoteForm &amp;&amp; selectedClearanceItem &amp;&amp; (
        &lt;div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50"&gt;
          &lt;div className="max-w-2xl w-full"&gt;
            &lt;Card className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-xl"&gt;
              &lt;div className="flex justify-between items-center mb-6"&gt;
                &lt;h3 className="text-xl font-semibold text-slate-900 dark:text-white"&gt;
                  {currentLanguage === 'ar'
                    ? `طلب عرض سعر لمنتج التصفية: ${selectedClearanceItem.name}`
                    : `Request Quote for Clearance Item: ${selectedClearanceItem.name}`}
                &lt;/h3&gt;
                &lt;Button
                  variant="ghost"
                  size="icon"
                  onClick={<span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    setShowClearanceQuoteForm(false);</span>
<span class="cstat-no" title="statement not covered" >                    setSelectedClearanceItem(null);</span>
                  }}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                &gt;
                  &lt;X size={20} /&gt;
                &lt;/Button&gt;
              &lt;/div&gt;
&nbsp;
              &lt;WholesaleQuoteForm
                onClose={<span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  setShowClearanceQuoteForm(false);</span>
<span class="cstat-no" title="statement not covered" >                  setSelectedClearanceItem(null);</span>
                }}
                isCustomProduct={false}
                product={clearanceProductAdapter(selectedClearanceItem)}
              /&gt;
            &lt;/Card&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      )}
&nbsp;
      {/* Most Requested Services - قسم الخدمات الأكثر طلباً */}
      &lt;section className="py-20 md:py-28 bg-gradient-to-b from-white via-primary-50/30 to-primary-50 dark:from-slate-900 dark:via-slate-900/80 dark:to-primary-900/20" dir={direction}&gt;
        &lt;div className="container-custom"&gt;
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          &lt;div className="text-center mb-20 relative"&gt;
            {/* شارة القسم */}
            &lt;div className="inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-6 shadow-sm"&gt;
              &lt;Star className="w-4 h-4 mr-2 text-accent-500" /&gt;
              {language === 'ar' ? 'خدمات متميزة' : 'Premium Services'}
            &lt;/div&gt;
&nbsp;
            {/* العنوان الرئيسي مع تأثير التدرج */}
            &lt;h2 className="text-3xl md:text-5xl font-bold mb-6 leading-tight relative inline-block"&gt;
              &lt;span className="bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-400 dark:to-accent-400"&gt;
                {language === 'ar' ? 'الخدمات الأكثر طلباً' : 'Most Requested Services'}
              &lt;/span&gt;
              &lt;div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full transform scale-x-100"&gt;&lt;/div&gt;
            &lt;/h2&gt;
&nbsp;
            {/* الوصف مع تحسين الخط والمساحة */}
            &lt;p className="text-lg md:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed"&gt;
              {language === 'ar' ? 'اكتشف مجموعتنا الشاملة من خدمات الأعمال المصممة خصيصاً لتحسين عملياتك وتعزيز النمو وتحقيق النجاح المستدام.' : 'Discover our comprehensive range of business services tailored to optimize your operations, drive growth, and achieve sustainable success.'}
            &lt;/p&gt;
&nbsp;
            {/* زخرفة خلفية */}
            &lt;div className="absolute -top-10 left-1/2 transform -translate-x-1/2 w-64 h-64 bg-primary-500/5 dark:bg-primary-500/10 rounded-full blur-3xl -z-10"&gt;&lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* شبكة بطاقات الخدمات مع تباعد محسن */}
          &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10"&gt;
            {services.slice(0, 6).map(<span class="fstat-no" title="function not covered" >(s</span>ervice: Service, index: number) =&gt; {
              const Icon = <span class="cstat-no" title="statement not covered" >service.icon === 'Search' ? Search :</span>
                          service.icon === 'Package' ? Package :
                          service.icon === 'Truck' ? Truck :
                          service.icon === 'FileCheck' ? FileCheck :
                          service.icon === 'Users' ? Users :
                          service.icon === 'ClipboardList' ? FileText :
                          Building2;
&nbsp;
<span class="cstat-no" title="statement not covered" >              return (</span>
                &lt;div
                  key={service.id}
                &gt;
                  &lt;div className="group h-full perspective-1000"&gt;
                    &lt;Card className="relative overflow-hidden h-full hover:shadow-xl transition-all duration-500 border border-slate-200/80 dark:border-slate-700/50 hover:border-primary-200 dark:hover:border-primary-800/70 flex flex-col transform-gpu group-hover:translate-y-[-8px] group-hover:rotate-y-1"&gt;
                      {/* حدود متدرجة متحركة */}
                      &lt;div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-br from-primary-500/0 via-primary-500/0 to-accent-500/0 group-hover:from-primary-500/20 group-hover:via-primary-500/10 group-hover:to-accent-500/20 transition-colors duration-700 -z-10"&gt;&lt;/div&gt;
&nbsp;
                      {/* شريط علوي متدرج */}
                      &lt;div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-500 scale-x-0 group-hover:scale-x-100"&gt;&lt;/div&gt;
&nbsp;
                      {/* شارة الخدمة */}
                      &lt;div className="absolute top-6 right-6 bg-primary-50/90 dark:bg-primary-900/50 text-primary-600 dark:text-primary-300 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm shadow-sm"&gt;
                        {currentLanguage === 'ar' ? 'خدمة احترافية' : 'Professional Service'}
                      &lt;/div&gt;
&nbsp;
                      &lt;div className="p-8 flex-grow flex flex-col"&gt;
                        {/* أيقونة الخدمة مع خلفية متحركة */}
                        &lt;div className="relative mb-8 group-hover:scale-105 transition-transform duration-500"&gt;
                          &lt;div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-50 dark:from-primary-900/60 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-primary-600 dark:text-primary-400 transform transition-all duration-500 group-hover:shadow-lg group-hover:shadow-primary-500/10"&gt;
                            &lt;Icon size={36} className="transform transition-transform duration-700 group-hover:rotate-12" /&gt;
                          &lt;/div&gt;
                          &lt;div className="absolute -inset-2 bg-primary-200 dark:bg-primary-800/50 rounded-3xl -z-10 blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-700"&gt;&lt;/div&gt;
                        &lt;/div&gt;
&nbsp;
                        {/* عنوان الخدمة */}
                        &lt;h3 className="text-2xl font-bold mb-4 text-slate-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors"&gt;
                          {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
                        &lt;/h3&gt;
&nbsp;
                        {/* وصف الخدمة */}
                        &lt;p className="text-slate-600 dark:text-slate-300 mb-6 line-clamp-3 leading-relaxed"&gt;
                          {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}
                        &lt;/p&gt;
&nbsp;
                        {/* الميزات الرئيسية مع تحسين العرض */}
                        &lt;div className="space-y-3 mb-8 flex-grow"&gt;
                          {(currentLanguage === 'ar' ? service.features_ar || service.features : service.features).slice(0, 4).map(<span class="fstat-no" title="function not covered" >(f</span>eature: string, fIndex: number) =&gt; (
<span class="cstat-no" title="statement not covered" >                            &lt;div key={fIndex} c</span>lassName="flex items-start text-sm group/feature"&gt;
                              &lt;div className="mt-0.5 transform transition-transform duration-300 group-hover/feature:scale-110"&gt;
                                &lt;CheckCircle className={`w-5 h-5 text-accent-500 dark:text-accent-400 ${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`} /&gt;
                              &lt;/div&gt;
                              &lt;span className="text-slate-700 dark:text-slate-300 group-hover/feature:text-primary-700 dark:group-hover/feature:text-primary-300 transition-colors"&gt;{feature}&lt;/span&gt;
                            &lt;/div&gt;
                          ))}
                        &lt;/div&gt;
&nbsp;
                        {/* أزرار الإجراءات مع تحسين التفاعل */}
                        &lt;div className="mt-auto"&gt;
                          &lt;Link href={`/${currentLanguage}/services/${service.slug}`} className="block w-full"&gt;
                            &lt;Button
                              variant="primary"
                              className="w-full flex items-center justify-center gap-2 group-hover:shadow-md transition-all duration-300 group-hover:shadow-primary-500/20 py-3"
                            &gt;
                              {currentLanguage === 'ar' ? 'احجز الخدمة الآن' : 'Book This Service'}
                              &lt;ArrowRight className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} /&gt;
                            &lt;/Button&gt;
                          &lt;/Link&gt;
                        &lt;/div&gt;
                      &lt;/div&gt;
&nbsp;
                      {/* شريط سفلي متدرج */}
                      &lt;div className="absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-500 scale-x-0 group-hover:scale-x-100"&gt;&lt;/div&gt;
                    &lt;/Card&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              );
            })}
          &lt;/div&gt;
&nbsp;
          {/* زر عرض جميع الخدمات مع تحسين التصميم */}
          &lt;div className="mt-20 text-center"&gt;
            &lt;Link href={`/${currentLanguage}/services`}&gt;
              &lt;Button
                variant="outline"
                size="lg"
                className="px-10 py-6 text-lg border-2 hover:border-primary-500 dark:hover:border-primary-400 group shadow-sm hover:shadow-md transition-shadow"
              &gt;
                {currentLanguage === 'ar' ? 'استكشاف جميع خدماتنا الاحترافية' : 'Explore All Our Professional Services'}
                &lt;ArrowRight className={`${currentLanguage === 'ar' ? 'mr-3' : 'ml-3'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-2 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} /&gt;
              &lt;/Button&gt;
            &lt;/Link&gt;
          &lt;/div&gt;
&nbsp;
&nbsp;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
      {/* CTA Section - قسم الدعوة للعمل المحسن */}
      &lt;section className="relative py-12 md:py-16 overflow-hidden" dir={direction}&gt;
        {/* خلفية متدرجة مع تأثيرات */}
        &lt;div className="absolute inset-0 bg-gradient-to-br from-primary-800 via-primary-700 to-secondary-800 z-0"&gt;&lt;/div&gt;
&nbsp;
        {/* طبقة التراكب مع تأثير الشبكة */}
        &lt;div className="absolute inset-0 bg-black/20 z-10"&gt;&lt;/div&gt;
        &lt;div className="absolute inset-0 z-10 opacity-10"&gt;
          &lt;div className="absolute top-0 left-0 w-full h-full"&gt;
            &lt;svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none"&gt;
              &lt;defs&gt;
                &lt;pattern id="cta-grid" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"&gt;
                  &lt;path d="M10 0H0V10" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" /&gt;
                &lt;/pattern&gt;
              &lt;/defs&gt;
              &lt;rect x="0" y="0" width="100" height="100" fill="url(#cta-grid)" /&gt;
            &lt;/svg&gt;
          &lt;/div&gt;
        &lt;/div&gt;
&nbsp;
        {/* المحتوى الرئيسي */}
        &lt;div className="container-custom relative z-20"&gt;
          &lt;div className="max-w-4xl mx-auto"&gt;
            {/* بطاقة محتوى CTA مع تأثيرات زجاجية */}
            &lt;div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 md:p-8 border border-white/20 shadow-xl relative overflow-hidden"&gt;
              {/* تأثير التوهج */}
              &lt;div className="absolute -top-20 -left-20 w-40 h-40 bg-accent-500/30 rounded-full blur-3xl"&gt;&lt;/div&gt;
              &lt;div className="absolute -bottom-20 -right-20 w-40 h-40 bg-primary-500/30 rounded-full blur-3xl"&gt;&lt;/div&gt;
&nbsp;
              &lt;div className="text-center relative"&gt;
                {/* العنوان الرئيسي مع تأثير متدرج */}
                &lt;h2 className="text-3xl md:text-4xl font-bold mb-4 leading-tight"&gt;
                  &lt;span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-100 to-white relative inline-block"&gt;
                    {language === 'ar' ? 'هل أنت مستعد لتحويل أعمالك؟' : 'Ready to Transform Your Business?'}
                  &lt;/span&gt;
                &lt;/h2&gt;
&nbsp;
                {/* وصف مع تأثيرات متحركة */}
                &lt;p className="text-base md:text-lg text-white/90 mb-6 max-w-2xl mx-auto"&gt;
                  {language === 'ar'
                    ? 'انضم إلى آلاف العملاء الراضين الذين رفعوا مستوى عملياتهم مع حلولنا الشاملة.'
                    : 'Join thousands of satisfied customers who have elevated their operations with our comprehensive solutions.'}
                &lt;/p&gt;
&nbsp;
                {/* أزرار الدعوة للعمل المحسنة */}
                &lt;div className="flex flex-wrap justify-center gap-4"&gt;
                  &lt;HeroButton
                    href={`/${currentLanguage}/shop`}
                    variant="accent"
                    className="py-2.5 px-6 shadow-lg hover:shadow-accent-500/30 transition-all duration-300 group"
                  &gt;
                    &lt;span className="relative z-10"&gt;{currentLanguage === 'ar' ? 'استكشف منتجاتنا' : 'Explore Our Products'}&lt;/span&gt;
                    &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} relative z-10 transition-transform duration-300 group-hover:translate-x-1`} /&gt;
                  &lt;/HeroButton&gt;
                  &lt;HeroButton
                    href={`/${currentLanguage}/contact`}
                    variant="outline"
                    className="py-2.5 px-6 border backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
                  &gt;
                    &lt;span className="relative z-10"&gt;{currentLanguage === 'ar' ? 'طلب استشارة' : 'Request Consultation'}&lt;/span&gt;
                  &lt;/HeroButton&gt;
                &lt;/div&gt;
&nbsp;
                {/* شارة الثقة */}
                &lt;div className="mt-6 inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm border border-white/20"&gt;
                  &lt;Shield className="w-4 h-4 text-accent-300 mr-1.5" /&gt;
                  &lt;span className="text-white/90 text-xs font-medium"&gt;
                    {currentLanguage === 'ar' ? 'ضمان الجودة 100%' : '100% Quality Guarantee'}
                  &lt;/span&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
      {/* Latest Industry Insights - قسم أحدث رؤى الصناعة */}
      &lt;section className="py-16 md:py-24 bg-gradient-to-b from-background to-slate-50 dark:from-slate-900 dark:to-slate-800/50" dir={direction}&gt;
        &lt;div className="container-custom"&gt;
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          &lt;div className="relative mb-16"&gt;
            &lt;div className="flex flex-col md:flex-row justify-between items-start md:items-center"&gt;
              &lt;div className="relative mb-8 md:mb-0"&gt;
                {/* شارة القسم */}
                &lt;div className="inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-4 shadow-sm"&gt;
                  &lt;FileText className="w-4 h-4 mr-2 text-accent-500" /&gt;
                  {currentLanguage === 'ar' ? 'رؤى وتحليلات صناعية' : 'Industry Insights'}
                &lt;/div&gt;
&nbsp;
                &lt;h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white"&gt;
                  {currentLanguage === 'ar' ? 'أحدث المقالات والتحليلات' : 'Latest Articles &amp; Analysis'}
                &lt;/h2&gt;
                &lt;p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl"&gt;
                  {currentLanguage === 'ar'
                    ? 'اكتشف أحدث الاتجاهات والرؤى في مجال الأعمال والصناعة من خبرائنا المتخصصين'
                    : 'Discover the latest trends and insights in business and industry from our expert specialists'}
                &lt;/p&gt;
&nbsp;
                {/* خط زخرفي */}
                &lt;div className="absolute -bottom-4 left-0 w-20 h-1 bg-accent-500 rounded-full"&gt;&lt;/div&gt;
              &lt;/div&gt;
&nbsp;
              &lt;div className="flex flex-wrap items-center gap-4"&gt;
                &lt;Link href={`/${currentLanguage}/blog`}&gt;
                  &lt;Button
                    variant="outline"
                    className="flex items-center gap-2 shadow-sm hover:shadow transition-shadow"
                  &gt;
                    {currentLanguage === 'ar' ? 'جميع المقالات' : 'All Articles'}
                    &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                  &lt;/Button&gt;
                &lt;/Link&gt;
                &lt;Link href={`/${currentLanguage}/blog/featured`}&gt;
                  &lt;Button
                    variant="primary"
                    className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                  &gt;
                    {currentLanguage === 'ar' ? 'المقالات المميزة' : 'Featured Articles'}
                    &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                  &lt;/Button&gt;
                &lt;/Link&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* عرض المقالات المميزة والأحدث - 4 مقالات في صف واحد */}
          &lt;div className="relative mt-8 mb-16"&gt;
            {/* خلفية زخرفية */}
            &lt;div className="absolute inset-0 -z-10 bg-gradient-to-r from-primary-50/30 to-accent-50/30 dark:from-primary-900/10 dark:to-accent-900/10 rounded-3xl transform -skew-y-1"&gt;&lt;/div&gt;
&nbsp;
            {/* شريط زخرفي */}
            &lt;div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"&gt;&lt;/div&gt;
&nbsp;
            {/* المقالات */}
            &lt;div className="py-10 px-4"&gt;
              &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8"&gt;
                {(<span class="fstat-no" title="function not covered" >() =&gt; {</span>
                  // تحضير المقالات المميزة
                  const featuredPosts = <span class="cstat-no" title="statement not covered" >blogPosts.filter(<span class="fstat-no" title="function not covered" >post =&gt; <span class="cstat-no" title="statement not covered" >p</span>ost.featured)</span>;</span>
&nbsp;
                  // تحضير المقالات الأحدث
                  const latestPosts = <span class="cstat-no" title="statement not covered" >blogPosts</span>
                    .sort(<span class="fstat-no" title="function not covered" >(a</span>, b) =&gt; <span class="cstat-no" title="statement not covered" >new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())</span>
                    .filter(<span class="fstat-no" title="function not covered" >post =&gt; <span class="cstat-no" title="statement not covered" >!</span>post.featured)</span>;
&nbsp;
                  // دمج المقالات المميزة والأحدث، مع الأولوية للمميزة
                  const postsToShow = <span class="cstat-no" title="statement not covered" >[...featuredPosts, ...latestPosts].slice(0, 4);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  return postsToShow.map(<span class="fstat-no" title="function not covered" >(p</span>ost, index) =&gt; {</span>
                    const isFeatured = <span class="cstat-no" title="statement not covered" >post.featured;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    return (</span>
                      &lt;div key={post.id} className="group relative"&gt;
                        {/* بطاقة المقال */}
                        &lt;div className="relative h-full bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-md group-hover:shadow-xl transition-all duration-500 flex flex-col border border-slate-200/50 dark:border-slate-700/50 transform group-hover:-translate-y-1 group-hover:scale-[1.01]"&gt;
                          {/* شارة المقال المميز */}
                          {<span class="branch-0 cbranch-no" title="branch not covered" >isFeatured &amp;&amp; (</span>
                            &lt;div className="absolute top-3 right-3 z-10"&gt;
                              &lt;span className="bg-accent-500 text-white text-xs font-bold px-2.5 py-1 rounded-full shadow-lg flex items-center"&gt;
                                &lt;Star className="w-3 h-3 mr-1" fill="white" /&gt;
                                {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                              &lt;/span&gt;
                            &lt;/div&gt;
                          )}
&nbsp;
                          {/* صورة المقال */}
                          &lt;Link href={`/${currentLanguage}/blog/${post.slug}`} className="block"&gt;
                            &lt;div className="relative aspect-[4/3] overflow-hidden"&gt;
                              &lt;EnhancedImage
                                src={post.coverImage}
                                alt={post.title}
                                fill={true}
                                objectFit="cover"
                                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                                progressive={true}
                                placeholder="shimmer"
                                className="transition-transform duration-700 group-hover:scale-105"
                              /&gt;
                              &lt;div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/10" /&gt;
&nbsp;
                              {/* فئة المقال */}
                              &lt;div className="absolute top-3 left-3"&gt;
                                &lt;span className="px-2.5 py-1 rounded-lg text-xs font-medium bg-white/90 backdrop-blur-sm text-primary-700 dark:text-primary-700 shadow-sm"&gt;
                                  {post.category}
                                &lt;/span&gt;
                              &lt;/div&gt;
&nbsp;
                              {/* تاريخ النشر */}
                              &lt;div className="absolute bottom-3 left-3 right-3 flex justify-between items-center"&gt;
                                &lt;div className="flex items-center gap-2 text-white text-xs bg-black/30 backdrop-blur-sm px-2.5 py-1 rounded-lg"&gt;
                                  &lt;Calendar className="w-3 h-3" /&gt;
                                  {new Date(post.publishedAt).toLocaleDateString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                  })}
                                &lt;/div&gt;
                                &lt;div className="flex items-center gap-1 text-white text-xs bg-black/30 backdrop-blur-sm px-2.5 py-1 rounded-lg"&gt;
                                  &lt;Clock className="w-3 h-3" /&gt;
                                  {post.readTime}
                                &lt;/div&gt;
                              &lt;/div&gt;
                            &lt;/div&gt;
                          &lt;/Link&gt;
&nbsp;
                          {/* محتوى المقال */}
                          &lt;div className="p-5 flex-grow flex flex-col"&gt;
                            &lt;Link href={`/${currentLanguage}/blog/${post.slug}`} className="block group-hover:text-primary-600 dark:group-hover:text-primary-400"&gt;
                              &lt;h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3 transition-colors line-clamp-2"&gt;
                                {post.title}
                              &lt;/h3&gt;
                            &lt;/Link&gt;
&nbsp;
                            &lt;p className="text-slate-600 dark:text-slate-400 mb-4 line-clamp-2 text-sm"&gt;
                              {post.excerpt}
                            &lt;/p&gt;
&nbsp;
                            {/* معلومات الكاتب */}
                            &lt;div className="mt-auto pt-4 border-t border-slate-100 dark:border-slate-700/50 flex items-center justify-between"&gt;
                              &lt;div className="flex items-center gap-2"&gt;
                                &lt;div className="w-8 h-8 rounded-full overflow-hidden border-2 border-white dark:border-slate-700 shadow-sm"&gt;
                                  &lt;EnhancedImage
                                    src={post.authorImage}
                                    alt={post.author}
                                    fill={true}
                                    objectFit="cover"
                                    rounded="full"
                                    progressive={true}
                                    placeholder="blur"
                                    className="w-full h-full"
                                    containerClassName="w-full h-full"
                                  /&gt;
                                &lt;/div&gt;
                                &lt;div&gt;
                                  &lt;p className="text-xs font-medium text-slate-900 dark:text-white line-clamp-1"&gt;
                                    {post.author}
                                  &lt;/p&gt;
                                  &lt;p className="text-xs text-slate-500 dark:text-slate-400"&gt;
                                    {post.authorTitle.split(' ')[0]}
                                  &lt;/p&gt;
                                &lt;/div&gt;
                              &lt;/div&gt;
&nbsp;
                              {/* زر قراءة المزيد */}
                              &lt;Link href={`/${currentLanguage}/blog/${post.slug}`} className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 transition-transform group-hover:scale-110"&gt;
                                &lt;ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} /&gt;
                              &lt;/Link&gt;
                            &lt;/div&gt;
                          &lt;/div&gt;
                        &lt;/div&gt;
&nbsp;
                        {/* تأثير الظل */}
                        &lt;div className="absolute -bottom-2 left-2 right-2 h-full rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 blur-xl -z-10 opacity-0 group-hover:opacity-100 transition-opacity"&gt;&lt;/div&gt;
                      &lt;/div&gt;
                    );
                  });
                })()}
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          &lt;div className="mt-12 text-center"&gt;
            &lt;Link href={`/${currentLanguage}/blog`}&gt;
              &lt;Button
                variant="outline"
                size="lg"
                className="px-8 shadow-sm hover:shadow transition-shadow group"
              &gt;
                {currentLanguage === 'ar' ? 'استكشاف جميع المقالات' : 'Explore All Articles'}
                &lt;ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} /&gt;
              &lt;/Button&gt;
            &lt;/Link&gt;
          &lt;/div&gt;
&nbsp;
          {/* Newsletter subscription - قسم الاشتراك بالنشرة البريدية */}
          &lt;div className="mt-20 relative"&gt;
            &lt;div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl opacity-90"&gt;&lt;/div&gt;
            &lt;div className="absolute inset-0 bg-[url('/images/pattern-dots.svg')] bg-repeat opacity-10"&gt;&lt;/div&gt;
&nbsp;
            {/* تأثير الزخرفة */}
            &lt;div className="absolute -top-5 -left-5 w-20 h-20 bg-primary-300 rounded-full blur-3xl opacity-30"&gt;&lt;/div&gt;
            &lt;div className="absolute -bottom-5 -right-5 w-20 h-20 bg-accent-300 rounded-full blur-3xl opacity-30"&gt;&lt;/div&gt;
&nbsp;
            &lt;div className="relative p-8 md:p-12 rounded-2xl overflow-hidden"&gt;
              &lt;div className="max-w-4xl mx-auto"&gt;
                &lt;div className="flex flex-col md:flex-row items-center justify-between gap-8"&gt;
                  &lt;div className="text-white text-center md:text-left md:max-w-md"&gt;
                    &lt;div className="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm text-white text-sm font-medium mb-4"&gt;
                      &lt;Bell className="w-4 h-4 mr-2" /&gt;
                      {currentLanguage === 'ar' ? 'ابق على اطلاع' : 'Stay Updated'}
                    &lt;/div&gt;
                    &lt;h3 className="text-2xl md:text-3xl font-bold mb-4"&gt;
                      {currentLanguage === 'ar'
                        ? 'اشترك في نشرتنا الإخبارية للحصول على آخر التحديثات'
                        : 'Subscribe to Our Newsletter for Latest Updates'}
                    &lt;/h3&gt;
                    &lt;p className="text-white/80 text-base md:text-lg"&gt;
                      {currentLanguage === 'ar'
                        ? 'احصل على آخر العروض والمنتجات الجديدة والتحديثات الصناعية مباشرة إلى بريدك الإلكتروني.'
                        : 'Get the latest offers, new products, and industry updates delivered directly to your inbox.'}
                    &lt;/p&gt;
                  &lt;/div&gt;
&nbsp;
                  &lt;div className="w-full md:w-auto bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 shadow-xl"&gt;
                    &lt;NewsletterForm
                      variant="inline"
                      className="w-full md:min-w-[400px]"
                    /&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/section&gt;
&nbsp;
      {<span class="branch-0 cbranch-no" title="branch not covered" >showWholesaleForm &amp;&amp; (</span>
        &lt;div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50"&gt;
          &lt;div className="max-w-2xl w-full"&gt;
            &lt;WholesaleQuoteForm
              onClose={<span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                setShowWholesaleForm(false);</span>
<span class="cstat-no" title="statement not covered" >                setSelectedProduct(null);</span>
<span class="cstat-no" title="statement not covered" >                setSelectedService(null);</span>
              }}
              isCustomProduct={false}
              product={selectedProduct}
              serviceName={selectedService?.name}
            /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      )}
&nbsp;
      {/* Quick View Modal */}
      &lt;QuickView
        product={selectedProduct}
        isOpen={showQuickView}
        onClose={<span class="fstat-no" title="function not covered" >() =&gt; <span class="cstat-no" title="statement not covered" >s</span>etShowQuickView(false)}</span>
      /&gt;
    &lt;/div&gt;
  );
}
&nbsp;
<span class="cstat-no" title="statement not covered" >export default <span class="cstat-no" title="statement not covered" >H</span>omePage;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-25T00:29:14.893Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    