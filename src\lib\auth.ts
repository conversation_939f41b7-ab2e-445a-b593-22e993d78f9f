import { sqlite } from './sqlite';
import { secureLocalStorage } from './encryption';
import { User } from '../types';
import { v4 as uuidv4 } from 'uuid'; 

// مفاتيح التخزين المحلي
const AUTH_TOKEN_KEY = 'auth-token';
const USER_DATA_KEY = 'user-data';
const SESSION_EXPIRY_KEY = 'session-expiry';
const CURRENT_USER_ID_KEY = 'current_user_id';

// مدة صلاحية الجلسة (بالمللي ثانية)
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 أيام

/**
 * وظيفة لتسجيل الدخول مع تشفير البيانات
 */
export async function signIn(email: string, password: string) {
  try {
    // Normalize email
    const normalizedEmail = email.toLowerCase().trim();

    // التحقق من صحة البريد الإلكتروني وكلمة المرور
    if (!normalizedEmail || !password) {
      console.error('Email and password are required');
      return { error: new Error('البريد الإلكتروني وكلمة المرور مطلوبان') };
    }

    // Check if account is locked
    const userRecord = await sqlite.getUserByEmail(normalizedEmail);
    if (userRecord?.lockout_until && new Date(userRecord.lockout_until) > new Date()) {
      return { error: new Error('الحساب موقوف مؤقتاً. يرجى المحاولة مرة أخرى لاحقاً') };
    }

    // Use SQLite.authenticateUser for authentication
    const authenticatedUser = await sqlite.authenticateUser(normalizedEmail, password);

    if (!authenticatedUser) {
      // Update failed login attempts
      if (userRecord) {
        const failedAttempts = (userRecord.failed_login_attempts || 0) + 1;
        const lockoutMinutes = Math.min(15 * failedAttempts, 120); // Maximum 2 hours lockout
        const lockoutUntil = new Date(Date.now() + lockoutMinutes * 60000).toISOString();

        await sqlite.updateUser(userRecord.id, {
          failed_login_attempts: failedAttempts,
          last_failed_login: new Date().toISOString(),
          lockout_until: lockoutUntil
        });
      }

      console.error('Sign in error: Invalid email or password');
      return { error: new Error('بريد إلكتروني أو كلمة مرور غير صالحة') };
    }

    // Reset failed attempts on successful login
    await sqlite.updateUser(authenticatedUser.id, {
      failed_login_attempts: 0,
      last_failed_login: null,
      lockout_until: null,
      last_login: new Date().toISOString()
    });

    // Create a secure session token
    const token = uuidv4();
    const sessionExpiry = Date.now() + SESSION_DURATION;

    // Store session data securely
    secureLocalStorage.setItem(AUTH_TOKEN_KEY, token, authenticatedUser.id);
    secureLocalStorage.setItem(USER_DATA_KEY, authenticatedUser, authenticatedUser.id);
    secureLocalStorage.setItem(SESSION_EXPIRY_KEY, sessionExpiry, authenticatedUser.id);

    // Update user's last login time in SQLite
    await sqlite.updateUser(authenticatedUser.id, {
      last_login: new Date().toISOString()
    });

    // Store current user ID
    localStorage.setItem(CURRENT_USER_ID_KEY, authenticatedUser.id);

    console.log('User signed in successfully via SQLite:', authenticatedUser.email, 'with role:', authenticatedUser.role);

    return { user: authenticatedUser, error: null };
  } catch (error) {
    console.error('Unexpected error during SQLite sign in:', error);
    // Ensure error is an instance of Error for consistency
    const err = error instanceof Error ? error : new Error(String(error));
    return { error: err };
  }
}

/**
 * وظيفة لتسجيل مستخدم جديد مع تشفير البيانات
 */
export async function signUp(email: string, password: string, userData: Partial<User>) {
  try {
    console.log('Signing up new user with SQLite:', email);

    // التحقق من صحة البريد الإلكتروني وكلمة المرور
    if (!email || !password) {
      console.error('Email and password are required');
      return { error: new Error('البريد الإلكتروني وكلمة المرور مطلوبان') };
    }

    // التحقق من طول كلمة المرور
    if (password.length < 6) {
      console.error('Password must be at least 6 characters long');
      return { error: new Error('يجب أن تكون كلمة المرور مكونة من 6 أحرف على الأقل') };
    }

    const combinedUserData = {
      ...userData,
      email,
      // id will be generated by sqlite.createUser
      // createdAt will be handled by sqlite.createUser
      // role can be part of userData or defaulted in createUser
    };

    // استخدام SQLite.createUser لإنشاء المستخدم
    // sqlite.createUser expects password separately for hashing
    const newUser = await sqlite.createUser(combinedUserData, password);

    if (!newUser) {
      console.error('Sign up error: Could not create user. Email might already exist.');
      return { error: new Error('فشل إنشاء المستخدم. قد يكون البريد الإلكتروني مستخدمًا بالفعل.') };
    }

    console.log('User created successfully with SQLite:', newUser.email);

    // بعد إنشاء المستخدم بنجاح، قم بتسجيل دخوله تلقائيًا
    const sessionExpiry = Date.now() + SESSION_DURATION;

    // تخزين رمز المصادقة
    secureLocalStorage.setItem(AUTH_TOKEN_KEY, `mock-sqlite-token-${Date.now()}`, newUser.id);

    // تخزين بيانات المستخدم
    secureLocalStorage.setItem(USER_DATA_KEY, newUser, newUser.id);

    // تخزين وقت انتهاء الجلسة
    secureLocalStorage.setItem(SESSION_EXPIRY_KEY, sessionExpiry, newUser.id);

    // Store current user ID for consistent key retrieval
    localStorage.setItem(CURRENT_USER_ID_KEY, newUser.id);

    console.log('User signed up and logged in successfully via SQLite:', newUser.email);

    return { user: newUser, error: null };

  } catch (error) {
    console.error('Unexpected error during SQLite sign up:', error);
    const err = error instanceof Error ? error : new Error(String(error));
    return { error: err };
  }
}

/**
 * وظيفة لتسجيل الخروج
 */
export async function signOut() {
  try {
    console.log('Signing out user');

    // احصل على معرّف المستخدم الحالي قبل مسحه
    const currentUserId = localStorage.getItem(CURRENT_USER_ID_KEY);

    // مسح التخزين المحلي
    localStorage.removeItem(CURRENT_USER_ID_KEY);
    
    // مسح التخزين الآمن باستخدام المفتاح الافتراضي للجهاز
    if (currentUserId) {
      console.log('Clearing secure storage for user ID:', currentUserId);
      secureLocalStorage.removeItem(AUTH_TOKEN_KEY); 
      secureLocalStorage.removeItem(USER_DATA_KEY); 
      secureLocalStorage.removeItem(SESSION_EXPIRY_KEY); 
    } else {
      // Fallback to device key if no user ID was found (should ideally not happen in a consistent state)
      secureLocalStorage.removeItem(AUTH_TOKEN_KEY);
      secureLocalStorage.removeItem(USER_DATA_KEY);
      secureLocalStorage.removeItem(SESSION_EXPIRY_KEY);
    }

    console.log('User signed out successfully and local storage cleared.');

    // Trigger a custom event or use a state management solution to update UI
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('auth-change', { detail: { signedIn: false } }));
    }

    return { error: null };
  } catch (error) {
    console.error('Error in signOut:', error);
    const err = error instanceof Error ? error : new Error(String(error));
    return { error: err };
  }
}

/**
 * وظيفة للتحقق من حالة المصادقة
 */
export function getAuthStatus() {
  if (typeof window === 'undefined') {
    console.log('getAuthStatus called in an environment without window object. Returning unauthenticated.');
    return { isAuthenticated: false, user: null };
  }

  try {
    console.log('Checking authentication status');
    const currentUserId = localStorage.getItem(CURRENT_USER_ID_KEY);

    if (!currentUserId) {
      // No active user session ID stored. Ensure any orphaned secure data is not accidentally used.
      // If there's no currentUserId, we assume no one is logged in or data is inconsistent.
      console.log('No current_user_id found in localStorage.');
      // Optional: Clear potentially orphaned secure storage items if necessary, though secureLocalStorage.getItem without userId uses a device key.
      return { isAuthenticated: false, user: null };
    }

    // Attempt to retrieve data using the stored user ID for the secret key
    const user = secureLocalStorage.getItem<User>(USER_DATA_KEY, currentUserId);
    const sessionExpiry = secureLocalStorage.getItem<number>(SESSION_EXPIRY_KEY, currentUserId);
    const token = secureLocalStorage.getItem<string>(AUTH_TOKEN_KEY, currentUserId);

    // If any piece of essential data is missing or decryption failed (getItem returns null)
    if (!user || !sessionExpiry || !token) {
      console.log('Essential auth data missing or decryption failed with current_user_id. Clearing auth storage.');
      // Clear all auth-related storage as it might be corrupted or inconsistent
      localStorage.removeItem(CURRENT_USER_ID_KEY);
      secureLocalStorage.removeItem(AUTH_TOKEN_KEY); 
      secureLocalStorage.removeItem(USER_DATA_KEY); 
      secureLocalStorage.removeItem(SESSION_EXPIRY_KEY); 
      return { isAuthenticated: false, user: null };
    }

    // التحقق من صلاحية الجلسة
    if (sessionExpiry < Date.now()) {
      console.log('Session expired, attempting to sign out');
      // انتهت صلاحية الجلسة، قم بتسجيل الخروج
      // Call signOut directly, which also clears storage
      signOut().catch(err => console.error('Error during automated signOut on session expiry:', err)); // Handle potential error from async signOut
      return { isAuthenticated: false, user: null };
    }

    console.log('User is authenticated:', user.email, 'using user_id:', currentUserId);
    return { isAuthenticated: true, user };
  } catch (error) {
    console.error('Error checking auth status:', error);
    // In case of any unexpected error, assume not authenticated and clear storage to be safe
    try {
      localStorage.removeItem(CURRENT_USER_ID_KEY);
      secureLocalStorage.removeItem(AUTH_TOKEN_KEY); 
      secureLocalStorage.removeItem(USER_DATA_KEY); 
      secureLocalStorage.removeItem(SESSION_EXPIRY_KEY); 
    } catch (clearError) {
      console.error('Error clearing auth storage during recovery:', clearError);
    }
    return { isAuthenticated: false, user: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
}

/**
 * وظيفة لتجديد جلسة المستخدم
 */
export function refreshSession() {
  try {
    console.log('Refreshing user session');

    // التحقق من وجود بيانات المستخدم
    const user = secureLocalStorage.getItem<User>(USER_DATA_KEY);

    if (!user) {
      console.log('No user data found, cannot refresh session');
      return false;
    }

    // تعيين وقت انتهاء جديد للجلسة
    const sessionExpiry = Date.now() + SESSION_DURATION;
    secureLocalStorage.setItem(SESSION_EXPIRY_KEY, sessionExpiry, user.id);

    console.log('Session refreshed for user:', user.email);
    return true;
  } catch (error) {
    console.error('Error refreshing session:', error);
    return false;
  }
}
