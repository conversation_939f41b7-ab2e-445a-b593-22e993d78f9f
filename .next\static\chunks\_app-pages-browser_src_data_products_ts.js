"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_data_products_ts"],{

/***/ "(app-pages-browser)/./src/data/products.ts":
/*!******************************!*\
  !*** ./src/data/products.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productCategories: () => (/* binding */ productCategories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst products = [\n    {\n        id: 'smart-factory-sensor',\n        name: 'Smart Factory IoT Sensor Kit',\n        slug: 'smart-factory-sensor',\n        description: 'Next-generation IoT sensors for real-time manufacturing monitoring and predictive maintenance.',\n        price: 2999.99,\n        compareAtPrice: 3499.99,\n        images: [\n            'https://images.pexels.com/photos/3912981/pexels-photo-3912981.jpeg',\n            'https://images.pexels.com/photos/3912982/pexels-photo-3912982.jpeg',\n            'https://images.pexels.com/photos/3912983/pexels-photo-3912983.jpeg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'IoT',\n            'Industry 4.0'\n        ],\n        stock: 50,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 150,\n        specifications: {\n            'Sensor Type': 'Multi-parameter',\n            'Connectivity': 'WiFi, Bluetooth, LoRaWAN',\n            'Battery Life': 'Up to 2 years',\n            'Data Rate': '1 sample/second',\n            'Operating Temperature': '-20°C to 85°C'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'ai-quality-system',\n        name: 'AI-Powered Quality Inspection System',\n        slug: 'ai-quality-system',\n        description: 'Advanced computer vision system for automated quality control in production lines.',\n        price: 8999.99,\n        compareAtPrice: 10999.99,\n        images: [\n            'https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg',\n            'https://images.pexels.com/photos/2159236/pexels-photo-2159236.jpeg',\n            'https://images.pexels.com/photos/2159237/pexels-photo-2159237.jpeg'\n        ],\n        category: 'Quality Control',\n        tags: [\n            'New Arrival',\n            'AI',\n            'Automation'\n        ],\n        stock: 15,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 210,\n        specifications: {\n            'Camera Resolution': '4K Ultra HD',\n            'Processing Speed': 'Up to 100 items/minute',\n            'AI Model': 'Deep Learning CNN',\n            'Integration': 'REST API, MQTT',\n            'Accuracy Rate': '99.9%'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'smart-inventory',\n        name: 'Smart Inventory Management System',\n        slug: 'smart-inventory',\n        description: 'Cloud-based inventory tracking system with real-time analytics and AI-driven forecasting.',\n        price: 4999.99,\n        compareAtPrice: 5999.99,\n        images: [\n            'https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg',\n            'https://images.pexels.com/photos/4483611/pexels-photo-4483611.jpeg',\n            'https://images.pexels.com/photos/4483612/pexels-photo-4483612.jpeg'\n        ],\n        category: 'Warehouse Management',\n        tags: [\n            'New Arrival',\n            'Smart Systems',\n            'Cloud'\n        ],\n        stock: 30,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 95,\n        specifications: {\n            'Storage Capacity': 'Unlimited',\n            'Real-time Tracking': 'Yes',\n            'Mobile App': 'iOS & Android',\n            'Barcode Support': '1D & 2D',\n            'Analytics': 'Advanced ML-based'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'collaborative-robot',\n        name: 'Next-Gen Collaborative Robot',\n        slug: 'collaborative-robot',\n        description: 'Advanced collaborative robot with enhanced safety features and intuitive programming.',\n        price: 29999.99,\n        compareAtPrice: 34999.99,\n        images: [\n            'https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg',\n            'https://images.pexels.com/photos/2159236/pexels-photo-2159236.jpeg',\n            'https://images.pexels.com/photos/2159237/pexels-photo-2159237.jpeg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'New Arrival',\n            'Robotics',\n            'Safety'\n        ],\n        stock: 10,\n        featured: true,\n        rating: 4.8,\n        reviewCount: 75,\n        specifications: {\n            'Payload': 'Up to 10kg',\n            'Reach': '1.3m',\n            'Degrees of Freedom': '6-axis',\n            'Safety Features': 'Force limiting, Vision',\n            'Programming': 'Teach pendant, GUI'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'digital-twin-platform',\n        name: 'Digital Twin Manufacturing Platform',\n        slug: 'digital-twin-platform',\n        description: 'Complete digital twin solution for real-time production monitoring and optimization.',\n        price: 12999.99,\n        compareAtPrice: 15999.99,\n        images: [\n            'https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg',\n            'https://images.pexels.com/photos/3862131/pexels-photo-3862131.jpeg',\n            'https://images.pexels.com/photos/3862132/pexels-photo-3862132.jpeg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'Industry 4.0',\n            'Digital Twin'\n        ],\n        stock: 20,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 110,\n        specifications: {\n            'Simulation': 'Real-time 3D',\n            'Data Integration': 'OPC UA, MQTT',\n            'Analytics': 'Predictive maintenance',\n            'Visualization': 'AR/VR support',\n            'API': 'RESTful, GraphQL'\n        },\n        createdAt: new Date().toISOString()\n    }\n];\nconst productCategories = [\n    {\n        id: 'smart-manufacturing',\n        name: {\n            en: 'Smart Manufacturing',\n            ar: 'التصنيع الذكي'\n        },\n        description: {\n            en: 'Industry 4.0 solutions for modern factories',\n            ar: 'حلول الصناعة 4.0 للمصانع الحديثة'\n        },\n        image: 'https://images.pexels.com/photos/3912981/pexels-photo-3912981.jpeg'\n    },\n    {\n        id: 'quality-control',\n        name: {\n            en: 'Quality Control',\n            ar: 'مراقبة الجودة'\n        },\n        description: {\n            en: 'Advanced quality assurance systems',\n            ar: 'أنظمة ضمان الجودة المتقدمة'\n        },\n        image: 'https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg'\n    },\n    {\n        id: 'warehouse-management',\n        name: {\n            en: 'Warehouse Management',\n            ar: 'إدارة المستودعات'\n        },\n        description: {\n            en: 'Smart warehouse and inventory solutions',\n            ar: 'حلول المستودعات والمخزون الذكية'\n        },\n        image: 'https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg'\n    },\n    {\n        id: 'robotics',\n        name: {\n            en: 'Robotics',\n            ar: 'الروبوتات'\n        },\n        description: {\n            en: 'Next-generation industrial robots',\n            ar: 'الجيل القادم من الروبوتات الصناعية'\n        },\n        image: 'https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg'\n    },\n    {\n        id: 'digital-solutions',\n        name: {\n            en: 'Digital Solutions',\n            ar: 'الحلول الرقمية'\n        },\n        description: {\n            en: 'Digital transformation tools',\n            ar: 'أدوات التحول الرقمي'\n        },\n        image: 'https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/products.ts\n"));

/***/ })

}]);