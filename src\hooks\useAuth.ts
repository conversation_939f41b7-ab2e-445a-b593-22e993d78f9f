import { useEffect, useCallback } from 'react';
import { useAuthStore } from '../stores/authStore';
import { AuthService } from '../services/AuthService';
import { User } from '../types';

export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
}

/**
 * Hook for authentication management
 * Provides authentication state and methods
 */
export function useAuth(): UseAuthReturn {
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    setUser,
    setLoading,
    setError,
    clearUser,
    clearError: clearAuthError
  } = useAuthStore();

  // Sign in user
  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await AuthService.signInWithEmail(email, password);
      
      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        setError(result.error || 'Sign in failed');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [setUser, setLoading, setError]);

  // Sign up user
  const signUp = useCallback(async (email: string, password: string, userData: Partial<User>) => {
    setLoading(true);
    setError(null);

    try {
      const result = await AuthService.signUpWithEmail(email, password, userData);
      
      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        setError(result.error || 'Sign up failed');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [setUser, setLoading, setError]);

  // Sign out user
  const signOut = useCallback(async () => {
    setLoading(true);
    
    try {
      await AuthService.signOut();
      clearUser();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setLoading(false);
    }
  }, [clearUser, setLoading]);

  // Update user profile
  const updateProfile = useCallback(async (userData: Partial<User>) => {
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    setLoading(true);
    setError(null);

    try {
      const result = await AuthService.updateProfile(user.id, userData);
      
      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        setError(result.error || 'Profile update failed');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user, setUser, setLoading, setError]);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    
    try {
      const result = await AuthService.getCurrentUser();
      
      if (result.success && result.user) {
        setUser(result.user);
      } else {
        // If user is no longer valid, sign them out
        clearUser();
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      clearUser();
    } finally {
      setLoading(false);
    }
  }, [user, setUser, clearUser, setLoading]);

  // Clear error
  const clearError = useCallback(() => {
    clearAuthError();
  }, [clearAuthError]);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      
      try {
        const result = await AuthService.getCurrentUser();
        
        if (result.success && result.user) {
          setUser(result.user);
        }
      } catch (error) {
        console.error('Initialize auth error:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [setUser, setLoading]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshUser,
    clearError
  };
}
