# FastCart E-Commerce Platform Developer Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Technology Stack](#technology-stack)
3. [Development Environment Setup](#development-environment-setup)
4. [Project Structure](#project-structure)
5. [Core Modules](#core-modules)
6. [Development Workflow](#development-workflow)
7. [Code Standards & Best Practices](#code-standards--best-practices)
8. [Testing](#testing)
9. [Deployment](#deployment)
10. [API Reference](#api-reference)
11. [Performance Optimization](#performance-optimization)
12. [Troubleshooting & FAQs](#troubleshooting--faqs)
13. [Contributing](#contributing)

## Introduction

This guide provides comprehensive documentation for developers working on the FastCart E-Commerce platform. Whether you're setting up a new development environment, adding features, fixing bugs, or optimizing performance, this guide will help you understand the platform's architecture and development workflow.

## Technology Stack

FastCart is built on the following technologies:

### Backend
- **Python 3.9+**
- **Django 4.x**: Web framework
- **Django REST Framework**: API development
- **PostgreSQL 13+**: Primary database
- **Redis**: For caching and session management
- **Celery**: For asynchronous task processing

### Frontend
- **HTML5/CSS3/JavaScript**
- **Bootstrap 5**: UI framework
- **jQuery**: JavaScript library
- **Vue.js**: For interactive components

### DevOps
- **Docker & Docker Compose**: Containerization
- **GitHub Actions**: CI/CD pipeline
- **Nginx**: Web server
- **Let's Encrypt**: SSL certificates

### Testing
- **pytest**: Python testing framework
- **Cypress**: Frontend testing

## Development Environment Setup

### Prerequisites

Before setting up FastCart, ensure you have the following installed:

- Python 3.9+
- PostgreSQL 13+
- Redis
- Node.js and npm
- Docker and Docker Compose (optional, for containerized development)
- Git

### Local Development Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/FastCart-Ecommerce.git
   cd FastCart-Ecommerce
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   Copy the `.env.example` file to `.env` and update the values:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

5. **Set up the database**:
   ```bash
   python manage.py migrate
   ```

6. **Create a superuser**:
   ```bash
   python manage.py createsuperuser
   ```

7. **Run the development server**:
   ```bash
   python manage.py runserver
   ```

8. **Access the site** at http://127.0.0.1:8000 and the admin at http://127.0.0.1:8000/admin/

### Docker Development Setup

For Docker-based development:

1. **Build and start the containers**:
   ```bash
   docker-compose -f docker-compose.dev.yml up -d --build
   ```

2. **Run migrations**:
   ```bash
   docker-compose exec web python manage.py migrate
   ```

3. **Create a superuser**:
   ```bash
   docker-compose exec web python manage.py createsuperuser
   ```

4. **Access the site** at http://localhost:8000

## Project Structure

The FastCart project follows a modular structure organized by functionality:

```
FastCart-Ecommerce/
├── .github/              # GitHub Actions workflows
├── docs/                 # Documentation
├── ecom_prj/             # Main project settings
├── core/                 # Core functionality
├── store/                # Store and product functionality
├── userauths/            # User authentication and profiles
├── vendor/               # Vendor management
├── wholesale/            # B2B functionality
├── services/             # Service booking
├── blog/                 # Blog functionality
├── api/                  # API endpoints
├── analytics/            # Analytics and reporting
├── tests/                # Test suite
├── templates/            # HTML templates
├── static/               # Static files
├── media/                # User-uploaded files
├── nginx/                # Nginx configuration
├── redis/                # Redis configuration
├── Dockerfile            # Production Docker build
├── Dockerfile.dev        # Development Docker build
├── docker-compose.yml    # Production Docker setup
├── docker-compose.dev.yml # Development Docker setup
├── requirements.txt      # Python dependencies
└── manage.py             # Django management script
```

### Key Directories

- **ecom_prj/**: Contains project settings, base URLs, WSGI/ASGI configuration
- **core/**: Core functionality, shared utilities, middleware
- **store/**: Product catalog, shopping cart, checkout, orders
- **userauths/**: User management, authentication, profiles
- **wholesale/**: B2B functionality, RFQ system, production lines
- **services/**: Service booking and management
- **api/**: REST API endpoints and serializers
- **tests/**: Comprehensive test suite

## Core Modules

### Product Management Module

Products are managed through the `store` app. Key models include:

- `Category`: Product categories and subcategories
- `Product`: Core product information
- `Variant`: Product variations (size, color, etc.)
- `Gallery`: Product images
- `Review`: Product reviews and ratings

Example of extending the product model:

```python
# store/models.py
class CustomProductField(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    field_name = models.CharField(max_length=100)
    field_value = models.TextField()
    
    class Meta:
        unique_together = ('product', 'field_name')
```

### User Management Module

User management is handled through the `userauths` app. Key models include:

- `User`: Extended from Django's AbstractUser
- `Profile`: Additional user profile information
- `Address`: User addresses

### Order Processing Module

Order processing is managed through models in the `store` app:

- `Cart`: Shopping cart items
- `Order`: Order information
- `OrderItem`: Individual items in an order

### RFQ System

The B2B RFQ system is implemented in the `wholesale` app:

- `RFQuote`: Request for quotation
- `RFQItem`: Individual items in an RFQ
- `RFQResponse`: Vendor responses to RFQs

### Service Booking System

The service booking system is implemented in the `services` app:

- `Service`: Available services
- `ServiceBooking`: Customer service bookings
- `ServiceProvider`: Service providers

## Development Workflow

### Feature Development Process

1. **Create a branch** from `develop` for your feature:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. **Implement your changes** following the code standards

3. **Write tests** for your new code

4. **Run tests locally**:
   ```bash
   python run_tests.py all
   ```

5. **Run linting**:
   ```bash
   python run_tests.py lint --all
   ```

6. **Create a pull request** to merge your feature branch into `develop`

7. **Address reviewer comments** and update your PR

8. **After approval**, merge your PR

### Database Migrations

When making model changes:

1. Create migrations:
   ```bash
   python manage.py makemigrations
   ```

2. Apply migrations:
   ```bash
   python manage.py migrate
   ```

3. Always include migrations in your commits

### Working with Static Files

Static files are managed using Django's staticfiles app:

1. Place static files in each app's `static` directory
2. Collect static files for production:
   ```bash
   python manage.py collectstatic
   ```

3. In templates, use:
   ```html
   {% load static %}
   <img src="{% static 'app_name/image.jpg' %}" alt="Image">
   ```

## Code Standards & Best Practices

### Python Code Style

- Follow PEP 8 standards
- Use Black for code formatting
- Use isort for import sorting
- Maximum line length: 100 characters
- Use docstrings for all functions and classes
- Use type hints where helpful

Example:
```python
def calculate_order_total(order_items: List[OrderItem]) -> Decimal:
    """
    Calculate the total price of all order items.
    
    Args:
        order_items: List of OrderItem objects
        
    Returns:
        Decimal: The total price
    """
    return sum(item.price for item in order_items)
```

### Django Best Practices

- Use class-based views where appropriate
- Keep views thin, move business logic to services or models
- Use Django forms for validation
- Use select_related and prefetch_related to optimize queries
- Create custom managers for complex queries
- Use signals sparingly

### JavaScript Code Style

- Follow Airbnb JavaScript Style Guide
- Use ESLint for linting
- Use Prettier for formatting
- Use async/await for asynchronous operations
- Document functions with JSDoc

### Version Control

- Use descriptive commit messages
- Reference issue numbers in commits where applicable
- Keep commits focused on a single change
- Rebase feature branches on develop before creating PR

## Testing

Refer to the [Testing Guide](TESTING_GUIDE.md) for detailed information on testing. Key points:

### Running Tests

- Unit tests: `python run_tests.py unit`
- Integration tests: `python run_tests.py integration`
- All tests with coverage: `python run_tests.py coverage --html`
- Frontend tests: `python run_tests.py cypress --open`

### Writing Tests

Example unit test:

```python
import pytest
from django.test import TestCase
from store.models import Product, Category

class TestProductModel(TestCase):
    def setUp(self):
        self.category = Category.objects.create(
            title="Test Category",
            slug="test-category"
        )
        
        self.product = Product.objects.create(
            name="Test Product",
            category=self.category,
            price=99.99,
            stock=10
        )

    def test_product_creation(self):
        self.assertEqual(self.product.name, "Test Product")
        self.assertEqual(self.product.price, 99.99)
        self.assertEqual(self.product.category, self.category)
```

## Deployment

FastCart supports deployment via Docker and can be deployed to various environments.

### Production Deployment

1. **Set up environment variables**:
   - Copy `.env.example` to `.env.prod`
   - Update with production settings
   - Ensure `DEBUG=False` and proper `ALLOWED_HOSTS`

2. **Deploy using Docker Compose**:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
   ```

3. **Run migrations**:
   ```bash
   docker-compose exec web python manage.py migrate
   ```

4. **Collect static files**:
   ```bash
   docker-compose exec web python manage.py collectstatic --noinput
   ```

### Deployment Environments

- **Development**: Local development environment
- **Staging**: Pre-production environment for testing
- **Production**: Live environment

### CI/CD Pipeline

FastCart uses GitHub Actions for CI/CD:

- **CI Pipeline**: Runs on every pull request and push to main branches
  - Linting
  - Unit tests
  - Integration tests
  - Test coverage report

- **CD Pipeline**: Runs on merge to main branches or manual trigger
  - Build Docker images
  - Push to container registry
  - Deploy to staging/production
  - Run post-deployment tests

## API Reference

FastCart provides a RESTful API for integration with other systems. See [API_REFERENCE.md](API_REFERENCE.md) for details.

### Authentication

API authentication uses JWT tokens:

1. Obtain a token:
   ```
   POST /api/token/
   {"username": "user", "password": "pass"}
   ```

2. Use the token in requests:
   ```
   Authorization: Bearer <token>
   ```

3. Refresh a token:
   ```
   POST /api/token/refresh/
   {"refresh": "<refresh_token>"}
   ```

## Performance Optimization

### Database Optimization

- Use database indexes for frequently queried fields
- Use select_related and prefetch_related to avoid N+1 queries
- Use the Django debug toolbar to identify query bottlenecks
- Use QuerySet.defer() and QuerySet.only() for large models

### Caching Strategy

FastCart uses Redis for caching:

- Page caching for anonymous users
- Fragment caching for dynamic content
- Query caching for expensive database queries
- Cached template loader in production

Example view caching:

```python
from django.views.decorators.cache import cache_page

@cache_page(60 * 15)  # Cache for 15 minutes
def product_list(request):
    products = Product.objects.filter(status='Published')
    return render(request, 'store/product_list.html', {'products': products})
```

### Static File Optimization

- Use WhiteNoise for serving static files
- Enable Brotli/Gzip compression
- Minify CSS and JavaScript
- Use a CDN for assets in production

## Troubleshooting & FAQs

### Common Issues

#### Database Migration Conflicts

If you get migration conflicts:

1. Create a new migration to resolve the conflict:
   ```bash
   python manage.py makemigrations --merge
   ```

2. Apply the merged migration:
   ```bash
   python manage.py migrate
   ```

#### Static Files Not Loading

If static files aren't loading:

1. Check `STATIC_URL` and `STATIC_ROOT` in settings
2. Run `collectstatic` again
3. Check file permissions
4. Clear browser cache

#### Performance Issues

If the site is slow:

1. Use Django Debug Toolbar to identify bottlenecks
2. Check for N+1 queries
3. Ensure proper indexing of database fields
4. Implement caching for expensive operations

## Contributing

We welcome contributions to FastCart! Please follow these steps:

1. Check the issue tracker for open issues
2. Fork the repository
3. Create a feature branch
4. Implement your changes
5. Add tests for new functionality
6. Ensure all tests pass
7. Submit a pull request

See [CONTRIBUTING.md](../CONTRIBUTING.md) for detailed guidelines.

### Code Review Process

All code changes must go through code review:

1. Pull requests require at least one approval
2. CI tests must pass
3. Code must meet style guidelines
4. Documentation must be updated
