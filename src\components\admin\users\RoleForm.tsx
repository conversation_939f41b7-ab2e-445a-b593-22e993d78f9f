'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Check, Shield, Package, ShoppingCart, Users, FileText, Image as ImageIcon, Briefcase, Settings } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';

// نوع الدور
interface Role {
  id: string;
  name: string;
  name_ar?: string;
  description: string;
  description_ar?: string;
  permissions: Permission[];
  usersCount: number;
  isSystem: boolean;
  createdAt: string;
}

// نوع الإذن
interface Permission {
  id: string;
  name: string;
  name_ar?: string;
  module: string;
  action: 'view' | 'create' | 'edit' | 'delete' | 'manage';
}

interface RoleFormProps {
  role: Role | null;
  onSave: (role: Role) => void;
  onCancel: () => void;
}

// قائمة الوحدات والصلاحيات المتاحة
const availablePermissions: Permission[] = [
  // وحدة المنتجات
  { id: 'products-view', name: 'View Products', name_ar: 'عرض المنتجات', module: 'products', action: 'view' },
  { id: 'products-create', name: 'Create Products', name_ar: 'إنشاء المنتجات', module: 'products', action: 'create' },
  { id: 'products-edit', name: 'Edit Products', name_ar: 'تعديل المنتجات', module: 'products', action: 'edit' },
  { id: 'products-delete', name: 'Delete Products', name_ar: 'حذف المنتجات', module: 'products', action: 'delete' },
  { id: 'products-manage', name: 'Manage Products', name_ar: 'إدارة المنتجات', module: 'products', action: 'manage' },
  
  // وحدة الطلبات
  { id: 'orders-view', name: 'View Orders', name_ar: 'عرض الطلبات', module: 'orders', action: 'view' },
  { id: 'orders-create', name: 'Create Orders', name_ar: 'إنشاء الطلبات', module: 'orders', action: 'create' },
  { id: 'orders-edit', name: 'Edit Orders', name_ar: 'تعديل الطلبات', module: 'orders', action: 'edit' },
  { id: 'orders-delete', name: 'Delete Orders', name_ar: 'حذف الطلبات', module: 'orders', action: 'delete' },
  { id: 'orders-manage', name: 'Manage Orders', name_ar: 'إدارة الطلبات', module: 'orders', action: 'manage' },
  
  // وحدة المستخدمين
  { id: 'users-view', name: 'View Users', name_ar: 'عرض المستخدمين', module: 'users', action: 'view' },
  { id: 'users-create', name: 'Create Users', name_ar: 'إنشاء المستخدمين', module: 'users', action: 'create' },
  { id: 'users-edit', name: 'Edit Users', name_ar: 'تعديل المستخدمين', module: 'users', action: 'edit' },
  { id: 'users-delete', name: 'Delete Users', name_ar: 'حذف المستخدمين', module: 'users', action: 'delete' },
  { id: 'users-manage', name: 'Manage Users', name_ar: 'إدارة المستخدمين', module: 'users', action: 'manage' },
  
  // وحدة المحتوى
  { id: 'content-view', name: 'View Content', name_ar: 'عرض المحتوى', module: 'content', action: 'view' },
  { id: 'content-create', name: 'Create Content', name_ar: 'إنشاء المحتوى', module: 'content', action: 'create' },
  { id: 'content-edit', name: 'Edit Content', name_ar: 'تعديل المحتوى', module: 'content', action: 'edit' },
  { id: 'content-delete', name: 'Delete Content', name_ar: 'حذف المحتوى', module: 'content', action: 'delete' },
  { id: 'content-manage', name: 'Manage Content', name_ar: 'إدارة المحتوى', module: 'content', action: 'manage' },
  
  // وحدة الوسائط
  { id: 'media-view', name: 'View Media', name_ar: 'عرض الوسائط', module: 'media', action: 'view' },
  { id: 'media-create', name: 'Upload Media', name_ar: 'رفع الوسائط', module: 'media', action: 'create' },
  { id: 'media-delete', name: 'Delete Media', name_ar: 'حذف الوسائط', module: 'media', action: 'delete' },
  { id: 'media-manage', name: 'Manage Media', name_ar: 'إدارة الوسائط', module: 'media', action: 'manage' },
  
  // وحدة الخدمات
  { id: 'services-view', name: 'View Services', name_ar: 'عرض الخدمات', module: 'services', action: 'view' },
  { id: 'services-create', name: 'Create Services', name_ar: 'إنشاء الخدمات', module: 'services', action: 'create' },
  { id: 'services-edit', name: 'Edit Services', name_ar: 'تعديل الخدمات', module: 'services', action: 'edit' },
  { id: 'services-delete', name: 'Delete Services', name_ar: 'حذف الخدمات', module: 'services', action: 'delete' },
  { id: 'services-manage', name: 'Manage Services', name_ar: 'إدارة الخدمات', module: 'services', action: 'manage' },
  
  // وحدة النظام
  { id: 'system-settings', name: 'System Settings', name_ar: 'إعدادات النظام', module: 'system', action: 'manage' },
  { id: 'all', name: 'All Permissions', name_ar: 'جميع الصلاحيات', module: 'system', action: 'manage' }
];

// تجميع الوحدات
const modules = [
  { id: 'products', name: 'Products', name_ar: 'المنتجات', icon: <Package className="h-5 w-5" /> },
  { id: 'orders', name: 'Orders', name_ar: 'الطلبات', icon: <ShoppingCart className="h-5 w-5" /> },
  { id: 'users', name: 'Users', name_ar: 'المستخدمين', icon: <Users className="h-5 w-5" /> },
  { id: 'content', name: 'Content', name_ar: 'المحتوى', icon: <FileText className="h-5 w-5" /> },
  { id: 'media', name: 'Media', name_ar: 'الوسائط', icon: <ImageIcon className="h-5 w-5" /> },
  { id: 'services', name: 'Services', name_ar: 'الخدمات', icon: <Briefcase className="h-5 w-5" /> },
  { id: 'system', name: 'System', name_ar: 'النظام', icon: <Settings className="h-5 w-5" /> }
];

export function RoleForm({ role, onSave, onCancel }: RoleFormProps) {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<Role>>({
    id: '',
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    permissions: [],
    usersCount: 0,
    isSystem: false,
    createdAt: new Date().toISOString(),
  });
  
  // تحميل بيانات الدور إذا كان موجودًا
  useEffect(() => {
    if (role) {
      setFormData({
        ...role,
      });
    }
  }, [role]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // تحديث الصلاحيات
  const handlePermissionChange = (permission: Permission) => {
    setFormData(prev => {
      const permissions = [...(prev.permissions || [])];
      
      // إذا كان الإذن هو "جميع الصلاحيات"
      if (permission.id === 'all') {
        // إذا كان محددًا بالفعل، قم بإزالته وإزالة جميع الصلاحيات
        if (permissions.some(p => p.id === 'all')) {
          return { ...prev, permissions: [] };
        }
        // وإلا، قم بإضافته وإزالة جميع الصلاحيات الأخرى
        return { ...prev, permissions: [permission] };
      }
      
      // إذا كان "جميع الصلاحيات" محددًا، قم بإزالته أولاً
      const filteredPermissions = permissions.filter(p => p.id !== 'all');
      
      // تحقق مما إذا كان الإذن موجودًا بالفعل
      const existingIndex = filteredPermissions.findIndex(p => p.id === permission.id);
      
      if (existingIndex >= 0) {
        // إزالة الإذن إذا كان موجودًا
        filteredPermissions.splice(existingIndex, 1);
      } else {
        // إضافة الإذن إذا لم يكن موجودًا
        filteredPermissions.push(permission);
      }
      
      return { ...prev, permissions: filteredPermissions };
    });
  };
  
  // تحديد جميع صلاحيات الوحدة
  const handleSelectAllModulePermissions = (moduleId: string) => {
    setFormData(prev => {
      const permissions = [...(prev.permissions || [])];
      
      // إزالة "جميع الصلاحيات" إذا كان موجودًا
      const filteredPermissions = permissions.filter(p => p.id !== 'all');
      
      // الحصول على جميع صلاحيات الوحدة
      const modulePermissions = availablePermissions.filter(p => p.module === moduleId);
      
      // تحقق مما إذا كانت جميع صلاحيات الوحدة محددة بالفعل
      const allModulePermissionsSelected = modulePermissions.every(
        mp => filteredPermissions.some(p => p.id === mp.id)
      );
      
      if (allModulePermissionsSelected) {
        // إزالة جميع صلاحيات الوحدة
        return {
          ...prev,
          permissions: filteredPermissions.filter(p => p.module !== moduleId)
        };
      } else {
        // إضافة جميع صلاحيات الوحدة التي لم يتم تحديدها بعد
        const newPermissions = [...filteredPermissions];
        
        modulePermissions.forEach(mp => {
          if (!newPermissions.some(p => p.id === mp.id)) {
            newPermissions.push(mp);
          }
        });
        
        return { ...prev, permissions: newPermissions };
      }
    });
  };
  
  // حفظ الدور
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.name || !formData.description) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    // إنشاء معرف جديد إذا كان دور جديد
    const roleData: Role = {
      ...(formData as Role),
      id: role?.id || `role-${Date.now()}`,
      createdAt: role?.createdAt || new Date().toISOString(),
      usersCount: role?.usersCount || 0,
      isSystem: role?.isSystem || false,
    };
    
    onSave(roleData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {role
              ? language === 'ar' ? 'تحرير الدور' : 'Edit Role'
              : language === 'ar' ? 'إضافة دور جديد' : 'Add New Role'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الاسم بالإنجليزية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'اسم الدور (بالإنجليزية)' : 'Role Name (English)'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                required
              />
            </div>
            
            {/* الاسم بالعربية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'اسم الدور (بالعربية)' : 'Role Name (Arabic)'}
              </label>
              <Input
                name="name_ar"
                value={formData.name_ar || ''}
                onChange={handleChange}
                dir="rtl"
              />
            </div>
            
            {/* الوصف بالإنجليزية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الوصف (بالإنجليزية)' : 'Description (English)'}
                <span className="text-red-500">*</span>
              </label>
              <textarea
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                rows={3}
                className={cn(
                  "w-full px-3 py-2 rounded-md border resize-none",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
                required
              />
            </div>
            
            {/* الوصف بالعربية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الوصف (بالعربية)' : 'Description (Arabic)'}
              </label>
              <textarea
                name="description_ar"
                value={formData.description_ar || ''}
                onChange={handleChange}
                rows={3}
                dir="rtl"
                className={cn(
                  "w-full px-3 py-2 rounded-md border resize-none",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              />
            </div>
          </div>
          
          {/* الصلاحيات */}
          <div>
            <label className="block text-sm font-medium mb-3">
              {language === 'ar' ? 'الصلاحيات' : 'Permissions'}
            </label>
            
            {/* صلاحية جميع الصلاحيات */}
            <div className={cn(
              "p-4 mb-4 rounded-lg border",
              isDarkMode ? "bg-slate-700/50 border-slate-600" : "bg-gray-50 border-gray-200"
            )}>
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="all-permissions"
                  checked={formData.permissions?.some(p => p.id === 'all') || false}
                  onChange={() => handlePermissionChange(availablePermissions.find(p => p.id === 'all')!)}
                  className="h-4 w-4"
                />
                <label htmlFor="all-permissions" className="flex items-center gap-2 font-medium">
                  <Shield className="h-5 w-5 text-purple-500" />
                  {language === 'ar' ? 'جميع الصلاحيات' : 'All Permissions'}
                </label>
              </div>
              <p className="text-sm text-slate-500 dark:text-slate-400 mt-2 ml-7">
                {language === 'ar'
                  ? 'منح جميع الصلاحيات للدور (وصول كامل)'
                  : 'Grant all permissions to this role (full access)'
                }
              </p>
            </div>
            
            {/* قائمة الوحدات والصلاحيات */}
            <div className="space-y-6">
              {modules.map((module) => {
                // تجاهل وحدة النظام إذا كانت "جميع الصلاحيات" محددة
                if (module.id === 'system' && formData.permissions?.some(p => p.id === 'all')) {
                  return null;
                }
                
                // الحصول على صلاحيات الوحدة
                const modulePermissions = availablePermissions.filter(
                  p => p.module === module.id && p.id !== 'all'
                );
                
                // التحقق مما إذا كانت جميع صلاحيات الوحدة محددة
                const allModulePermissionsSelected = modulePermissions.every(
                  mp => formData.permissions?.some(p => p.id === mp.id)
                );
                
                // التحقق مما إذا كانت بعض صلاحيات الوحدة محددة
                const someModulePermissionsSelected = modulePermissions.some(
                  mp => formData.permissions?.some(p => p.id === mp.id)
                );
                
                return (
                  <div key={module.id} className={cn(
                    "p-4 rounded-lg border",
                    isDarkMode ? "border-slate-600" : "border-gray-200"
                  )}>
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        {module.icon}
                        <h3 className="font-medium">
                          {language === 'ar' ? module.name_ar : module.name}
                        </h3>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleSelectAllModulePermissions(module.id)}
                        className={cn(
                          "text-xs px-2 py-1 rounded",
                          allModulePermissionsSelected
                            ? isDarkMode
                              ? "bg-blue-900/30 text-blue-400"
                              : "bg-blue-100 text-blue-800"
                            : someModulePermissionsSelected
                              ? isDarkMode
                                ? "bg-yellow-900/30 text-yellow-400"
                                : "bg-yellow-100 text-yellow-800"
                              : isDarkMode
                                ? "bg-slate-700 text-slate-300"
                                : "bg-gray-100 text-gray-800"
                        )}
                      >
                        {allModulePermissionsSelected
                          ? language === 'ar' ? 'إلغاء تحديد الكل' : 'Deselect All'
                          : someModulePermissionsSelected
                            ? language === 'ar' ? 'تحديد الكل' : 'Select All'
                            : language === 'ar' ? 'تحديد الكل' : 'Select All'
                        }
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {modulePermissions.map((permission) => (
                        <div
                          key={permission.id}
                          className={cn(
                            "flex items-center gap-2 p-2 rounded",
                            formData.permissions?.some(p => p.id === permission.id)
                              ? isDarkMode
                                ? "bg-blue-900/30 text-blue-400"
                                : "bg-blue-50 text-blue-800"
                              : ""
                          )}
                        >
                          <input
                            type="checkbox"
                            id={permission.id}
                            checked={formData.permissions?.some(p => p.id === permission.id) || false}
                            onChange={() => handlePermissionChange(permission)}
                            className="h-4 w-4"
                            disabled={formData.permissions?.some(p => p.id === 'all')}
                          />
                          <label htmlFor={permission.id} className="text-sm">
                            {language === 'ar' ? permission.name_ar : permission.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button type="submit">
              {language === 'ar' ? 'حفظ' : 'Save'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
