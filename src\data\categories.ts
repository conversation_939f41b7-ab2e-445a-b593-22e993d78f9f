export interface Category {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  description?: string;
  description_ar?: string;
  image?: string;
  icon?: string;
  parent_id?: string;
  children?: Category[];
  product_count?: number;
  featured?: boolean;
  sort_order?: number;
}

export const categories: Category[] = [
  {
    id: 'electronics',
    name: 'Electronics',
    name_ar: 'الإلكترونيات',
    slug: 'electronics',
    description: 'Electronic devices and components',
    description_ar: 'الأجهزة والمكونات الإلكترونية',
    icon: 'smartphone',
    featured: true,
    sort_order: 1,
    product_count: 150,
    children: [
      {
        id: 'smartphones',
        name: 'Smartphones',
        name_ar: 'الهواتف الذكية',
        slug: 'smartphones',
        parent_id: 'electronics',
        product_count: 45
      },
      {
        id: 'laptops',
        name: 'Laptops',
        name_ar: 'أجهزة الكمبيوتر المحمولة',
        slug: 'laptops',
        parent_id: 'electronics',
        product_count: 32
      },
      {
        id: 'tablets',
        name: 'Tablets',
        name_ar: 'الأجهزة اللوحية',
        slug: 'tablets',
        parent_id: 'electronics',
        product_count: 28
      },
      {
        id: 'accessories',
        name: 'Accessories',
        name_ar: 'الإكسسوارات',
        slug: 'accessories',
        parent_id: 'electronics',
        product_count: 45
      }
    ]
  },
  {
    id: 'clothing',
    name: 'Clothing',
    name_ar: 'الملابس',
    slug: 'clothing',
    description: 'Fashion and apparel',
    description_ar: 'الأزياء والملابس',
    icon: 'shirt',
    featured: true,
    sort_order: 2,
    product_count: 200,
    children: [
      {
        id: 'mens-clothing',
        name: "Men's Clothing",
        name_ar: 'ملابس رجالية',
        slug: 'mens-clothing',
        parent_id: 'clothing',
        product_count: 80
      },
      {
        id: 'womens-clothing',
        name: "Women's Clothing",
        name_ar: 'ملابس نسائية',
        slug: 'womens-clothing',
        parent_id: 'clothing',
        product_count: 90
      },
      {
        id: 'kids-clothing',
        name: "Kids' Clothing",
        name_ar: 'ملابس أطفال',
        slug: 'kids-clothing',
        parent_id: 'clothing',
        product_count: 30
      }
    ]
  },
  {
    id: 'home-garden',
    name: 'Home & Garden',
    name_ar: 'المنزل والحديقة',
    slug: 'home-garden',
    description: 'Home improvement and garden supplies',
    description_ar: 'مستلزمات تحسين المنزل والحديقة',
    icon: 'home',
    featured: true,
    sort_order: 3,
    product_count: 120,
    children: [
      {
        id: 'furniture',
        name: 'Furniture',
        name_ar: 'الأثاث',
        slug: 'furniture',
        parent_id: 'home-garden',
        product_count: 50
      },
      {
        id: 'kitchen',
        name: 'Kitchen',
        name_ar: 'المطبخ',
        slug: 'kitchen',
        parent_id: 'home-garden',
        product_count: 35
      },
      {
        id: 'garden',
        name: 'Garden',
        name_ar: 'الحديقة',
        slug: 'garden',
        parent_id: 'home-garden',
        product_count: 35
      }
    ]
  },
  {
    id: 'sports-outdoors',
    name: 'Sports & Outdoors',
    name_ar: 'الرياضة والأنشطة الخارجية',
    slug: 'sports-outdoors',
    description: 'Sports equipment and outdoor gear',
    description_ar: 'معدات رياضية وأدوات الأنشطة الخارجية',
    icon: 'activity',
    featured: false,
    sort_order: 4,
    product_count: 85,
    children: [
      {
        id: 'fitness',
        name: 'Fitness',
        name_ar: 'اللياقة البدنية',
        slug: 'fitness',
        parent_id: 'sports-outdoors',
        product_count: 40
      },
      {
        id: 'outdoor-gear',
        name: 'Outdoor Gear',
        name_ar: 'معدات خارجية',
        slug: 'outdoor-gear',
        parent_id: 'sports-outdoors',
        product_count: 45
      }
    ]
  },
  {
    id: 'books-media',
    name: 'Books & Media',
    name_ar: 'الكتب والوسائط',
    slug: 'books-media',
    description: 'Books, movies, music and digital media',
    description_ar: 'الكتب والأفلام والموسيقى والوسائط الرقمية',
    icon: 'book',
    featured: false,
    sort_order: 5,
    product_count: 60,
    children: [
      {
        id: 'books',
        name: 'Books',
        name_ar: 'الكتب',
        slug: 'books',
        parent_id: 'books-media',
        product_count: 35
      },
      {
        id: 'digital-media',
        name: 'Digital Media',
        name_ar: 'الوسائط الرقمية',
        slug: 'digital-media',
        parent_id: 'books-media',
        product_count: 25
      }
    ]
  },
  {
    id: 'automotive',
    name: 'Automotive',
    name_ar: 'السيارات',
    slug: 'automotive',
    description: 'Car parts and automotive accessories',
    description_ar: 'قطع غيار السيارات والإكسسوارات',
    icon: 'car',
    featured: false,
    sort_order: 6,
    product_count: 75
  },
  {
    id: 'health-beauty',
    name: 'Health & Beauty',
    name_ar: 'الصحة والجمال',
    slug: 'health-beauty',
    description: 'Health and beauty products',
    description_ar: 'منتجات الصحة والجمال',
    icon: 'heart',
    featured: false,
    sort_order: 7,
    product_count: 90
  },
  {
    id: 'toys-games',
    name: 'Toys & Games',
    name_ar: 'الألعاب',
    slug: 'toys-games',
    description: 'Toys and games for all ages',
    description_ar: 'ألعاب لجميع الأعمار',
    icon: 'gamepad-2',
    featured: false,
    sort_order: 8,
    product_count: 55
  }
];

export const featuredCategories = categories.filter(cat => cat.featured);

export const getCategoryById = (id: string): Category | undefined => {
  return categories.find(cat => cat.id === id);
};

export const getCategoryBySlug = (slug: string): Category | undefined => {
  return categories.find(cat => cat.slug === slug);
};

export const getSubcategories = (parentId: string): Category[] => {
  const parent = getCategoryById(parentId);
  return parent?.children || [];
};

export const getAllCategories = (): Category[] => {
  const allCategories: Category[] = [];
  
  categories.forEach(category => {
    allCategories.push(category);
    if (category.children) {
      allCategories.push(...category.children);
    }
  });
  
  return allCategories;
};

export const getCategoryPath = (categoryId: string): Category[] => {
  const path: Category[] = [];
  const allCategories = getAllCategories();
  
  const category = allCategories.find(cat => cat.id === categoryId);
  if (!category) return path;
  
  path.unshift(category);
  
  if (category.parent_id) {
    const parent = getCategoryById(category.parent_id);
    if (parent) {
      path.unshift(parent);
    }
  }
  
  return path;
};
