# FastCart API Reference

## Overview

The FastCart API provides programmatic access to FastCart's e-commerce platform features. This RESTful API allows developers to integrate with the FastCart platform, retrieve data, and perform operations like managing products, processing orders, and handling user data.

## Base URL

All API requests should be sent to:

```
https://yourdomain.com/api/v1/
```

## Authentication

### JWT Authentication

FastCart API uses JSON Web Tokens (JWT) for authentication. To authenticate, you need to:

1. Obtain an access token using your credentials
2. Include the token in the Authorization header of subsequent requests

#### Obtaining a Token

```
POST /api/token/
```

**Request Body:**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ..."
}
```

The access token is valid for 1 hour. The refresh token is valid for 24 hours.

#### Using the Token

Include the access token in your API requests:

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ...
```

#### Refreshing a Token

When the access token expires, you can use the refresh token to get a new one:

```
POST /api/token/refresh/
```

**Request Body:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ..."
}
```

**Response:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ..."
}
```

### API Key Authentication

For server-to-server integrations, you can use API key authentication:

1. Generate an API key in the admin interface
2. Include the API key in the request header

```
X-API-Key: your_api_key
```

## Response Format

All API responses are returned in JSON format. A typical successful response looks like:

```json
{
  "status": "success",
  "data": { ... }
}
```

Error responses follow this structure:

```json
{
  "status": "error",
  "error": {
    "code": "error_code",
    "message": "Description of the error"
  }
}
```

## Pagination

List endpoints return paginated results. The pagination metadata is included in the response:

```json
{
  "status": "success",
  "data": [ ... ],
  "pagination": {
    "count": 100,
    "next": "https://yourdomain.com/api/v1/products/?page=2",
    "previous": null,
    "page": 1,
    "pages": 5,
    "page_size": 20
  }
}
```

You can control pagination with these query parameters:
- `page`: The page number (default: 1)
- `page_size`: Number of items per page (default: 20, max: 100)

## Rate Limiting

The API enforces rate limits to ensure fair usage:

- Anonymous requests: 100 requests per hour
- Authenticated requests: 1000 requests per hour
- Webhook endpoints: 60 requests per minute

Rate limit information is included in the response headers:
- `X-RateLimit-Limit`: Total number of requests allowed
- `X-RateLimit-Remaining`: Number of requests remaining
- `X-RateLimit-Reset`: Time when the rate limit will reset (Unix timestamp)

## Endpoints

### Products

#### List Products

```
GET /api/v1/products/
```

Query parameters:
- `category`: Filter by category ID or slug
- `search`: Search term
- `min_price`: Minimum price
- `max_price`: Maximum price
- `featured`: Filter featured products (true/false)
- `status`: Filter by status (Published, Draft, Disabled)
- `sort`: Sort field (name, price, created)
- `sort_dir`: Sort direction (asc, desc)

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "Product Name",
      "slug": "product-name",
      "description": "Product description",
      "price": "99.99",
      "regular_price": "129.99",
      "category": {
        "id": 1,
        "name": "Category Name",
        "slug": "category-name"
      },
      "status": "Published",
      "featured": true,
      "stock": 10,
      "image": "https://yourdomain.com/media/products/image.jpg",
      "created_at": "2025-05-01T12:00:00Z",
      "updated_at": "2025-05-02T12:00:00Z"
    },
    // More products...
  ],
  "pagination": { ... }
}
```

#### Get Product

```
GET /api/v1/products/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Product Name",
    "slug": "product-name",
    "description": "Product description",
    "price": "99.99",
    "regular_price": "129.99",
    "category": {
      "id": 1,
      "name": "Category Name",
      "slug": "category-name"
    },
    "status": "Published",
    "featured": true,
    "stock": 10,
    "sku": "SKU12345",
    "shipping": "9.99",
    "image": "https://yourdomain.com/media/products/image.jpg",
    "gallery": [
      {
        "id": 1,
        "image": "https://yourdomain.com/media/products/gallery1.jpg"
      },
      {
        "id": 2,
        "image": "https://yourdomain.com/media/products/gallery2.jpg"
      }
    ],
    "variants": [
      {
        "id": 1,
        "name": "Size",
        "items": [
          {
            "id": 1,
            "title": "Small",
            "content": "S"
          },
          {
            "id": 2,
            "title": "Medium",
            "content": "M"
          }
        ]
      }
    ],
    "reviews": [
      {
        "id": 1,
        "user": "username",
        "rating": 5,
        "review": "Great product!",
        "created_at": "2025-05-01T14:30:00Z"
      }
    ],
    "created_at": "2025-05-01T12:00:00Z",
    "updated_at": "2025-05-02T12:00:00Z"
  }
}
```

#### Create Product

```
POST /api/v1/products/
```

**Request Body:**
```json
{
  "name": "New Product",
  "description": "Product description",
  "category": 1,
  "price": "99.99",
  "regular_price": "129.99",
  "stock": 10,
  "status": "Published",
  "featured": false,
  "shipping": "9.99"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 3,
    "name": "New Product",
    // Other product fields...
  }
}
```

#### Update Product

```
PUT /api/v1/products/{id}/
```

**Request Body:**
```json
{
  "name": "Updated Product Name",
  "price": "89.99",
  "stock": 15
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Updated Product Name",
    "price": "89.99",
    "stock": 15,
    // Other product fields...
  }
}
```

#### Delete Product

```
DELETE /api/v1/products/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "message": "Product deleted successfully"
  }
}
```

### Categories

#### List Categories

```
GET /api/v1/categories/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "title": "Category Name",
      "slug": "category-name",
      "image": "https://yourdomain.com/media/categories/image.jpg",
      "products_count": 10
    },
    // More categories...
  ],
  "pagination": { ... }
}
```

#### Get Category

```
GET /api/v1/categories/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "title": "Category Name",
    "slug": "category-name",
    "image": "https://yourdomain.com/media/categories/image.jpg",
    "products_count": 10,
    "products": [
      {
        "id": 1,
        "name": "Product Name",
        // Basic product info...
      },
      // More products...
    ]
  }
}
```

### Cart

#### Get Cart

```
GET /api/v1/cart/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "product": {
          "id": 1,
          "name": "Product Name",
          "image": "https://yourdomain.com/media/products/image.jpg",
          "price": "99.99"
        },
        "quantity": 2,
        "price": "199.98"
      }
    ],
    "subtotal": "199.98",
    "shipping": "10.00",
    "total": "209.98",
    "item_count": 2
  }
}
```

#### Add to Cart

```
POST /api/v1/cart/items/
```

**Request Body:**
```json
{
  "product_id": 1,
  "quantity": 2
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "item": {
      "id": 1,
      "product": {
        "id": 1,
        "name": "Product Name",
        "image": "https://yourdomain.com/media/products/image.jpg",
        "price": "99.99"
      },
      "quantity": 2,
      "price": "199.98"
    },
    "cart": {
      "subtotal": "199.98",
      "shipping": "10.00",
      "total": "209.98",
      "item_count": 2
    }
  }
}
```

#### Update Cart Item

```
PUT /api/v1/cart/items/{id}/
```

**Request Body:**
```json
{
  "quantity": 3
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "item": {
      "id": 1,
      "product": {
        "id": 1,
        "name": "Product Name",
        "price": "99.99"
      },
      "quantity": 3,
      "price": "299.97"
    },
    "cart": {
      "subtotal": "299.97",
      "shipping": "10.00",
      "total": "309.97",
      "item_count": 3
    }
  }
}
```

#### Remove from Cart

```
DELETE /api/v1/cart/items/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "message": "Item removed from cart",
    "cart": {
      "subtotal": "0.00",
      "shipping": "0.00",
      "total": "0.00",
      "item_count": 0
    }
  }
}
```

#### Clear Cart

```
DELETE /api/v1/cart/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "message": "Cart cleared successfully"
  }
}
```

### Orders

#### List Orders

```
GET /api/v1/orders/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "order_number": "ORD-12345",
      "status": "Processing",
      "payment_status": "Paid",
      "total": "209.98",
      "created_at": "2025-05-01T14:30:00Z"
    },
    // More orders...
  ],
  "pagination": { ... }
}
```

#### Get Order

```
GET /api/v1/orders/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "order_number": "ORD-12345",
    "user": {
      "id": 1,
      "username": "username",
      "email": "<EMAIL>"
    },
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "shipping_address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipcode": "10001",
    "phone": "************",
    "items": [
      {
        "id": 1,
        "product": {
          "id": 1,
          "name": "Product Name",
          "image": "https://yourdomain.com/media/products/image.jpg"
        },
        "quantity": 2,
        "price": "199.98"
      }
    ],
    "subtotal": "199.98",
    "shipping": "10.00",
    "total": "209.98",
    "payment_method": "PayPal",
    "payment_status": "Paid",
    "order_status": "Processing",
    "tracking_number": "TRACK123456",
    "created_at": "2025-05-01T14:30:00Z",
    "updated_at": "2025-05-02T10:15:00Z"
  }
}
```

#### Create Order

```
POST /api/v1/orders/
```

**Request Body:**
```json
{
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "shipping_address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "zipcode": "10001",
  "phone": "************",
  "payment_method": "PayPal"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 2,
    "order_number": "ORD-12346",
    // Order details...
    "payment_url": "https://yourdomain.com/payments/process/2/"
  }
}
```

#### Update Order Status

```
PATCH /api/v1/orders/{id}/
```

**Request Body:**
```json
{
  "order_status": "Shipped",
  "tracking_number": "TRACK123456"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "order_status": "Shipped",
    "tracking_number": "TRACK123456",
    // Other order fields...
  }
}
```

### Users

#### Get User Profile

```
GET /api/v1/users/me/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "username": "username",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "user_type": "Customer",
    "addresses": [
      {
        "id": 1,
        "address_type": "Shipping",
        "address": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zipcode": "10001",
        "country": "US",
        "is_default": true
      }
    ],
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Update User Profile

```
PUT /api/v1/users/me/
```

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Smith",
  "phone": "************"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "username": "username",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Smith",
    "phone": "************",
    // Other user fields...
  }
}
```

#### User Addresses

```
GET /api/v1/users/me/addresses/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "address_type": "Shipping",
      "address": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipcode": "10001",
      "country": "US",
      "is_default": true
    },
    // More addresses...
  ]
}
```

#### Add Address

```
POST /api/v1/users/me/addresses/
```

**Request Body:**
```json
{
  "address_type": "Billing",
  "address": "456 Park Ave",
  "city": "New York",
  "state": "NY",
  "zipcode": "10002",
  "country": "US",
  "is_default": false
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 2,
    "address_type": "Billing",
    "address": "456 Park Ave",
    "city": "New York",
    "state": "NY",
    "zipcode": "10002",
    "country": "US",
    "is_default": false
  }
}
```

### Wishlist

#### Get Wishlist

```
GET /api/v1/wishlist/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "product": {
        "id": 1,
        "name": "Product Name",
        "image": "https://yourdomain.com/media/products/image.jpg",
        "price": "99.99",
        "stock": 10
      },
      "added_at": "2025-05-01T14:30:00Z"
    },
    // More wishlist items...
  ]
}
```

#### Add to Wishlist

```
POST /api/v1/wishlist/
```

**Request Body:**
```json
{
  "product_id": 2
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 2,
    "product": {
      "id": 2,
      "name": "Another Product",
      "image": "https://yourdomain.com/media/products/another.jpg",
      "price": "49.99",
      "stock": 5
    },
    "added_at": "2025-05-02T15:45:00Z"
  }
}
```

#### Remove from Wishlist

```
DELETE /api/v1/wishlist/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "message": "Item removed from wishlist"
  }
}
```

### B2B RFQ API

#### List RFQs

```
GET /api/v1/rfq/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "rfq_id": "RFQ-12345",
      "company_name": "Company Name",
      "status": "Pending",
      "created_at": "2025-05-01T14:30:00Z"
    },
    // More RFQs...
  ],
  "pagination": { ... }
}
```

#### Get RFQ

```
GET /api/v1/rfq/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "rfq_id": "RFQ-12345",
    "company_name": "Company Name",
    "contact_email": "<EMAIL>",
    "contact_phone": "************",
    "requirements": "Need 1000 units of product X with custom branding.",
    "status": "Pending",
    "quoted_price": null,
    "estimated_delivery": null,
    "admin_notes": null,
    "customer_notes": null,
    "date_created": "2025-05-01T14:30:00Z",
    "date_quoted": null
  }
}
```

#### Create RFQ

```
POST /api/v1/rfq/
```

**Request Body:**
```json
{
  "company_name": "New Company",
  "contact_email": "<EMAIL>",
  "contact_phone": "************",
  "requirements": "Need 500 units of product Y with custom packaging."
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 2,
    "rfq_id": "RFQ-12346",
    "company_name": "New Company",
    "status": "Pending",
    // Other RFQ fields...
  }
}
```

#### Update RFQ

```
PATCH /api/v1/rfq/{id}/
```

**Request Body:**
```json
{
  "status": "Quoted",
  "quoted_price": "5000.00",
  "estimated_delivery": "2025-06-01"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "status": "Quoted",
    "quoted_price": "5000.00",
    "estimated_delivery": "2025-06-01",
    "date_quoted": "2025-05-02T16:00:00Z",
    // Other RFQ fields...
  }
}
```

### Services API

#### List Services

```
GET /api/v1/services/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "Product Inspection",
      "slug": "product-inspection",
      "service_type": "Inspection",
      "price": "299.99",
      "price_unit": "Per Inspection",
      "status": "Available",
      "image": "https://yourdomain.com/media/services/inspection.jpg"
    },
    // More services...
  ],
  "pagination": { ... }
}
```

#### Get Service

```
GET /api/v1/services/{id}/
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Product Inspection",
    "slug": "product-inspection",
    "service_type": "Inspection",
    "description": "Comprehensive product quality inspection service.",
    "features": "Quality check\nCompliance verification\nDefect identification",
    "price": "299.99",
    "price_unit": "Per Inspection",
    "status": "Available",
    "service_id": "SRV12345",
    "featured": true,
    "image": "https://yourdomain.com/media/services/inspection.jpg",
    "date_added": "2025-01-10T00:00:00Z"
  }
}
```

#### Book Service

```
POST /api/v1/service-bookings/
```

**Request Body:**
```json
{
  "service_id": 1,
  "scheduled_date": "2025-06-01",
  "requirements": "Need inspection for 500 units of product",
  "duration": 1,
  "contact_email": "<EMAIL>",
  "contact_phone": "************"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "booking_id": "BOOK-12345",
    "service": {
      "id": 1,
      "name": "Product Inspection",
      "service_type": "Inspection"
    },
    "scheduled_date": "2025-06-01",
    "requirements": "Need inspection for 500 units of product",
    "duration": 1,
    "status": "Pending",
    "total_price": "299.99",
    "created_at": "2025-05-02T16:30:00Z"
  }
}
```

#### List Service Bookings

```
GET /api/v1/service-bookings/
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "booking_id": "BOOK-12345",
      "service": {
        "id": 1,
        "name": "Product Inspection",
        "service_type": "Inspection"
      },
      "scheduled_date": "2025-06-01",
      "status": "Pending",
      "created_at": "2025-05-02T16:30:00Z"
    },
    // More bookings...
  ],
  "pagination": { ... }
}
```

## Webhooks

FastCart supports webhooks for real-time event notifications.

### Available Webhook Events

- `order.created`: Triggered when a new order is created
- `order.status_updated`: Triggered when an order status changes
- `order.payment_received`: Triggered when payment is received
- `product.stock_low`: Triggered when product stock falls below threshold
- `rfq.created`: Triggered when a new RFQ is submitted
- `rfq.status_updated`: Triggered when an RFQ status changes
- `service_booking.created`: Triggered when a new service booking is created
- `service_booking.status_updated`: Triggered when a service booking status changes

### Webhook Payload Example

```json
{
  "event": "order.created",
  "timestamp": "2025-05-02T16:45:00Z",
  "data": {
    "id": 1,
    "order_number": "ORD-12345",
    "total": "209.98",
    "status": "Pending",
    "payment_status": "Pending"
  }
}
```

### Creating a Webhook Subscription

```
POST /api/v1/webhooks/
```

**Request Body:**
```json
{
  "target_url": "https://your-server.com/webhook-receiver",
  "event_types": ["order.created", "order.status_updated"],
  "description": "Order notifications webhook"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "target_url": "https://your-server.com/webhook-receiver",
    "event_types": ["order.created", "order.status_updated"],
    "description": "Order notifications webhook",
    "secret": "whsec_abcdefghijklmnopqrstuvwxyz",
    "active": true,
    "created_at": "2025-05-02T16:50:00Z"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `authentication_error` | Authentication failed |
| `permission_denied` | Insufficient permissions |
| `not_found` | Resource not found |
| `validation_error` | Invalid input data |
| `rate_limit_exceeded` | Rate limit exceeded |
| `server_error` | Internal server error |

## Best Practices

1. **Caching**: Cache responses for frequently accessed, relatively static data
2. **Pagination**: Use pagination for large collections
3. **Filtering**: Use query parameters to filter results
4. **Rate Limiting**: Implement client-side throttling
5. **Error Handling**: Handle errors gracefully with proper fallbacks
6. **Webhook Security**: Verify webhook signatures
7. **Authentication**: Securely store tokens

## SDK Clients

FastCart provides official SDK clients for easy integration:

- [Python SDK](https://github.com/fastcart/python-sdk)
- [JavaScript SDK](https://github.com/fastcart/js-sdk)
- [PHP SDK](https://github.com/fastcart/php-sdk)

## Support

For API support, contact <EMAIL> or visit our [Developer Forum](https://forum.fastcart.com/developers).
