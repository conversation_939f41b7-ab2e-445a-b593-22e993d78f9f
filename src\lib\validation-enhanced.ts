import { z } from 'zod';

// Enhanced validation schemas with security considerations
export const emailSchema = z.string()
  .email('Invalid email format')
  .min(5, 'Email must be at least 5 characters')
  .max(254, 'Email must not exceed 254 characters')
  .transform(email => email.toLowerCase().trim());

export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must not exceed 128 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/\d/, 'Password must contain at least one number')
  .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character');

export const nameSchema = z.string()
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must not exceed 50 characters')
  .regex(/^[a-zA-Z\u0600-\u06FF\s]+$/, 'Name can only contain letters and spaces')
  .transform(name => name.trim());

export const phoneSchema = z.string()
  .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
  .optional();

export const userSchema = z.object({
  email: emailSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  phone: phoneSchema,
  language: z.enum(['ar', 'en']).default('ar'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
});

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const registerSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  phone: phoneSchema,
  language: z.enum(['ar', 'en']).default('ar'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

export const productSchema = z.object({
  name: z.string()
    .min(1, 'Product name is required')
    .max(200, 'Product name must not exceed 200 characters')
    .transform(name => name.trim()),
  name_ar: z.string()
    .min(1, 'Arabic product name is required')
    .max(200, 'Arabic product name must not exceed 200 characters')
    .transform(name => name.trim())
    .optional(),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description must not exceed 2000 characters')
    .transform(desc => desc.trim()),
  description_ar: z.string()
    .min(10, 'Arabic description must be at least 10 characters')
    .max(2000, 'Arabic description must not exceed 2000 characters')
    .transform(desc => desc.trim())
    .optional(),
  price: z.number()
    .positive('Price must be greater than zero')
    .max(999999.99, 'Price must not exceed 999,999.99'),
  salePrice: z.number()
    .positive('Sale price must be greater than zero')
    .max(999999.99, 'Sale price must not exceed 999,999.99')
    .optional(),
  category: z.string()
    .min(1, 'Category is required')
    .max(100, 'Category must not exceed 100 characters'),
  tags: z.array(z.string().max(50)).max(10, 'Maximum 10 tags allowed').optional(),
  inStock: z.boolean().default(true),
  stock: z.number().int().min(0, 'Stock cannot be negative').optional(),
  sku: z.string()
    .min(1, 'SKU is required')
    .max(50, 'SKU must not exceed 50 characters')
    .regex(/^[A-Z0-9-_]+$/, 'SKU can only contain uppercase letters, numbers, hyphens, and underscores'),
});

export const reviewSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must not exceed 5'),
  title: z.string()
    .min(1, 'Review title is required')
    .max(200, 'Review title must not exceed 200 characters')
    .transform(title => title.trim()),
  comment: z.string()
    .min(10, 'Comment must be at least 10 characters')
    .max(1000, 'Comment must not exceed 1000 characters')
    .transform(comment => comment.trim()),
  images: z.array(z.string().url()).max(5, 'Maximum 5 images allowed').optional(),
});

export const addressSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  company: z.string().max(100, 'Company name must not exceed 100 characters').optional(),
  street: z.string()
    .min(5, 'Street address must be at least 5 characters')
    .max(200, 'Street address must not exceed 200 characters'),
  city: z.string()
    .min(2, 'City must be at least 2 characters')
    .max(100, 'City must not exceed 100 characters'),
  state: z.string()
    .min(2, 'State must be at least 2 characters')
    .max(100, 'State must not exceed 100 characters'),
  postalCode: z.string()
    .min(3, 'Postal code must be at least 3 characters')
    .max(20, 'Postal code must not exceed 20 characters')
    .regex(/^[A-Z0-9\s-]+$/i, 'Invalid postal code format'),
  country: z.string()
    .min(2, 'Country is required')
    .max(100, 'Country must not exceed 100 characters'),
  phone: phoneSchema,
  isDefault: z.boolean().optional(),
});

export const orderSchema = z.object({
  items: z.array(z.object({
    productId: z.string().uuid('Invalid product ID'),
    quantity: z.number().int().min(1, 'Quantity must be at least 1').max(999, 'Quantity must not exceed 999'),
  })).min(1, 'Order must contain at least one item'),
  shippingAddress: addressSchema,
  billingAddress: addressSchema.optional(),
  paymentMethod: z.enum(['credit_card', 'paypal', 'stripe', 'cash_on_delivery']),
  notes: z.string().max(500, 'Notes must not exceed 500 characters').optional(),
});

export const wholesaleQuoteSchema = z.object({
  companyName: z.string()
    .min(2, 'Company name must be at least 2 characters')
    .max(200, 'Company name must not exceed 200 characters'),
  contactName: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  productInterest: z.string()
    .min(5, 'Product interest must be at least 5 characters')
    .max(500, 'Product interest must not exceed 500 characters'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1'),
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must not exceed 1000 characters'),
});

export const contactFormSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  company: z.string().max(100, 'Company name must not exceed 100 characters').optional(),
  subject: z.string()
    .min(1, 'Subject is required')
    .max(200, 'Subject must not exceed 200 characters'),
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must not exceed 1000 characters'),
  department: z.string().min(1, 'Department is required'),
});

export const serviceRequestSchema = z.object({
  fullName: nameSchema,
  email: emailSchema,
  phone: z.string().min(1, 'Phone number is required'),
  companyName: z.string().max(100, 'Company name must not exceed 100 characters').optional(),
  serviceDate: z.string().optional(),
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must not exceed 1000 characters'),
});

// Type exports
export type UserInput = z.infer<typeof userSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ProductInput = z.infer<typeof productSchema>;
export type ReviewInput = z.infer<typeof reviewSchema>;
export type AddressInput = z.infer<typeof addressSchema>;
export type OrderInput = z.infer<typeof orderSchema>;
export type WholesaleQuoteInput = z.infer<typeof wholesaleQuoteSchema>;
export type ContactFormInput = z.infer<typeof contactFormSchema>;
export type ServiceRequestInput = z.infer<typeof serviceRequestSchema>;

// Validation helper functions
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: boolean; data?: T; errors?: string[] } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`) 
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

// Sanitization functions
export function sanitizeHtml(input: string): string {
  return input
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '')
    .trim();
}

export function sanitizeFileName(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .substring(0, 255);
}

export function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url);
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol');
    }
    return parsed.toString();
  } catch {
    return '';
  }
}
