export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'admin';
  password_hash?: string; // Added for server-side creation
  createdAt: string;
  updatedAt?: string;
  avatarUrl?: string;
  phoneNumber?: string;
  addresses?: Array<{ // Define a basic structure for addresses
    type: 'billing' | 'shipping' | 'other';
    street: string;
    city: string;
    postalCode: string;
    country: string;
    isDefault?: boolean;
  }>;
  emailVerified?: boolean;
  lastLogin?: string;
  isActive?: boolean; // Added to represent user's active status
  failedLoginAttempts?: number; // Added for account security
  lastFailedLogin?: string;   // Added for account security
  lockoutUntil?: string;      // Added for account security
}

export interface Product {
  id: string;
  name: string;
  name_ar?: string; // Added for Arabic name
  slug: string;
  description: string;
  description_ar?: string; // Added for Arabic description
  price: number;
  compareAtPrice?: number;
  images: string[]; // Array of image URLs
  category: string;
  tags: string[];
  stock: number; // Current stock level
  inStock?: boolean; // Added: true if stock > 0
  featured: boolean;
  specifications: Record<string, string>; // Key-value pairs for product specs
  createdAt: string; // ISO date string
  updatedAt?: string; // Added: ISO date string for last update
  reviews?: Review[];
  rating?: number; // Average rating
  reviewCount?: number;
  relatedProducts?: string[]; // IDs of related products
  // Trivial comment to attempt to force type re-evaluation
}

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

export interface ClearanceItem {
  id: string;
  name: string;
  description: string;
  originalPrice: number;
  clearancePrice: number;
  minOrder: number;
  availableQuantity: number;
  image: string;
  category: string;
  condition: 'new' | 'like-new' | 'used';
  location: string;
  expiryDate?: string;
}

export interface RFQ {
  id: string;
  userId: string;
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  productType: string;
  quantity: number;
  details: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  createdAt: string;
}

export interface Service {
  id: string | number;
  name: string;
  name_ar?: string; // الاسم بالعربية
  slug: string;
  description: string;
  description_ar?: string; // الوصف بالعربية
  icon: string;
  features: string[];
  features_ar?: string[]; // الميزات بالعربية
  createdAt: string;
}

export interface ProductionLine {
  id: string;
  name: string;
  slug: string;
  description: string;
  capacity: string;
  specifications: Record<string, string>;
  images: string[];
  category: string;
  features: string[];
  createdAt: string;
}

export interface BlogPost {
  id: string;
  title: string;
  title_ar?: string;
  slug: string;
  excerpt: string;
  excerpt_ar?: string;
  content: string;
  content_ar?: string;
  author: string;
  authorTitle: string;
  authorImage: string;
  coverImage: string;
  category: string;
  tags: string[];
  publishedAt: string;
  readTime: string;
  featured: boolean;
}

export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number; // 1-5
  title: string;
  comment: string;
  createdAt: string;
  updatedAt?: string;
  helpful: number; // عدد المستخدمين الذين وجدوا المراجعة مفيدة
  verified: boolean; // هل المستخدم اشترى المنتج بالفعل
  images?: string[]; // صور أضافها المستخدم للمراجعة
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'credit_card' | 'paypal' | 'stripe' | 'mada' | 'bank_transfer' | 'cash_on_delivery';
  icon: string;
  supportedCurrencies: string[];
  processingFee?: number;
  processingFeeType?: 'fixed' | 'percentage';
  enabled: boolean;
}

export interface Currency {
  code: string; // USD, SAR, CNY
  name: string; // US Dollar, Saudi Riyal, Chinese Yuan
  symbol: string; // $, ﷼, ¥
  rate: number; // سعر الصرف مقابل العملة الأساسية (USD)
  default?: boolean;
}

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDelivery: string; // مثل "3-5 أيام عمل"
  countries: string[]; // البلدان المدعومة أو '*' للكل
  icon: string;
  enabled: boolean;
}

export interface NavigationItem {
  title: string;
  href: string;
  submenu?: {
    title: string;
    href: string;
    description?: string;
  }[];
  label?: string; // Para compatibilidad con MobileMenu
}

export interface ProductCategory {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  image: string;
  subcategories?: ProductCategory[]; // Optional: for nested categories
}

export interface ProductFiltersState {
  category: string;
  priceRange: { min: number; max: number };
  inStock: boolean;
  onSale: boolean;
  featured: boolean;
  searchQuery: string;
}

export type SortOption = 'featured' | 'newest' | 'price-asc' | 'price-desc' | 'popular' | 'discount';

/**
 * Represents an authentication-specific error.
 */
export interface AuthError extends Error {
  status?: number;
}

/**
 * Represents the structure of an author for blog posts or similar content.
 */

// Enum for Order Status
export enum OrderStatus {
  pending,
  processing,
  completed,
  cancelled,
}