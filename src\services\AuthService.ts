import { sqlite } from '../lib/sqlite';
import {
  getLocalUsers,
  saveLocalUsers,
  getLocalProfiles,
  saveLocalProfiles,
  createDefaultAdminUser as createAdmin,
  resetLocalUsers,
  findUserByCredentials,
  findProfileById,
  findProfileByEmail,
  saveProfile
} from './SQLiteUserService';

// إنشاء مستخدم مدير افتراضي للتطوير المحلي
export async function createDefaultAdminUser() {
  console.log('Creating default admin user using SQLiteUserService...');

  // استخدام الدالة من SQLiteUserService
  const result = await createAdmin();

  console.log('Default admin user creation result:', result);
  return result;
}

// إنشاء مستخدم مدير ثاني
export async function createSecondAdminUser() {
  console.log('Creating second admin user...');

  try {
    const userData = {
      email: '<EMAIL>',
      password: 'admin123',
      firstName: 'Admin',
      lastName: 'Two',
      role: 'admin' as const,
    };

    const result = await sqlite.createUser(userData);
    console.log('Second admin user creation result:', result);
    return result;
  } catch (error) {
    console.error('Error creating second admin user:', error);
    return null;
  }
}

// تهيئة المستخدمين الافتراضيين
export async function initializeDefaultUsers() {
  if (typeof window === 'undefined') {
    return; // تنفيذ فقط في جانب العميل
  }

  try {
    await createDefaultAdminUser();
    await createSecondAdminUser();
    console.log('Default users initialized');
    return { success: true };
  } catch (error) {
    console.error('Initialize default users exception:', error);
    return { success: false, error: 'حدث خطأ غير متوقع أثناء تهيئة المستخدمين الافتراضيين' };
  }
}
