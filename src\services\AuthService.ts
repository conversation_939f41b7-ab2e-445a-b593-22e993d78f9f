import { sqlite } from '../lib/sqlite';
import {
  getLocalUsers,
  saveLocalUsers,
  getLocalProfiles,
  saveLocalProfiles,
  createDefaultAdminUser as createAdmin,
  resetLocalUsers,
  findUserByCredentials,
  findProfileById,
  findProfileByEmail,
  saveProfile
} from './SQLiteUserService';

// إنشاء مستخدم مدير افتراضي للتطوير المحلي
export async function createDefaultAdminUser() {
  console.log('Creating default admin user using SQLiteUserService...');

  // استخدام الدالة من SQLiteUserService
  const result = await createAdmin();

  console.log('Default admin user creation result:', result);
  return result;
}

// تهيئة المستخدمين الافتراضيين
export async function initializeDefaultUsers() {
  if (typeof window === 'undefined') {
    return; // تنفيذ فقط في جانب العميل
  }

  try {
    await createDefaultAdminUser();
    console.log('Default users initialized');
    return { success: true };
  } catch (error) {
    console.error('Initialize default users exception:', error);
    return { success: false, error: 'حدث خطأ غير متوقع أثناء تهيئة المستخدمين الافتراضيين' };
  }
}
