
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/pages/FAQPage.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/pages</a> FAQPage.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/14</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/90</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/3</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/14</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use client';
&nbsp;
import { HelpCircle } from <span class="cstat-no" title="statement not covered" >'lucide-react';</span>
import { Card } from <span class="cstat-no" title="statement not covered" >'../components/ui/Card';</span>
import { useLanguageStore } from <span class="cstat-no" title="statement not covered" >'../stores/languageStore';</span>
import { useTranslation } from <span class="cstat-no" title="statement not covered" >'../translations';</span>
import { ScrollAnimation, ScrollStagger, HoverAnimation } from <span class="cstat-no" title="statement not covered" >'../components/ui/animations';</span>
import { useThemeStore } from <span class="cstat-no" title="statement not covered" >'../stores/themeStore';</span>
import { cn } from <span class="cstat-no" title="statement not covered" >'../lib/utils';</span>
&nbsp;
export default function <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >FAQPage() {</span></span>
  const { language } = <span class="cstat-no" title="statement not covered" >useLanguageStore();</span>
  const { t, locale } = <span class="cstat-no" title="statement not covered" >useTranslation();</span>
  const { isDarkMode } = <span class="cstat-no" title="statement not covered" >useThemeStore();</span>
&nbsp;
  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = <span class="cstat-no" title="statement not covered" >(locale as 'ar' | 'en') || language;</span>
  return (
    &lt;div className="container-custom py-12"&gt;
      &lt;div className="max-w-4xl mx-auto"&gt;
        &lt;ScrollAnimation animation="fade" delay={0.1}&gt;
          &lt;div className="flex items-center justify-center mb-8"&gt;
            &lt;div className="inline-flex justify-center items-center w-24 h-24 rounded-full bg-primary-50 dark:bg-primary-900/20"&gt;
              &lt;HelpCircle className="h-12 w-12 text-primary-500" /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          &lt;h1 className="text-4xl font-bold text-center mb-8 text-slate-900 dark:text-white"&gt;
            {currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
          &lt;/h1&gt;
        &lt;/ScrollAnimation&gt;
&nbsp;
        &lt;ScrollStagger
          animation="fade"
          staggerDelay={0.1}
          className="space-y-6"
        &gt;
          {[
            {
              category: currentLanguage === 'ar' ? "الطلبات والشحن" : "Orders &amp; Shipping",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "كم من الوقت يستغرق الشحن؟" : "How long does shipping take?",
                  a: currentLanguage === 'ar'
                    ? "يستغرق الشحن القياسي 5-7 أيام عمل داخل الولايات المتحدة القارية. الشحن السريع متاح للتسليم خلال 2-3 أيام عمل. تختلف أوقات الشحن الدولي حسب الموقع."
                    : "Standard shipping takes 5-7 business days within the continental US. Express shipping is available for 2-3 business days delivery. International shipping times vary by location."
                },
                {
                  q: currentLanguage === 'ar' ? "هل تشحنون دولياً؟" : "Do you ship internationally?",
                  a: currentLanguage === 'ar'
                    ? "نعم، نشحن إلى أكثر من 100 دولة حول العالم. تختلف أسعار الشحن الدولي وأوقات التسليم حسب الموقع."
                    : "Yes, we ship to over 100 countries worldwide. International shipping rates and delivery times vary by location."
                },
                {
                  q: currentLanguage === 'ar' ? "كيف يمكنني تتبع طلبي؟" : "How can I track my order?",
                  a: currentLanguage === 'ar'
                    ? "بمجرد شحن طلبك، ستتلقى رقم تتبع عبر البريد الإلكتروني. يمكنك أيضًا تتبع طلبك عن طريق تسجيل الدخول إلى حسابك."
                    : "Once your order ships, you'll receive a tracking number via email. You can also track your order by logging into your account."
                },
                {
                  q: currentLanguage === 'ar' ? "ما هي طرق الدفع التي تقبلونها؟" : "What payment methods do you accept?",
                  a: currentLanguage === 'ar'
                    ? "نقبل جميع بطاقات الائتمان الرئيسية، وباي بال، والتحويلات المصرفية للحسابات التجارية."
                    : "We accept all major credit cards, PayPal, and bank transfers for business accounts."
                }
              ]
            },
            {
              category: currentLanguage === 'ar' ? "الإرجاع واسترداد الأموال" : "Returns &amp; Refunds",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "ما هي سياسة الإرجاع لديكم؟" : "What is your return policy?",
                  a: currentLanguage === 'ar'
                    ? "نقبل الإرجاع خلال 30 يومًا من التسليم لمعظم العناصر في حالتها الأصلية. قد تكون للطلبات المخصصة والمشتريات بالجملة شروط مختلفة."
                    : "We accept returns within 30 days of delivery for most items in their original condition. Custom orders and bulk purchases may have different terms."
                },
                {
                  q: currentLanguage === 'ar' ? "كيف أبدأ عملية الإرجاع؟" : "How do I initiate a return?",
                  a: currentLanguage === 'ar'
                    ? "اتصل بفريق خدمة العملاء للحصول على تصريح الإرجاع. بمجرد الموافقة، سنقدم تعليمات الشحن."
                    : "Contact our customer service team to receive a return authorization. Once approved, we'll provide shipping instructions."
                },
                {
                  q: currentLanguage === 'ar' ? "متى سأستلم المبلغ المسترد؟" : "When will I receive my refund?",
                  a: currentLanguage === 'ar'
                    ? "تتم معالجة المبالغ المستردة في غضون 5-7 أيام عمل من استلام المرتجع. سيتم إصدار المبلغ المسترد إلى طريقة الدفع الأصلية."
                    : "Refunds are processed within 5-7 business days of receiving your return. The refund will be issued to the original payment method."
                },
                {
                  q: currentLanguage === 'ar' ? "هل تقدمون إرجاع مجاني؟" : "Do you offer free returns?",
                  a: currentLanguage === 'ar'
                    ? "شحن الإرجاع مجاني للعناصر المعيبة أو الأخطاء من جانبنا. قد تتطلب عمليات الإرجاع القياسية دفع رسوم شحن الإرجاع."
                    : "Return shipping is free for defective items or errors on our part. Standard returns may require return shipping payment."
                }
              ]
            },
            {
              category: currentLanguage === 'ar' ? "المنتجات والخدمات" : "Products &amp; Services",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "هل تقدمون أسعاراً خاصة للطلبات بالجملة؟" : "Do you offer bulk pricing?",
                  a: currentLanguage === 'ar'
                    ? "نعم، نقدم أسعاراً خاصة للطلبات بالجملة. اتصل بفريق المبيعات للحصول على عرض سعر مخصص."
                    : "Yes, we offer special pricing for bulk orders. Contact our sales team for a custom quote."
                },
                {
                  q: currentLanguage === 'ar' ? "هل يمكنني تخصيص المنتجات؟" : "Can I customize products?",
                  a: currentLanguage === 'ar'
                    ? "نعم، يمكن تخصيص العديد من منتجاتنا. قد تنطبق الحد الأدنى لكميات الطلب."
                    : "Yes, many of our products can be customized. Minimum order quantities may apply."
                },
                {
                  q: currentLanguage === 'ar' ? "ما هي معايير الجودة التي تتبعونها؟" : "What quality standards do you follow?",
                  a: currentLanguage === 'ar'
                    ? "تلبي جميع منتجاتنا معايير الجودة الدولية وتخضع لاختبارات صارمة."
                    : "All our products meet international quality standards and undergo rigorous testing."
                },
                {
                  q: currentLanguage === 'ar' ? "هل تقدمون الدعم الفني؟" : "Do you provide technical support?",
                  a: currentLanguage === 'ar'
                    ? "نعم، فريق الدعم الفني لدينا متاح خلال ساعات العمل للمساعدة في الأسئلة المتعلقة بالمنتج."
                    : "Yes, our technical support team is available during business hours to assist with product-related questions."
                }
              ]
            },
            {
              category: currentLanguage === 'ar' ? "الحساب والأمان" : "Account &amp; Security",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "كيف أنشئ حساباً؟" : "How do I create an account?",
                  a: currentLanguage === 'ar'
                    ? "انقر على زر 'التسجيل' في الزاوية العلوية اليمنى واتبع عملية التسجيل."
                    : "Click the 'Sign Up' button in the top right corner and follow the registration process."
                },
                {
                  q: currentLanguage === 'ar' ? "هل معلومات الدفع الخاصة بي آمنة؟" : "Is my payment information secure?",
                  a: currentLanguage === 'ar'
                    ? "نعم، نستخدم تشفيراً بمعايير الصناعة لحماية معلومات الدفع الخاصة بك."
                    : "Yes, we use industry-standard encryption to protect your payment information."
                },
                {
                  q: currentLanguage === 'ar' ? "هل يمكنني تغيير تفاصيل حسابي؟" : "Can I change my account details?",
                  a: currentLanguage === 'ar'
                    ? "نعم، يمكنك تحديث معلومات حسابك في أي وقت عن طريق تسجيل الدخول وزيارة إعدادات حسابك."
                    : "Yes, you can update your account information anytime by logging in and visiting your account settings."
                },
                {
                  q: currentLanguage === 'ar' ? "ماذا يحدث إذا نسيت كلمة المرور الخاصة بي؟" : "What happens if I forget my password?",
                  a: currentLanguage === 'ar'
                    ? "استخدم رابط 'نسيت كلمة المرور' في صفحة تسجيل الدخول لإعادة تعيين كلمة المرور الخاصة بك عبر البريد الإلكتروني."
                    : "Use the 'Forgot Password' link on the login page to reset your password via email."
                }
              ]
            }
          ].map(<span class="fstat-no" title="function not covered" >(s</span>ection, index) =&gt; (
<span class="cstat-no" title="statement not covered" >            &lt;div key={index} c</span>lassName="mb-12"&gt;
              &lt;h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-white"&gt;{section.category}&lt;/h2&gt;
              &lt;ScrollStagger
                animation="slide"
                direction="up"
                staggerDelay={0.05}
                className="space-y-4"
              &gt;
                {section.questions.map(<span class="fstat-no" title="function not covered" >(i</span>tem, i) =&gt; (
<span class="cstat-no" title="statement not covered" >                  &lt;HoverAnimation key={i} a</span>nimation="lift"&gt;
                    &lt;Card className="p-6"&gt;
                      &lt;h3 className="text-lg font-semibold mb-3 text-slate-900 dark:text-white"&gt;{item.q}&lt;/h3&gt;
                      &lt;p className="text-slate-600 dark:text-slate-300"&gt;{item.a}&lt;/p&gt;
                    &lt;/Card&gt;
                  &lt;/HoverAnimation&gt;
                ))}
              &lt;/ScrollStagger&gt;
            &lt;/div&gt;
          ))}
        &lt;/ScrollStagger&gt;
&nbsp;
        &lt;ScrollAnimation animation="fade" delay={0.4} className="mt-12"&gt;
          &lt;div className={cn("p-8 rounded-lg", isDarkMode ? "bg-slate-800" : "bg-primary-50")}&gt;
            &lt;h2 className="text-2xl font-semibold mb-4 text-slate-900 dark:text-white"&gt;
              {currentLanguage === 'ar' ? 'هل لا تزال لديك أسئلة؟' : 'Still Have Questions?'}
            &lt;/h2&gt;
            &lt;p className="text-slate-600 dark:text-slate-300 mb-4"&gt;
              {currentLanguage === 'ar'
                ? 'فريق خدمة العملاء لدينا هنا للمساعدة. اتصل بنا:'
                : 'Our customer service team is here to help. Contact us:'}
            &lt;/p&gt;
            &lt;ul className="list-none space-y-3 text-slate-700 dark:text-slate-300"&gt;
              &lt;li className="flex items-center"&gt;
                &lt;span className="font-medium"&gt;{currentLanguage === 'ar' ? 'البريد الإلكتروني:' : 'Email:'}&lt;/span&gt;
                &lt;span className="ml-2"&gt;<EMAIL>&lt;/span&gt;
              &lt;/li&gt;
              &lt;li className="flex items-center"&gt;
                &lt;span className="font-medium"&gt;{currentLanguage === 'ar' ? 'الهاتف:' : 'Phone:'}&lt;/span&gt;
                &lt;span className="ml-2"&gt;+****************&lt;/span&gt;
              &lt;/li&gt;
              &lt;li className="flex items-center"&gt;
                &lt;span className="font-medium"&gt;{currentLanguage === 'ar' ? 'ساعات العمل:' : 'Hours:'}&lt;/span&gt;
                &lt;span className="ml-2"&gt;{currentLanguage === 'ar' ? 'الاثنين - الجمعة، 9:00 صباحاً - 6:00 مساءً بالتوقيت الشرقي' : 'Monday - Friday, 9:00 AM - 6:00 PM ET'}&lt;/span&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
          &lt;/div&gt;
        &lt;/ScrollAnimation&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  );
}</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-25T00:29:14.893Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    