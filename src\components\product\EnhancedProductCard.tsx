'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Heart,
  ShoppingCart,
  Eye,
  Star,
  Truck,
  Package,
  Sparkles,
  Flame,
  Clock,
  Plus,
  Minus,
  Check,
  ShoppingBag
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { EnhancedImage } from '../ui/EnhancedImage';
import { Badge } from '../ui/Badge';
import { Tooltip } from '../ui/Tooltip';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { formatCurrency, cn } from '../../lib/utils';
import { Product } from '../../types/index';
import { useTranslation } from '../../translations';
import { HoverAnimation } from '../ui/animations/HoverAnimation';

interface EnhancedProductCardProps {
  product: Product;
  index?: number;
  className?: string;
  showQuickView?: boolean;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  showQuantity?: boolean;
  onQuickView?: (product: Product) => void;
  onAddToCart?: (product: Product, quantity: number) => void;
  onToggleWishlist?: (product: Product) => void;
  onWholesaleInquiry?: (product: Product) => void;
  viewMode?: 'grid' | 'list';
  badgeText?: string;
  badgeVariant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  badgeIcon?: React.ReactNode;
}

export function EnhancedProductCard({
  product,
  index = 0,
  className = '',
  showQuickView = true,
  showAddToCart = true,
  showWishlist = true,
  showQuantity = false,
  onQuickView,
  onAddToCart,
  onToggleWishlist,
  onWholesaleInquiry,
  viewMode = 'grid',
  badgeText,
  badgeVariant = 'default',
  badgeIcon
}: EnhancedProductCardProps) {
  const { t, locale } = useTranslation();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  const { language } = useLanguageStore();
  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const [imageError, setImageError] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [showAddedToCart, setShowAddedToCart] = useState(false);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // إعادة تعيين حالة الإضافة إلى السلة عند تغيير المنتج
  useEffect(() => {
    setShowAddedToCart(false);
    setIsAddingToCart(false);
  }, [product.id]);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isInStock) return;

    setIsAddingToCart(true);

    // محاكاة تأخير الإضافة إلى السلة
    setTimeout(() => {
      // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
      if (onAddToCart) {
        onAddToCart(product, quantity);
      } else {
        cartStore.addItem(product, quantity);
      }

      setIsAddingToCart(false);
      setShowAddedToCart(true);

      // إخفاء رسالة "تمت الإضافة" بعد 2 ثانية
      setTimeout(() => {
        setShowAddedToCart(false);
      }, 2000);
    }, 500);
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
    if (onToggleWishlist) {
      onToggleWishlist(product);
    } else {
      if (wishlistStore.isInWishlist(product.id)) {
        wishlistStore.removeItem(product.id);
      } else {
        wishlistStore.addItem(product);
      }
    }
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onQuickView) {
      onQuickView(product);
    }
  };

  const handleWholesaleInquiry = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onWholesaleInquiry) {
      onWholesaleInquiry(product);
    }
  };

  // زيادة الكمية
  const incrementQuantity = () => {
    setQuantity(prev => Math.min(prev + 1, 99));
  };

  // إنقاص الكمية
  const decrementQuantity = () => {
    setQuantity(prev => Math.max(prev - 1, 1));
  };

  // Fallback image if the product image fails to load
  const fallbackImage = `/images/product-placeholder-${isDarkMode ? 'dark' : 'light'}.svg`;

  // Use the first product image or fallback if there are no images
  const productImage = imageError || !product.images || product.images.length === 0
    ? fallbackImage
    : product.images[0];

  // تحديد ما إذا كان المنتج في المخزون
  const isInStock = product.stock > 0;

  // حساب نسبة الخصم إذا كان هناك سعر مقارنة
  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price
    ? Math.round((1 - product.price / product.compareAtPrice) * 100)
    : 0;

  // تحديد ما إذا كان المنتج جديدًا (أقل من 14 يومًا)
  const isNew = () => {
    const createdDate = new Date(product.createdAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - createdDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 14;
  };

  // تحديد ما إذا كان المنتج رائجًا (له تقييم عالٍ)
  const isTrending = product.rating && product.rating >= 4.5 && product.reviewCount && product.reviewCount >= 10;

  // تحديد ما إذا كان المنتج محدود الكمية
  const isLimitedStock = isInStock && product.stock <= 5;

  // تحديد ما إذا كان المنتج في السلة
  const isInCart = cartStore.isProductInCart(product.id);

  // تحديد ما إذا كان المنتج في المفضلة
  const isInWishlist = wishlistStore.isInWishlist(product.id);

  // تحديد نوع العرض (شبكي أو قائمة)
  const isList = viewMode === 'list';

  return (
    <HoverAnimation animation="lift">
      <Card
        className={cn(
          "group relative overflow-hidden transition-all duration-300",
          "border border-slate-200 dark:border-slate-700",
          "bg-white dark:bg-slate-800",
          "hover:shadow-lg hover:border-primary-200 dark:hover:border-primary-700",
          isList
            ? "flex flex-row h-full"
            : "flex flex-col h-full",
          className
        )}
      >
        {/* صورة المنتج */}
        <div className={cn(
          "relative overflow-hidden",
          isList ? "w-1/3" : "w-full"
        )}>
          <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
            <div className={cn(
              "relative overflow-hidden",
              isList ? "h-full" : "aspect-square",
              "bg-slate-100 dark:bg-slate-700"
            )}>
              <EnhancedImage
                src={productImage}
                alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                fill={true}
                objectFit="cover"
                progressive={true}
                placeholder="shimmer"
                className="transition-transform duration-500 group-hover:scale-110"
                sizes={isList
                  ? "(max-width: 640px) 33vw, 25vw"
                  : "(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                }
                priority={index < 4}
                onError={() => setImageError(true)}
              />

              {/* تأثير التحويم */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </Link>

          {/* شريط أدوات سريع يظهر عند التحويم */}
          <div className={cn(
            "absolute bottom-0 left-0 right-0 bg-black/70 backdrop-blur-sm py-2 px-3",
            "transform translate-y-full group-hover:translate-y-0 transition-transform duration-300",
            "flex items-center justify-center gap-2 z-20"
          )}>
            <Tooltip content={currentLanguage === 'ar' ? 'نظرة سريعة' : 'Quick View'}>
              <Button
                variant="outline"
                size="icon"
                className="bg-white/20 hover:bg-white/40 text-white rounded-full w-8 h-8 flex items-center justify-center"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleQuickView(e);
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
            </Tooltip>

            <Tooltip content={isInWishlist
              ? (currentLanguage === 'ar' ? 'إزالة من المفضلة' : 'Remove from Wishlist')
              : (currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to Wishlist')
            }>
              <Button
                variant="outline"
                size="icon"
                className={cn(
                  "rounded-full w-8 h-8 flex items-center justify-center",
                  isInWishlist
                    ? "bg-primary-500 text-white hover:bg-primary-600"
                    : "bg-white/20 hover:bg-white/40 text-white"
                )}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleToggleWishlist(e);
                }}
              >
                <Heart className={cn("h-4 w-4", isInWishlist && "fill-current")} />
              </Button>
            </Tooltip>

            {isInStock && (
              <Tooltip content={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}>
                <Button
                  variant="outline"
                  size="icon"
                  className={cn(
                    "rounded-full w-8 h-8 flex items-center justify-center",
                    "bg-primary-500 text-white hover:bg-primary-600"
                  )}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleAddToCart(e);
                  }}
                  disabled={isAddingToCart || isInCart}
                >
                  {isAddingToCart ? (
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  ) : isInCart ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <ShoppingCart className="h-4 w-4" />
                  )}
                </Button>
              </Tooltip>
            )}
          </div>

          {/* شارات المنتج */}
          <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
            {/* شارة مخصصة */}
            {badgeText && (
              <Badge
                variant={badgeVariant === 'error' ? 'destructive' : badgeVariant === 'info' ? 'default' : badgeVariant}
                className="flex items-center"
              >
                {badgeIcon}
                {badgeText}
              </Badge>
            )}

            {/* شارات تلقائية (تظهر فقط إذا لم يتم تحديد شارة مخصصة) */}
            {!badgeText && (
              <>
                {isNew() && (
                  <Badge variant="default" className="animate-pulse">
                    {currentLanguage === 'ar' ? 'جديد' : 'NEW'}
                  </Badge>
                )}
                {discountPercentage > 0 && (
                  <Badge variant="destructive">
                    {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}
                  </Badge>
                )}
                {isTrending && (
                  <Badge variant="warning">
                    <Flame className="w-3 h-3 mr-1" />
                    {currentLanguage === 'ar' ? 'رائج' : 'HOT'}
                  </Badge>
                )}
                {isLimitedStock && (
                  <Badge variant="secondary">
                    <Clock className="w-3 h-3 mr-1" />
                    {currentLanguage === 'ar' ? 'كمية محدودة' : 'LIMITED'}
                  </Badge>
                )}
                {!isInStock && (
                  <Badge variant="secondary" className="bg-slate-500 text-white">
                    {currentLanguage === 'ar' ? 'نفذ المخزون' : 'OUT OF STOCK'}
                  </Badge>
                )}
              </>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <div className={cn(
            "absolute top-2 right-2 flex flex-col gap-1 z-10",
            "opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          )}>
            {showWishlist && (
              <Tooltip content={isInWishlist
                ? (currentLanguage === 'ar' ? 'إزالة من المفضلة' : 'Remove from Wishlist')
                : (currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to Wishlist')
              }>
                <Button
                  variant="outline"
                  size="icon"
                  className={cn(
                    "p-1.5 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110",
                    isInWishlist
                      ? "bg-primary-500 text-white"
                      : "bg-white text-slate-700 dark:bg-slate-700 dark:text-white"
                  )}
                  onClick={handleToggleWishlist}
                  aria-label={isInWishlist
                    ? (currentLanguage === 'ar' ? 'إزالة من المفضلة' : 'Remove from Wishlist')
                    : (currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to Wishlist')
                  }
                >
                  <Heart
                    className={cn(
                      "h-4 w-4",
                      isInWishlist && "fill-current"
                    )}
                  />
                </Button>
              </Tooltip>
            )}

            {showQuickView && onQuickView && (
              <Tooltip content={currentLanguage === 'ar' ? 'نظرة سريعة' : 'Quick View'}>
                <Button
                  variant="outline"
                  size="icon"
                  className="p-1.5 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110"
                  onClick={handleQuickView}
                  aria-label={currentLanguage === 'ar' ? 'نظرة سريعة' : 'Quick View'}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </Tooltip>
            )}
          </div>

          {/* طبقة التراكب عند التحويم */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>

        {/* محتوى المنتج */}
        <div className={cn(
          "flex flex-col",
          isList ? "flex-1 p-6" : "p-4"
        )}>
          {/* الفئة والتقييم */}
          <div className="flex items-center justify-between mb-2">
            {product.category && (
              <span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                {product.category}
              </span>
            )}

            <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
              <Star className={`h-4 w-4 text-yellow-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
              <span>
                {product.rating?.toFixed(1) ?? 'N/A'}
                {product.reviewCount ? ` (${product.reviewCount})` : ''}
              </span>
            </div>
          </div>

          {/* اسم المنتج */}
          <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
            <h3 className={cn(
              "font-semibold text-slate-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200",
              isList ? "text-xl mb-2" : "text-lg mb-1 line-clamp-1"
            )}>
              {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
            </h3>
          </Link>

          {/* وصف المنتج */}
          <p className={cn(
            "text-slate-600 dark:text-slate-300 text-sm",
            isList ? "mb-4" : "mb-3 line-clamp-2"
          )}>
            {currentLanguage === 'ar'
              ? (product.description_ar || product.description)
              : product.description}
          </p>

          {/* السعر والمخزون */}
          <div className="flex items-center justify-between mb-3 mt-auto">
            <div className="flex items-baseline gap-1">
              <span className="text-lg font-bold text-slate-900 dark:text-white">
                {formatCurrency(product.price)}
              </span>
              {product.compareAtPrice && product.compareAtPrice > product.price && (
                <span className="text-sm text-slate-500 line-through">
                  {formatCurrency(product.compareAtPrice)}
                </span>
              )}
            </div>

            <div className="text-xs font-medium">
              {isInStock ? (
                <span className="text-green-600 dark:text-green-400">
                  {currentLanguage === 'ar' ? 'متوفر' : 'In Stock'}
                </span>
              ) : (
                <span className="text-red-600 dark:text-red-400">
                  {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}
                </span>
              )}
            </div>
          </div>

          {/* معلومات إضافية */}
          {isList && (
            <div className="mb-4 flex flex-wrap gap-4 text-xs text-slate-500 dark:text-slate-400">
              {isInStock && (
                <div className="flex items-center">
                  <Truck className="h-3.5 w-3.5 mr-1" />
                  {currentLanguage === 'ar' ? 'شحن مجاني' : 'Free Shipping'}
                </div>
              )}
              <div className="flex items-center">
                <Package className="h-3.5 w-3.5 mr-1" />
                {product.stock > 10
                  ? (currentLanguage === 'ar' ? 'متوفر بكثرة' : 'In Stock')
                  : product.stock > 0
                    ? (currentLanguage === 'ar' ? `${product.stock} متبقية` : `${product.stock} left`)
                    : (currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock')}
              </div>
              {product.featured && (
                <div className="flex items-center">
                  <Sparkles className="h-3.5 w-3.5 mr-1" />
                  {currentLanguage === 'ar' ? 'منتج مميز' : 'Featured'}
                </div>
              )}
            </div>
          )}

          {/* أزرار الإجراءات */}
          <div className={cn(
            "flex gap-2",
            isList && "flex-wrap"
          )}>
            {/* زر إضافة إلى السلة */}
            {showAddToCart && (
              <div className={cn(
                "flex",
                showQuantity ? "flex-col gap-2 w-full" : "flex-row gap-2"
              )}>
                {showQuantity && (
                  <div className="flex items-center">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-r-none"
                      onClick={decrementQuantity}
                      disabled={quantity <= 1 || !isInStock}
                      aria-label={currentLanguage === 'ar' ? 'إنقاص الكمية' : 'Decrease quantity'}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <div className="h-8 px-3 flex items-center justify-center border border-slate-300 dark:border-slate-600 border-x-0">
                      {quantity}
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-l-none"
                      onClick={incrementQuantity}
                      disabled={quantity >= 99 || !isInStock}
                      aria-label={currentLanguage === 'ar' ? 'زيادة الكمية' : 'Increase quantity'}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                )}

                <Button
                  variant={isInCart || showAddedToCart ? "primary" : "primary"}
                  size="sm"
                  className={cn(
                    "flex-1 rounded-md transition-all duration-300",
                    (isInCart || showAddedToCart) && "bg-green-600 hover:bg-green-700"
                  )}
                  onClick={handleAddToCart}
                  disabled={!isInStock || isAddingToCart || isInCart}
                  aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                >
                  {isAddingToCart ? (
                    <span className="flex items-center">
                      <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                      {currentLanguage === 'ar' ? 'جاري الإضافة...' : 'Adding...'}
                    </span>
                  ) : isInCart || showAddedToCart ? (
                    <span className="flex items-center">
                      <Check className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {currentLanguage === 'ar' ? 'تمت الإضافة' : 'Added'}
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                    </span>
                  )}
                </Button>
              </div>
            )}

            {/* زر طلب عرض سعر للجملة */}
            {isList && onWholesaleInquiry && (
              <Button
                variant="outline"
                size="sm"
                className="rounded-md"
                onClick={handleWholesaleInquiry}
                aria-label={currentLanguage === 'ar' ? 'طلب عرض سعر للجملة' : 'Wholesale Inquiry'}
              >
                <ShoppingBag className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span>{currentLanguage === 'ar' ? 'طلب عرض سعر للجملة' : 'Wholesale Inquiry'}</span>
              </Button>
            )}
          </div>
        </div>
      </Card>
    </HoverAnimation>
  );
}
