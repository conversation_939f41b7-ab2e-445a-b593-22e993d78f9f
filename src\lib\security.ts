/**
 * Comprehensive Security Library
 * Implements CSRF protection, rate limiting, input validation, and security headers
 */

import crypto from 'crypto';
import { NextRequest, NextResponse } from 'next/server';

// Security configuration
const CSRF_TOKEN_LENGTH = 32;
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const MAX_REQUESTS_PER_WINDOW = 100;
const MAX_LOGIN_ATTEMPTS = 5;
const LOGIN_LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

// In-memory stores (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const csrfTokenStore = new Map<string, { token: string; expiresAt: number }>();
const loginAttemptStore = new Map<string, { attempts: number; lockedUntil?: number }>();

/**
 * Generate CSRF token
 */
export function generateCSRFToken(sessionId: string): string {
  const token = crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
  const expiresAt = Date.now() + (60 * 60 * 1000); // 1 hour
  
  csrfTokenStore.set(sessionId, { token, expiresAt });
  return token;
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(sessionId: string, token: string): boolean {
  const stored = csrfTokenStore.get(sessionId);
  
  if (!stored || stored.expiresAt < Date.now()) {
    csrfTokenStore.delete(sessionId);
    return false;
  }
  
  return crypto.timingSafeEqual(
    Buffer.from(stored.token, 'hex'),
    Buffer.from(token, 'hex')
  );
}

/**
 * Rate limiting implementation
 */
export function checkRateLimit(identifier: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;
  
  // Clean up expired entries
  for (const [key, value] of rateLimitStore.entries()) {
    if (value.resetTime < now) {
      rateLimitStore.delete(key);
    }
  }
  
  const current = rateLimitStore.get(identifier);
  
  if (!current || current.resetTime < now) {
    // New window
    const resetTime = now + RATE_LIMIT_WINDOW;
    rateLimitStore.set(identifier, { count: 1, resetTime });
    return { allowed: true, remaining: MAX_REQUESTS_PER_WINDOW - 1, resetTime };
  }
  
  if (current.count >= MAX_REQUESTS_PER_WINDOW) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  return { 
    allowed: true, 
    remaining: MAX_REQUESTS_PER_WINDOW - current.count, 
    resetTime: current.resetTime 
  };
}

/**
 * Login attempt tracking
 */
export function trackLoginAttempt(identifier: string, success: boolean): { allowed: boolean; attemptsRemaining: number; lockedUntil?: number } {
  const now = Date.now();
  const current = loginAttemptStore.get(identifier);
  
  if (success) {
    // Reset on successful login
    loginAttemptStore.delete(identifier);
    return { allowed: true, attemptsRemaining: MAX_LOGIN_ATTEMPTS };
  }
  
  if (!current) {
    loginAttemptStore.set(identifier, { attempts: 1 });
    return { allowed: true, attemptsRemaining: MAX_LOGIN_ATTEMPTS - 1 };
  }
  
  // Check if still locked
  if (current.lockedUntil && current.lockedUntil > now) {
    return { allowed: false, attemptsRemaining: 0, lockedUntil: current.lockedUntil };
  }
  
  current.attempts++;
  
  if (current.attempts >= MAX_LOGIN_ATTEMPTS) {
    current.lockedUntil = now + LOGIN_LOCKOUT_DURATION;
    return { allowed: false, attemptsRemaining: 0, lockedUntil: current.lockedUntil };
  }
  
  return { allowed: true, attemptsRemaining: MAX_LOGIN_ATTEMPTS - current.attempts };
}

/**
 * Input sanitization
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return { valid: errors.length === 0, errors };
}

/**
 * Security headers middleware
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // Content Security Policy
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';"
  );
  
  // Other security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // HSTS (only in production with HTTPS)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  return response;
}

/**
 * Get client IP address
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return request.ip || 'unknown';
}

/**
 * Security middleware for API routes
 */
export function securityMiddleware(request: NextRequest): NextResponse | null {
  const ip = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  // Rate limiting
  const rateLimit = checkRateLimit(ip);
  if (!rateLimit.allowed) {
    const response = NextResponse.json(
      { error: 'Rate limit exceeded' },
      { status: 429 }
    );
    response.headers.set('Retry-After', Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString());
    return addSecurityHeaders(response);
  }
  
  // CSRF protection for state-changing operations
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
    const csrfToken = request.headers.get('x-csrf-token');
    const sessionId = request.headers.get('x-session-id');
    
    if (!csrfToken || !sessionId || !validateCSRFToken(sessionId, csrfToken)) {
      const response = NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403 }
      );
      return addSecurityHeaders(response);
    }
  }
  
  return null; // Continue to next middleware
}

/**
 * Clean up expired tokens and rate limit entries
 */
export function cleanupSecurityStore(): void {
  const now = Date.now();
  
  // Clean CSRF tokens
  for (const [key, value] of csrfTokenStore.entries()) {
    if (value.expiresAt < now) {
      csrfTokenStore.delete(key);
    }
  }
  
  // Clean rate limit entries
  for (const [key, value] of rateLimitStore.entries()) {
    if (value.resetTime < now) {
      rateLimitStore.delete(key);
    }
  }
  
  // Clean login attempts
  for (const [key, value] of loginAttemptStore.entries()) {
    if (value.lockedUntil && value.lockedUntil < now) {
      loginAttemptStore.delete(key);
    }
  }
}

// Run cleanup every 5 minutes
if (typeof window === 'undefined') {
  setInterval(cleanupSecurityStore, 5 * 60 * 1000);
}
