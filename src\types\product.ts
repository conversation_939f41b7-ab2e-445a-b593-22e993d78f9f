export interface Product {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  description: string;
  description_ar: string;
  shortDescription?: string;
  shortDescription_ar?: string;
  sku: string;
  barcode?: string;
  price: number;
  salePrice?: number;
  costPrice?: number;
  currency: string;
  weight?: number;
  dimensions?: ProductDimensions;
  category: ProductCategory;
  subcategory?: ProductCategory;
  brand?: ProductBrand;
  tags: string[];
  images: ProductImage[];
  variants?: ProductVariant[];
  attributes: ProductAttribute[];
  specifications: ProductSpecification[];
  features: string[];
  features_ar: string[];
  inventory: ProductInventory;
  seo: ProductSEO;
  status: ProductStatus;
  visibility: ProductVisibility;
  isFeatured: boolean;
  isDigital: boolean;
  requiresShipping: boolean;
  allowBackorders: boolean;
  trackQuantity: boolean;
  soldIndividually: boolean;
  reviews: ProductReview[];
  rating: ProductRating;
  relatedProducts: string[];
  crossSellProducts: string[];
  upSellProducts: string[];
  downloadableFiles?: DownloadableFile[];
  externalUrl?: string;
  affiliateUrl?: string;
  videoUrl?: string;
  warranty?: ProductWarranty;
  returnPolicy?: string;
  shippingClass?: string;
  taxClass?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'in' | 'm';
}

export interface ProductCategory {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  parentId?: string;
}

export interface ProductBrand {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  description?: string;
}

export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  alt_ar: string;
  isPrimary: boolean;
  sortOrder: number;
  size?: number;
  width?: number;
  height?: number;
}

export interface ProductVariant {
  id: string;
  name: string;
  name_ar: string;
  sku: string;
  price: number;
  salePrice?: number;
  image?: string;
  attributes: VariantAttribute[];
  inventory: ProductInventory;
  isDefault: boolean;
}

export interface VariantAttribute {
  name: string;
  value: string;
  displayName: string;
  displayName_ar: string;
}

export interface ProductAttribute {
  id: string;
  name: string;
  name_ar: string;
  type: AttributeType;
  values: AttributeValue[];
  isRequired: boolean;
  isVariation: boolean;
  isVisible: boolean;
  sortOrder: number;
}

export type AttributeType = 'text' | 'number' | 'select' | 'multiselect' | 'color' | 'image' | 'boolean';

export interface AttributeValue {
  id: string;
  value: string;
  value_ar: string;
  color?: string;
  image?: string;
  sortOrder: number;
}

export interface ProductSpecification {
  name: string;
  name_ar: string;
  value: string;
  value_ar: string;
  unit?: string;
  group?: string;
  group_ar?: string;
}

export interface ProductInventory {
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  lowStockThreshold: number;
  stockStatus: StockStatus;
  manageStock: boolean;
  allowBackorders: boolean;
  soldIndividually: boolean;
  locations?: InventoryLocation[];
}

export type StockStatus = 'in_stock' | 'out_of_stock' | 'on_backorder' | 'discontinued';

export interface InventoryLocation {
  locationId: string;
  locationName: string;
  quantity: number;
  reservedQuantity: number;
}

export interface ProductSEO {
  metaTitle?: string;
  metaTitle_ar?: string;
  metaDescription?: string;
  metaDescription_ar?: string;
  metaKeywords: string[];
  canonicalUrl?: string;
  ogTitle?: string;
  ogTitle_ar?: string;
  ogDescription?: string;
  ogDescription_ar?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterTitle_ar?: string;
  twitterDescription?: string;
  twitterDescription_ar?: string;
  twitterImage?: string;
  structuredData?: Record<string, any>;
}

export type ProductStatus = 'draft' | 'published' | 'archived' | 'deleted';
export type ProductVisibility = 'public' | 'private' | 'password_protected' | 'hidden';

export interface ProductReview {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  isVerifiedPurchase: boolean;
  isApproved: boolean;
  helpfulCount: number;
  reportCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ProductRating {
  average: number;
  count: number;
  distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface DownloadableFile {
  id: string;
  name: string;
  url: string;
  size: number;
  downloadLimit?: number;
  expiryDays?: number;
}

export interface ProductWarranty {
  duration: number;
  unit: 'days' | 'months' | 'years';
  type: 'manufacturer' | 'seller' | 'extended';
  description: string;
  description_ar: string;
  terms?: string;
  terms_ar?: string;
}

export interface ProductSearchFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  priceMin?: number;
  priceMax?: number;
  rating?: number;
  inStock?: boolean;
  onSale?: boolean;
  featured?: boolean;
  tags?: string[];
  attributes?: Record<string, string[]>;
  search?: string;
  sortBy?: ProductSortOption;
  sortOrder?: 'asc' | 'desc';
}

export type ProductSortOption = 
  | 'name'
  | 'price'
  | 'rating'
  | 'popularity'
  | 'newest'
  | 'oldest'
  | 'sales'
  | 'reviews';

export interface ProductListResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    categories: { id: string; name: string; count: number }[];
    brands: { id: string; name: string; count: number }[];
    priceRange: { min: number; max: number };
    attributes: { name: string; values: { value: string; count: number }[] }[];
  };
}

export interface CreateProductRequest {
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  shortDescription?: string;
  shortDescription_ar?: string;
  sku: string;
  price: number;
  salePrice?: number;
  categoryId: string;
  subcategoryId?: string;
  brandId?: string;
  tags: string[];
  images: Omit<ProductImage, 'id'>[];
  attributes: Omit<ProductAttribute, 'id'>[];
  specifications: ProductSpecification[];
  features: string[];
  features_ar: string[];
  inventory: Omit<ProductInventory, 'availableQuantity'>;
  seo?: Partial<ProductSEO>;
  status: ProductStatus;
  visibility: ProductVisibility;
  isFeatured: boolean;
  isDigital: boolean;
  requiresShipping: boolean;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  id: string;
}

// Utility types
export type ProductSummary = Pick<Product,
  | 'id'
  | 'name'
  | 'name_ar'
  | 'slug'
  | 'price'
  | 'salePrice'
  | 'images'
  | 'rating'
  | 'inventory'
  | 'isFeatured'
>;

export type ProductCard = Pick<Product,
  | 'id'
  | 'name'
  | 'name_ar'
  | 'slug'
  | 'price'
  | 'salePrice'
  | 'images'
  | 'rating'
  | 'inventory'
  | 'isFeatured'
  | 'category'
  | 'brand'
>;

export type ProductDetails = Omit<Product, 'reviews'> & {
  reviewsCount: number;
  averageRating: number;
};
