/**
 * Enhanced Order Management Service
 * Comprehensive order processing with payment integration and inventory management
 */

import { getDatabase } from '../lib/database-secure';
import { paymentGateway, PaymentResult } from '../lib/payment-gateway';
import { Product, Order, OrderItem, User } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { validateInput, orderSchema } from '../lib/validation-enhanced';

// Order status types
export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// Enhanced Order interface
export interface EnhancedOrder extends Order {
  orderNumber: string;
  subtotal: number;
  taxAmount: number;
  shippingFee: number;
  discountAmount: number;
  total: number;
  currency: string;
  paymentMethod: string;
  paymentStatus: PaymentStatus;
  paymentReference?: string;
  trackingNumber?: string;
  shippedAt?: string;
  deliveredAt?: string;
  cancelledAt?: string;
  cancellationReason?: string;
  items: OrderItem[];
}

/**
 * Order Management Repository
 */
export class OrderRepository {
  private db: any;

  constructor() {
    this.db = getDatabase();
  }

  /**
   * Generate unique order number
   */
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `ORD-${timestamp.slice(-6)}${random}`;
  }

  /**
   * Calculate order totals
   */
  private calculateOrderTotals(
    items: { productId: string; quantity: number; price: number }[],
    shippingFee: number = 0,
    taxRate: number = 0.15,
    discountAmount: number = 0
  ): { subtotal: number; taxAmount: number; total: number } {
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const taxAmount = subtotal * taxRate;
    const total = subtotal + taxAmount + shippingFee - discountAmount;

    return { subtotal, taxAmount, total };
  }

  /**
   * Check product availability and reserve stock
   */
  private async checkAndReserveStock(items: { productId: string; quantity: number }[]): Promise<boolean> {
    const transaction = this.db.transaction(() => {
      for (const item of items) {
        const product = this.db.prepare('SELECT stock_quantity FROM products WHERE id = ? AND is_active = 1').get(item.productId);

        if (!product || product.stock_quantity < item.quantity) {
          throw new Error(`Insufficient stock for product ${item.productId}`);
        }

        // Reserve stock
        this.db.prepare('UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?')
          .run(item.quantity, item.productId);
      }
    });

    try {
      transaction();
      return true;
    } catch (error) {
      console.error('Stock reservation failed:', error);
      return false;
    }
  }

  /**
   * Release reserved stock
   */
  private releaseStock(items: { productId: string; quantity: number }[]): void {
    const transaction = this.db.transaction(() => {
      for (const item of items) {
        this.db.prepare('UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?')
          .run(item.quantity, item.productId);
      }
    });

    transaction();
  }

  /**
   * Create a new order
   */
  async createOrder(
    userId: string,
    orderData: {
      items: { productId: string; quantity: number }[];
      shippingAddress: any;
      billingAddress?: any;
      paymentMethod: string;
      shippingFee?: number;
      discountAmount?: number;
      notes?: string;
    }
  ): Promise<EnhancedOrder | null> {
    try {
      // Validate order data
      const validation = validateInput(orderSchema, {
        items: orderData.items,
        shippingAddress: orderData.shippingAddress,
        billingAddress: orderData.billingAddress,
        paymentMethod: orderData.paymentMethod,
        notes: orderData.notes,
      });

      if (!validation.success) {
        throw new Error(`Validation failed: ${validation.errors?.join(', ')}`);
      }

      // Get product details and check availability
      const productDetails = [];
      for (const item of orderData.items) {
        const product = this.db.prepare(`
          SELECT id, name, price, sale_price, stock_quantity, sku
          FROM products
          WHERE id = ? AND is_active = 1
        `).get(item.productId);

        if (!product) {
          throw new Error(`Product not found: ${item.productId}`);
        }

        if (product.stock_quantity < item.quantity) {
          throw new Error(`Insufficient stock for product: ${product.name}`);
        }

        productDetails.push({
          ...item,
          name: product.name,
          sku: product.sku,
          price: product.sale_price || product.price,
        });
      }

      // Calculate totals
      const { subtotal, taxAmount, total } = this.calculateOrderTotals(
        productDetails,
        orderData.shippingFee || 0,
        0.15, // 15% tax rate
        orderData.discountAmount || 0
      );

      // Reserve stock
      const stockReserved = await this.checkAndReserveStock(orderData.items);
      if (!stockReserved) {
        throw new Error('Failed to reserve stock');
      }

      // Generate order details
      const orderId = uuidv4();
      const orderNumber = this.generateOrderNumber();
      const now = new Date().toISOString();

      // Create order in database
      const orderInsert = this.db.prepare(`
        INSERT INTO orders (
          id, order_number, user_id, status, subtotal, tax_amount,
          shipping_fee, discount_amount, total, currency, payment_method,
          payment_status, shipping_address, billing_address, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      orderInsert.run(
        orderId,
        orderNumber,
        userId,
        'pending',
        subtotal,
        taxAmount,
        orderData.shippingFee || 0,
        orderData.discountAmount || 0,
        total,
        'SAR',
        orderData.paymentMethod,
        'pending',
        JSON.stringify(orderData.shippingAddress),
        JSON.stringify(orderData.billingAddress || orderData.shippingAddress),
        orderData.notes || '',
        now,
        now
      );

      // Create order items
      const itemInsert = this.db.prepare(`
        INSERT INTO order_items (
          id, order_id, product_id, product_name, product_sku,
          quantity, unit_price, total_price, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const item of productDetails) {
        itemInsert.run(
          uuidv4(),
          orderId,
          item.productId,
          item.name,
          item.sku,
          item.quantity,
          item.price,
          item.price * item.quantity,
          now
        );
      }

      // Return the created order
      return this.getOrderById(orderId);
    } catch (error) {
      console.error('Order creation failed:', error);

      // Release any reserved stock
      if (orderData.items) {
        this.releaseStock(orderData.items);
      }

      throw error;
    }
  }

  /**
   * Get order by ID with items
   */
  async getOrderById(orderId: string): Promise<EnhancedOrder | null> {
    try {
      const order = this.db.prepare(`
        SELECT * FROM orders WHERE id = ?
      `).get(orderId);

      if (!order) {
        return null;
      }

      const items = this.db.prepare(`
        SELECT * FROM order_items WHERE order_id = ?
      `).all(orderId);

      return {
        ...order,
        shippingAddress: JSON.parse(order.shipping_address || '{}'),
        billingAddress: JSON.parse(order.billing_address || '{}'),
        items: items.map(item => ({
          id: item.id,
          productId: item.product_id,
          productName: item.product_name,
          productSku: item.product_sku,
          quantity: item.quantity,
          unitPrice: item.unit_price,
          totalPrice: item.total_price,
        })),
      } as EnhancedOrder;
    } catch (error) {
      console.error('Failed to get order:', error);
      return null;
    }
  }

  /**
   * Get orders by user ID
   */
  async getOrdersByUserId(userId: string, limit: number = 50, offset: number = 0): Promise<EnhancedOrder[]> {
    try {
      const orders = this.db.prepare(`
        SELECT * FROM orders
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `).all(userId, limit, offset);

      const ordersWithItems = [];
      for (const order of orders) {
        const items = this.db.prepare(`
          SELECT * FROM order_items WHERE order_id = ?
        `).all(order.id);

        ordersWithItems.push({
          ...order,
          shippingAddress: JSON.parse(order.shipping_address || '{}'),
          billingAddress: JSON.parse(order.billing_address || '{}'),
          items: items.map(item => ({
            id: item.id,
            productId: item.product_id,
            productName: item.product_name,
            productSku: item.product_sku,
            quantity: item.quantity,
            unitPrice: item.unit_price,
            totalPrice: item.total_price,
          })),
        });
      }

      return ordersWithItems as EnhancedOrder[];
    } catch (error) {
      console.error('Failed to get user orders:', error);
      return [];
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string, status: OrderStatus, metadata?: any): Promise<boolean> {
    try {
      const updateData: any = { status, updated_at: new Date().toISOString() };

      if (status === 'shipped' && metadata?.trackingNumber) {
        updateData.tracking_number = metadata.trackingNumber;
        updateData.shipped_at = new Date().toISOString();
      } else if (status === 'delivered') {
        updateData.delivered_at = new Date().toISOString();
      } else if (status === 'cancelled') {
        updateData.cancelled_at = new Date().toISOString();
        updateData.cancellation_reason = metadata?.reason || '';

        // Release stock for cancelled orders
        const order = await this.getOrderById(orderId);
        if (order) {
          this.releaseStock(order.items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
          })));
        }
      }

      const stmt = this.db.prepare(`
        UPDATE orders
        SET status = ?, updated_at = ?, tracking_number = ?, shipped_at = ?,
            delivered_at = ?, cancelled_at = ?, cancellation_reason = ?
        WHERE id = ?
      `);

      const result = stmt.run(
        updateData.status,
        updateData.updated_at,
        updateData.tracking_number || null,
        updateData.shipped_at || null,
        updateData.delivered_at || null,
        updateData.cancelled_at || null,
        updateData.cancellation_reason || null,
        orderId
      );

      return result.changes > 0;
    } catch (error) {
      console.error('Failed to update order status:', error);
      return false;
    }
  }

  /**
   * Process payment for order
   */
  async processPayment(orderId: string, paymentMethodId?: string): Promise<PaymentResult> {
    try {
      const order = await this.getOrderById(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      if (order.paymentStatus === 'paid') {
        return { success: true, status: 'already_paid' };
      }

      // Create payment intent
      const paymentIntent = await paymentGateway.createPaymentIntent(
        order.paymentMethod,
        order.total,
        order.currency,
        { orderId, orderNumber: order.orderNumber }
      );

      // Confirm payment
      const paymentResult = await paymentGateway.confirmPayment(
        order.paymentMethod,
        paymentIntent.id,
        paymentMethodId
      );

      // Update order payment status
      if (paymentResult.success) {
        this.db.prepare(`
          UPDATE orders
          SET payment_status = 'paid', payment_reference = ?, updated_at = ?
          WHERE id = ?
        `).run(paymentResult.paymentId, new Date().toISOString(), orderId);

        // Update order status to processing
        await this.updateOrderStatus(orderId, 'processing');
      } else {
        this.db.prepare(`
          UPDATE orders
          SET payment_status = 'failed', updated_at = ?
          WHERE id = ?
        `).run(new Date().toISOString(), orderId);
      }

      return paymentResult;
    } catch (error) {
      console.error('Payment processing failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Refund order
   */
  async refundOrder(orderId: string, amount?: number, reason?: string): Promise<boolean> {
    try {
      const order = await this.getOrderById(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      if (order.paymentStatus !== 'paid') {
        throw new Error('Order is not paid');
      }

      // Process refund through payment gateway
      const refundResult = await paymentGateway.refundPayment(
        order.paymentMethod,
        order.paymentReference!,
        amount
      );

      if (refundResult.success) {
        // Update order status
        this.db.prepare(`
          UPDATE orders
          SET payment_status = 'refunded', status = 'refunded',
              cancellation_reason = ?, updated_at = ?
          WHERE id = ?
        `).run(reason || 'Refunded', new Date().toISOString(), orderId);

        // Release stock
        this.releaseStock(order.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
        })));

        return true;
      }

      return false;
    } catch (error) {
      console.error('Refund failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const orderRepository = new OrderRepository();

// نوع العنوان
interface Address {
  firstName: string;
  lastName: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
}

// نوع عنصر الطلب مع تفاصيل المنتج
interface OrderItemWithProduct extends OrderItem {
  product: Product;
}

// نوع الطلب مع العناصر
interface OrderWithItems extends Order {
  items: OrderItemWithProduct[];
}

/**
 * الحصول على جميع الطلبات للمستخدم
 */
export async function getUserOrders(userId: string): Promise<OrderWithItems[]> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];

    // تصفية الطلبات حسب المستخدم
    const userOrders = orders.filter(order => order.userId === userId);

    // الحصول على عناصر الطلبات
    const orderItemsJson = localStorage.getItem(LOCAL_ORDER_ITEMS_KEY);
    const orderItems: OrderItem[] = orderItemsJson ? JSON.parse(orderItemsJson) : [];

    // الحصول على المنتجات
    const products = sqliteDB.getProducts();

    // إضافة العناصر إلى كل طلب
    const ordersWithItems: OrderWithItems[] = userOrders.map(order => {
      // تصفية عناصر الطلب حسب معرف الطلب
      const items = orderItems.filter(item => item.orderId === order.id);

      // إضافة تفاصيل المنتج إلى كل عنصر
      const itemsWithProducts: OrderItemWithProduct[] = items.map(item => {
        const product = products.find(p => p.id.toString() === item.productId);

        return {
          ...item,
          product: product || {} as Product
        };
      });

      return {
        ...order,
        items: itemsWithProducts
      };
    });

    return ordersWithItems;
  } catch (error) {
    console.error(`Error getting orders for user ${userId}:`, error);
    return [];
  }
}

/**
 * الحصول على طلب بواسطة المعرف
 */
export async function getOrderById(orderId: string): Promise<OrderWithItems | null> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];

    // البحث عن الطلب
    const order = orders.find(order => order.id === orderId);

    if (!order) {
      return null;
    }

    // الحصول على عناصر الطلب
    const orderItemsJson = localStorage.getItem(LOCAL_ORDER_ITEMS_KEY);
    const orderItems: OrderItem[] = orderItemsJson ? JSON.parse(orderItemsJson) : [];

    // تصفية عناصر الطلب حسب معرف الطلب
    const items = orderItems.filter(item => item.orderId === orderId);

    // الحصول على المنتجات
    const products = sqliteDB.getProducts();

    // إضافة تفاصيل المنتج إلى كل عنصر
    const itemsWithProducts: OrderItemWithProduct[] = items.map(item => {
      const product = products.find(p => p.id.toString() === item.productId);

      return {
        ...item,
        product: product || {} as Product
      };
    });

    return {
      ...order,
      items: itemsWithProducts
    };
  } catch (error) {
    console.error(`Error getting order ${orderId}:`, error);
    return null;
  }
}

/**
 * إنشاء طلب جديد
 */
export async function createOrder(userId: string, orderData: Partial<Order>, items: { productId: string; quantity: number }[]): Promise<OrderWithItems | null> {
  try {
    // التحقق من وجود عناصر في الطلب
    if (!items || items.length === 0) {
      throw new Error('لا يمكن إنشاء طلب بدون عناصر');
    }

    // الحصول على المنتجات
    const products = sqliteDB.getProducts();

    // التحقق من وجود جميع المنتجات وتوفرها
    for (const item of items) {
      const product = products.find(p => p.id.toString() === item.productId);

      if (!product) {
        throw new Error(`المنتج غير موجود: ${item.productId}`);
      }

      if (!product.inStock) {
        throw new Error(`المنتج غير متوفر حاليًا: ${product.name}`);
      }
    }

    // حساب إجمالي الطلب
    let subtotal = 0;
    const orderItems: OrderItem[] = [];

    for (const item of items) {
      const product = products.find(p => p.id.toString() === item.productId) as Product;
      const price = product.salePrice || product.price;
      const total = price * item.quantity;

      subtotal += total;

      orderItems.push({
        id: `order-item-${Date.now()}-${item.productId}`,
        orderId: '', // سيتم تحديثه لاحقًا
        productId: item.productId,
        quantity: item.quantity,
        price,
        total,
        createdAt: new Date().toISOString()
      });
    }

    // حساب الضريبة والشحن والخصم
    const tax = orderData.tax || 0;
    const shippingFee = orderData.shippingFee || 0;
    const discount = orderData.discount || 0;

    // حساب الإجمالي النهائي
    const total = subtotal + tax + shippingFee - discount;

    // إنشاء معرف فريد للطلب
    const orderId = `order-${Date.now()}`;
    const now = new Date().toISOString();

    // إنشاء الطلب الجديد
    const newOrder: Order = {
      id: orderId,
      userId,
      status: 'pending',
      total,
      shippingFee,
      tax,
      discount,
      paymentMethod: orderData.paymentMethod || 'cash_on_delivery',
      paymentStatus: 'pending',
      shippingAddress: orderData.shippingAddress || {} as Address,
      billingAddress: orderData.billingAddress || {} as Address,
      notes: orderData.notes || '',
      createdAt: now,
      updatedAt: now
    };

    // تحديث معرف الطلب في عناصر الطلب
    for (let i = 0; i < orderItems.length; i++) {
      orderItems[i].orderId = orderId;
    }

    // حفظ الطلب وعناصره
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];

    const orderItemsJson = localStorage.getItem(LOCAL_ORDER_ITEMS_KEY);
    const allOrderItems: OrderItem[] = orderItemsJson ? JSON.parse(orderItemsJson) : [];

    // إضافة الطلب وعناصره
    orders.push(newOrder);
    allOrderItems.push(...orderItems);

    // حفظ الطلب وعناصره
    localStorage.setItem(LOCAL_ORDERS_KEY, JSON.stringify(orders));
    localStorage.setItem(LOCAL_ORDER_ITEMS_KEY, JSON.stringify(allOrderItems));

    // تفريغ سلة التسوق
    const { clearCart } = await import('./CartService');
    await clearCart(userId);

    // إرجاع الطلب مع العناصر
    return {
      ...newOrder,
      items: orderItems.map(item => {
        const product = products.find(p => p.id.toString() === item.productId);

        return {
          ...item,
          product: product || {} as Product
        };
      })
    };
  } catch (error) {
    console.error(`Error creating order for user ${userId}:`, error);
    throw error;
  }
}

/**
 * تحديث حالة الطلب
 */
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<Order | null> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];

    // البحث عن الطلب
    const orderIndex = orders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      return null;
    }

    // تحديث حالة الطلب
    orders[orderIndex] = {
      ...orders[orderIndex],
      status,
      updatedAt: new Date().toISOString()
    };

    // حفظ الطلبات
    localStorage.setItem(LOCAL_ORDERS_KEY, JSON.stringify(orders));

    return orders[orderIndex];
  } catch (error) {
    console.error(`Error updating status for order ${orderId}:`, error);
    return null;
  }
}

/**
 * تحديث حالة الدفع
 */
export async function updatePaymentStatus(orderId: string, paymentStatus: Order['paymentStatus']): Promise<Order | null> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];

    // البحث عن الطلب
    const orderIndex = orders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      return null;
    }

    // تحديث حالة الدفع
    orders[orderIndex] = {
      ...orders[orderIndex],
      paymentStatus,
      updatedAt: new Date().toISOString()
    };

    // حفظ الطلبات
    localStorage.setItem(LOCAL_ORDERS_KEY, JSON.stringify(orders));

    return orders[orderIndex];
  } catch (error) {
    console.error(`Error updating payment status for order ${orderId}:`, error);
    return null;
  }
}
