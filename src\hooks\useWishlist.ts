import { useCallback } from 'react';
import { useWishlistStore } from '../stores/wishlistStore';
import { useAuthStore } from '../stores/authStore';
import { WishlistService } from '../services/WishlistService';
import { Product } from '../types';

export interface UseWishlistReturn {
  items: Product[];
  itemCount: number;
  isLoading: boolean;
  error: string | null;
  addItem: (product: Product) => Promise<void>;
  removeItem: (productId: string) => Promise<void>;
  toggleItem: (product: Product) => Promise<void>;
  clearWishlist: () => Promise<void>;
  refreshWishlist: () => Promise<void>;
  isInWishlist: (productId: string) => boolean;
}

/**
 * Hook for wishlist management
 * Provides wishlist state and methods for managing wishlist items
 */
export function useWishlist(): UseWishlistReturn {
  const {
    items,
    itemCount,
    isLoading,
    error,
    addItem: addWishlistItem,
    removeItem: removeWishlistItem,
    clearWishlist: clearWishlistItems,
    setItems,
    setLoading,
    setError
  } = useWishlistStore();

  const { user } = useAuthStore();

  // Add item to wishlist
  const addItem = useCallback(async (product: Product) => {
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        await WishlistService.addToWishlist(user.id, product.id);
      }
      
      // Update local state
      addWishlistItem(product);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add item to wishlist';
      setError(errorMessage);
      console.error('Add to wishlist error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, addWishlistItem, setLoading, setError]);

  // Remove item from wishlist
  const removeItem = useCallback(async (productId: string) => {
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        await WishlistService.removeFromWishlist(user.id, productId);
      }
      
      // Update local state
      removeWishlistItem(productId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove item from wishlist';
      setError(errorMessage);
      console.error('Remove from wishlist error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, removeWishlistItem, setLoading, setError]);

  // Toggle item in wishlist
  const toggleItem = useCallback(async (product: Product) => {
    const isCurrentlyInWishlist = items.some(item => item.id === product.id);
    
    if (isCurrentlyInWishlist) {
      await removeItem(product.id);
    } else {
      await addItem(product);
    }
  }, [items, addItem, removeItem]);

  // Clear wishlist
  const clearWishlist = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        const wishlistItems = await WishlistService.getWishlistItems(user.id);
        for (const item of wishlistItems) {
          await WishlistService.removeFromWishlist(user.id, item.product.id);
        }
      }
      
      // Update local state
      clearWishlistItems();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to clear wishlist';
      setError(errorMessage);
      console.error('Clear wishlist error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, clearWishlistItems, setLoading, setError]);

  // Refresh wishlist from server
  const refreshWishlist = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const wishlistItems = await WishlistService.getWishlistItems(user.id);
      const products = wishlistItems.map(item => item.product);
      setItems(products);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh wishlist';
      setError(errorMessage);
      console.error('Refresh wishlist error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, setItems, setLoading, setError]);

  // Check if item is in wishlist
  const isInWishlist = useCallback((productId: string): boolean => {
    return items.some(item => item.id === productId);
  }, [items]);

  return {
    items,
    itemCount,
    isLoading,
    error,
    addItem,
    removeItem,
    toggleItem,
    clearWishlist,
    refreshWishlist,
    isInWishlist
  };
}
