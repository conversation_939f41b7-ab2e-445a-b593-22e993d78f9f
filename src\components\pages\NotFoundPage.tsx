'use client';

import { memo } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useLanguageStore } from '../../stores/languageStore';
import { Button } from '../ui/Button';
import { HomeIcon, SearchIcon, ArrowLeftIcon } from 'lucide-react';

const NotFoundPage = memo(function NotFoundPage() {
  const { language, t } = useLanguageStore();
  const isRTL = language === 'ar';

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 px-4">
      <motion.div
        className="text-center max-w-2xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 404 Animation */}
        <motion.div
          className="mb-8"
          variants={itemVariants}
        >
          <motion.div
            className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            404
          </motion.div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          className="mb-8 space-y-4"
          variants={itemVariants}
        >
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
            {t('notFound.title', 'Page Not Found')}
          </h1>
          <p className="text-lg text-gray-600 max-w-md mx-auto">
            {t('notFound.description', 'Sorry, we couldn\'t find the page you\'re looking for. It might have been moved, deleted, or you entered the wrong URL.')}
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
          variants={itemVariants}
        >
          <Button
            asChild
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl"
          >
            <Link href="/" className="flex items-center gap-2">
              <HomeIcon className="w-5 h-5" />
              {t('notFound.goHome', 'Go Home')}
            </Link>
          </Button>

          <Button
            asChild
            variant="outline"
            size="lg"
            className="border-2 border-gray-300 hover:border-blue-500 text-gray-700 hover:text-blue-600 px-6 py-3 rounded-xl"
          >
            <Link href="/shop" className="flex items-center gap-2">
              <SearchIcon className="w-5 h-5" />
              {t('notFound.browseProducts', 'Browse Products')}
            </Link>
          </Button>
        </motion.div>

        {/* Back Button */}
        <motion.div
          variants={itemVariants}
        >
          <Button
            variant="ghost"
            onClick={() => window.history.back()}
            className="text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className={`w-4 h-4 mr-2 ${isRTL ? 'rotate-180' : ''}`} />
            {t('notFound.goBack', 'Go Back')}
          </Button>
        </motion.div>

        {/* Helpful Links */}
        <motion.div
          className="mt-12 pt-8 border-t border-gray-200"
          variants={itemVariants}
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('notFound.helpfulLinks', 'Helpful Links')}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <Link
              href="/shop"
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              {t('nav.shop', 'Shop')}
            </Link>
            <Link
              href="/services"
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              {t('nav.services', 'Services')}
            </Link>
            <Link
              href="/contact"
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              {t('nav.contact', 'Contact')}
            </Link>
            <Link
              href="/faq"
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              {t('nav.faq', 'FAQ')}
            </Link>
          </div>
        </motion.div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none -z-10">
          <motion.div
            className="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20"
            animate={{
              y: [0, -20, 0],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20"
            animate={{
              y: [0, 20, 0],
              rotate: [360, 180, 0],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-20 left-20 w-12 h-12 bg-green-200 rounded-full opacity-20"
            animate={{
              y: [0, -15, 0],
              x: [0, 15, 0],
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>
      </motion.div>
    </div>
  );
});

export default NotFoundPage;
