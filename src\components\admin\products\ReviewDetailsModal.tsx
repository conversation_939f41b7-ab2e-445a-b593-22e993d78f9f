'use client';

import { 
  X, 
  Star, 
  Calendar, 
  User, 
  ThumbsUp, 
  CheckCircle, 
  XCircle, 
  Trash2,
  Image as ImageIcon 
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { Review } from '../../../types/index';
import NextImage from 'next/image';

interface ReviewDetailsModalProps {
  review: Review & { productName: string };
  onClose: () => void;
  onToggleVerified: (reviewId: string, verified: boolean) => void;
  onDelete: (reviewId: string) => void;
}

export function ReviewDetailsModal({ 
  review, 
  onClose, 
  onToggleVerified, 
  onDelete 
}: ReviewDetailsModalProps) {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // عرض نجوم التقييم
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, index) => (
          <Star
            key={index}
            className={cn(
              "h-5 w-5",
              index < rating ? "text-yellow-500 fill-yellow-500" : "text-slate-300"
            )}
          />
        ))}
      </div>
    );
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-3xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'تفاصيل المراجعة' : 'Review Details'}
          </h2>
          <button
            onClick={onClose}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          {/* معلومات المنتج */}
          <div>
            <h3 className="text-lg font-medium mb-2">
              {language === 'ar' ? 'المنتج' : 'Product'}
            </h3>
            <p className="text-xl font-bold">{review.productName}</p>
          </div>
          
          {/* معلومات المستخدم */}
          <div>
            <h3 className="text-lg font-medium mb-2">
              {language === 'ar' ? 'المستخدم' : 'User'}
            </h3>
            <div className="flex items-center gap-3">
              {review.userAvatar ? (
                <NextImage 
                  src={review.userAvatar} 
                  alt={review.userName}
                  width={48} 
                  height={48} 
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center">
                  <User className="h-6 w-6" />
                </div>
              )}
              <div>
                <p className="font-bold text-lg">{review.userName}</p>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  {language === 'ar' ? 'معرف المستخدم: ' : 'User ID: '}{review.userId}
                </p>
              </div>
            </div>
          </div>
          
          {/* التقييم والتاريخ */}
          <div className="flex flex-col md:flex-row md:items-center gap-4 md:gap-8">
            <div>
              <h3 className="text-lg font-medium mb-2">
                {language === 'ar' ? 'التقييم' : 'Rating'}
              </h3>
              <div className="flex items-center gap-2">
                {renderStars(review.rating)}
                <span className="font-bold">{review.rating}/5</span>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">
                {language === 'ar' ? 'التاريخ' : 'Date'}
              </h3>
              <div className="flex items-center gap-2 text-slate-600 dark:text-slate-300">
                <Calendar className="h-5 w-5" />
                <span>
                  {new Date(review.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">
                {language === 'ar' ? 'مفيدة' : 'Helpful'}
              </h3>
              <div className="flex items-center gap-2 text-slate-600 dark:text-slate-300">
                <ThumbsUp className="h-5 w-5" />
                <span>{review.helpful}</span>
              </div>
            </div>
          </div>
          
          {/* محتوى المراجعة */}
          <div>
            <h3 className="text-lg font-medium mb-2">
              {language === 'ar' ? 'المراجعة' : 'Review'}
            </h3>
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <h4 className="font-bold text-lg mb-2">{review.title}</h4>
              <p className="whitespace-pre-line">{review.comment}</p>
            </div>
          </div>
          
          {/* صور المراجعة */}
          {review.images && review.images.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-2">
                {language === 'ar' ? 'الصور' : 'Images'}
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {review.images.map((image, index) => (
                  <div key={index} className="aspect-square rounded-lg overflow-hidden relative">
                    <NextImage 
                      src={image} 
                      alt={`Review image ${index + 1}`}
                      fill
                      className="object-cover" 
                      sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw" 
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* الإجراءات */}
          <div className="flex flex-wrap justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              variant={review.verified ? "outline" : "outline"}
              onClick={() => onToggleVerified(review.id, !review.verified)}
              className="flex items-center gap-2"
            >
              {review.verified ? (
                <>
                  <XCircle className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إلغاء التوثيق' : 'Mark as Unverified'}</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-5 w-5" />
                  <span>{language === 'ar' ? 'توثيق المراجعة' : 'Mark as Verified'}</span>
                </>
              )}
            </Button>
            
            <Button
              variant="accent"
              onClick={() => {
                onDelete(review.id);
                onClose();
              }}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-5 w-5" />
              <span>{language === 'ar' ? 'حذف المراجعة' : 'Delete Review'}</span>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
