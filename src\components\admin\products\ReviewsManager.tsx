'use client';

import { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Eye, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Star,
  StarOff,
  CheckCircle,
  XCircle,
  ThumbsUp,
  Calendar,
  User
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { products } from '../../../data/products';
import { Product, Review } from '../../../types/index';
import { ReviewDetailsModal } from './ReviewDetailsModal';
import Image from 'next/image';

// استخراج جميع المراجعات من المنتجات
const getAllReviews = (): (Review & { productName: string })[] => {
  const allReviews: (Review & { productName: string })[] = [];
  
  products.forEach(product => {
    if (product.reviews && product.reviews.length > 0) {
      product.reviews.forEach(review => {
        allReviews.push({
          ...review,
          productName: product.name
        });
      });
    }
  });
  
  return allReviews;
};

// مكون إدارة المراجعات
export function ReviewsManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [selectedVerified, setSelectedVerified] = useState<boolean | null>(null);
  
  // حالة المراجعات
  const [filteredReviews, setFilteredReviews] = useState<(Review & { productName: string })[]>([]);
  const [selectedReview, setSelectedReview] = useState<(Review & { productName: string }) | null>(null);
  const [showReviewDetails, setShowReviewDetails] = useState(false);
  
  // تصفية المراجعات
  useEffect(() => {
    let result = getAllReviews();
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(review => 
        review.userName.toLowerCase().includes(query) || 
        review.title.toLowerCase().includes(query) ||
        review.comment.toLowerCase().includes(query) ||
        review.productName.toLowerCase().includes(query)
      );
    }
    
    // تطبيق تصفية التقييم
    if (selectedRating !== null) {
      result = result.filter(review => review.rating === selectedRating);
    }
    
    // تطبيق تصفية التحقق
    if (selectedVerified !== null) {
      result = result.filter(review => review.verified === selectedVerified);
    }
    
    // ترتيب المراجعات حسب التاريخ (الأحدث أولاً)
    result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    setFilteredReviews(result);
  }, [searchQuery, selectedRating, selectedVerified]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredReviews.length / itemsPerPage);
  
  // الحصول على المراجعات للصفحة الحالية
  const currentReviews = filteredReviews.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // عرض تفاصيل المراجعة
  const handleViewReview = (review: Review & { productName: string }) => {
    setSelectedReview(review);
    setShowReviewDetails(true);
  };
  
  // حذف مراجعة
  const handleDeleteReview = (reviewId: string) => {
    // هنا سيتم تنفيذ منطق حذف المراجعة
    console.log('Delete review:', reviewId);
    
    // في الإنتاج، سيتم استدعاء API لحذف المراجعة
    // وتحديث قائمة المراجعات
  };
  
  // تغيير حالة التحقق من المراجعة
  const handleToggleVerified = (reviewId: string, verified: boolean) => {
    // هنا سيتم تنفيذ منطق تغيير حالة التحقق
    console.log('Toggle verified status:', reviewId, verified);
    
    // في الإنتاج، سيتم استدعاء API لتحديث حالة التحقق
    // وتحديث قائمة المراجعات
  };
  
  // عرض نجوم التقييم
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, index) => (
          <Star
            key={index}
            className={cn(
              "h-4 w-4",
              index < rating ? "text-yellow-500 fill-yellow-500" : "text-slate-300"
            )}
          />
        ))}
      </div>
    );
  };
  
  return (
    <>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة المراجعات' : 'Reviews Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة ومراجعة تقييمات المنتجات'
              : 'Manage and moderate product reviews'}
          </p>
        </div>
        
        {/* أدوات البحث والتصفية */}
        <Card className={cn(
          "p-4",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
              <Input
                type="text"
                placeholder={language === 'ar' ? 'البحث في المراجعات...' : 'Search reviews...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="w-full md:w-48">
              <select
                value={selectedRating !== null ? selectedRating.toString() : ''}
                onChange={(e) => setSelectedRating(e.target.value ? parseInt(e.target.value, 10) : null)}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              >
                <option value="">
                  {language === 'ar' ? 'جميع التقييمات' : 'All Ratings'}
                </option>
                {[5, 4, 3, 2, 1].map((rating) => (
                  <option key={rating} value={rating}>
                    {rating} {language === 'ar' ? 'نجوم' : 'Stars'}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="w-full md:w-48">
              <select
                value={selectedVerified !== null ? selectedVerified.toString() : ''}
                onChange={(e) => {
                  if (e.target.value === '') {
                    setSelectedVerified(null);
                  } else {
                    setSelectedVerified(e.target.value === 'true');
                  }
                }}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              >
                <option value="">
                  {language === 'ar' ? 'جميع المراجعات' : 'All Reviews'}
                </option>
                <option value="true">
                  {language === 'ar' ? 'مراجعات موثقة' : 'Verified Reviews'}
                </option>
                <option value="false">
                  {language === 'ar' ? 'مراجعات غير موثقة' : 'Unverified Reviews'}
                </option>
              </select>
            </div>
          </div>
        </Card>
        
        {/* جدول المراجعات */}
        <Card className={cn(
          "overflow-hidden",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={cn(
                "text-xs uppercase",
                isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
              )}>
                <tr>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'المنتج' : 'Product'}
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'المستخدم' : 'User'}
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'المراجعة' : 'Review'}
                  </th>
                  <th className="px-6 py-3 text-center">
                    {language === 'ar' ? 'التقييم' : 'Rating'}
                  </th>
                  <th className="px-6 py-3 text-center">
                    {language === 'ar' ? 'التاريخ' : 'Date'}
                  </th>
                  <th className="px-6 py-3 text-center">
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className="px-6 py-3 text-right">
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y dark:divide-slate-700">
                {currentReviews.map((review) => (
                  <tr key={review.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <p className="font-medium">{review.productName}</p>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {review.userAvatar ? (
                          <Image
                            src={review.userAvatar}
                            alt={review.userName}
                            width={32}
                            height={32}
                            className="rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center">
                            <User className="h-4 w-4" />
                          </div>
                        )}
                        <p>{review.userName}</p>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <p className="font-medium">{review.title}</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400 line-clamp-1">
                        {review.comment}
                      </p>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      {renderStars(review.rating)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center gap-1 text-sm text-slate-500 dark:text-slate-400">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {new Date(review.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center">
                        <button
                          onClick={() => handleToggleVerified(review.id, !review.verified)}
                          className={cn(
                            "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
                            review.verified
                              ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                              : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                          )}
                        >
                          {review.verified ? (
                            <>
                              <CheckCircle className="h-3 w-3" />
                              <span>{language === 'ar' ? 'موثق' : 'Verified'}</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-3 w-3" />
                              <span>{language === 'ar' ? 'غير موثق' : 'Unverified'}</span>
                            </>
                          )}
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => handleViewReview(review)}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Eye className="h-5 w-5 text-blue-500" />
                        </button>
                        <button
                          onClick={() => handleDeleteReview(review.id)}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Trash2 className="h-5 w-5 text-red-500" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* ترقيم الصفحات */}
          {totalPages > 1 && (
            <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {language === 'ar'
                    ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredReviews.length)} من ${filteredReviews.length} مراجعة`
                    : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredReviews.length)} of ${filteredReviews.length} reviews`
                  }
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className={cn(
                    "p-2 rounded-md",
                    currentPage === 1 
                      ? "opacity-50 cursor-not-allowed" 
                      : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                  )}
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                
                <span className="text-sm">
                  {language === 'ar'
                    ? `${currentPage} من ${totalPages}`
                    : `${currentPage} of ${totalPages}`
                  }
                </span>
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className={cn(
                    "p-2 rounded-md",
                    currentPage === totalPages 
                      ? "opacity-50 cursor-not-allowed" 
                      : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                  )}
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}
        </Card>
      </div>
      
      {/* نافذة تفاصيل المراجعة */}
      {showReviewDetails && selectedReview && (
        <ReviewDetailsModal
          review={selectedReview}
          onClose={() => setShowReviewDetails(false)}
          onToggleVerified={handleToggleVerified}
          onDelete={handleDeleteReview}
        />
      )}
    </>
  );
}
