/**
 * Utility functions for formatting data
 */

// Currency formatting
export const formatCurrency = (
  amount: number,
  currency: string = 'SAR',
  locale: string = 'ar-SA'
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  } catch (error) {
    // Fallback formatting
    return `${amount.toFixed(2)} ${currency}`;
  }
};

// Number formatting
export const formatNumber = (
  number: number,
  locale: string = 'ar-SA',
  options?: Intl.NumberFormatOptions
): string => {
  try {
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    return number.toString();
  }
};

// Percentage formatting
export const formatPercentage = (
  value: number,
  locale: string = 'ar-SA',
  decimals: number = 1
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value / 100);
  } catch (error) {
    return `${value.toFixed(decimals)}%`;
  }
};

// Date formatting
export const formatDate = (
  date: Date | string,
  locale: string = 'ar-SA',
  options?: Intl.DateTimeFormatOptions
): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, options).format(dateObj);
  } catch (error) {
    return date.toString();
  }
};

// Relative time formatting
export const formatRelativeTime = (
  date: Date | string,
  locale: string = 'ar-SA'
): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
  } catch (error) {
    return formatDate(date, locale);
  }
};

// File size formatting
export const formatFileSize = (bytes: number, locale: string = 'en-US'): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${formatNumber(value, locale)} ${sizes[i]}`;
};

// Phone number formatting
export const formatPhoneNumber = (phoneNumber: string, countryCode: string = 'SA'): string => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  if (countryCode === 'SA') {
    // Saudi Arabia phone number formatting
    if (cleaned.length === 10 && cleaned.startsWith('5')) {
      return `+966 ${cleaned.slice(1, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    } else if (cleaned.length === 9 && cleaned.startsWith('5')) {
      return `+966 ${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5)}`;
    }
  }

  // Default formatting
  return phoneNumber;
};

// Text truncation
export const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - suffix.length) + suffix;
};

// Slug formatting
export const formatSlug = (text: string): string => {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Name formatting
export const formatName = (firstName: string, lastName: string, locale: string = 'ar'): string => {
  if (locale === 'ar') {
    return `${firstName} ${lastName}`.trim();
  }
  return `${firstName} ${lastName}`.trim();
};

// Address formatting
export const formatAddress = (address: {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}, locale: string = 'ar'): string => {
  const parts = [];

  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.postalCode) parts.push(address.postalCode);
  if (address.country) parts.push(address.country);

  return parts.join(', ');
};

// Order status formatting
export const formatOrderStatus = (status: string, locale: string = 'ar'): string => {
  const statusMap: Record<string, { ar: string; en: string }> = {
    pending: { ar: 'قيد الانتظار', en: 'Pending' },
    processing: { ar: 'قيد المعالجة', en: 'Processing' },
    shipped: { ar: 'تم الشحن', en: 'Shipped' },
    delivered: { ar: 'تم التسليم', en: 'Delivered' },
    cancelled: { ar: 'ملغي', en: 'Cancelled' },
    refunded: { ar: 'مسترد', en: 'Refunded' }
  };

  const statusInfo = statusMap[status.toLowerCase()];
  if (!statusInfo) return status;

  return locale === 'ar' ? statusInfo.ar : statusInfo.en;
};

// Payment status formatting
export const formatPaymentStatus = (status: string, locale: string = 'ar'): string => {
  const statusMap: Record<string, { ar: string; en: string }> = {
    pending: { ar: 'قيد الانتظار', en: 'Pending' },
    paid: { ar: 'مدفوع', en: 'Paid' },
    failed: { ar: 'فشل', en: 'Failed' },
    refunded: { ar: 'مسترد', en: 'Refunded' },
    cancelled: { ar: 'ملغي', en: 'Cancelled' }
  };

  const statusInfo = statusMap[status.toLowerCase()];
  if (!statusInfo) return status;

  return locale === 'ar' ? statusInfo.ar : statusInfo.en;
};

// Rating formatting
export const formatRating = (rating: number, maxRating: number = 5): string => {
  const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(maxRating - Math.floor(rating));
  return `${stars} (${rating.toFixed(1)})`;
};

// Discount formatting
export const formatDiscount = (originalPrice: number, discountedPrice: number): {
  amount: number;
  percentage: number;
  formatted: string;
} => {
  const amount = originalPrice - discountedPrice;
  const percentage = (amount / originalPrice) * 100;
  
  return {
    amount,
    percentage,
    formatted: `${percentage.toFixed(0)}% خصم`
  };
};

// Time duration formatting
export const formatDuration = (seconds: number, locale: string = 'ar'): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const parts = [];
  
  if (hours > 0) {
    parts.push(`${hours}${locale === 'ar' ? 'س' : 'h'}`);
  }
  if (minutes > 0) {
    parts.push(`${minutes}${locale === 'ar' ? 'د' : 'm'}`);
  }
  if (remainingSeconds > 0 || parts.length === 0) {
    parts.push(`${remainingSeconds}${locale === 'ar' ? 'ث' : 's'}`);
  }

  return parts.join(' ');
};
