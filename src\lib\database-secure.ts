/**
 * Secure Database Layer with Parameterized Queries
 * Prevents SQL injection and implements proper error handling
 */

import Database from 'better-sqlite3';
import { hashPassword, verifyPassword } from './auth';
import { User, Product, Order, Review } from '../types';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

const IS_SERVER = typeof window === 'undefined';
let db: Database.Database | null = null;

// Database configuration
const DB_CONFIG = {
  readonly: false,
  fileMustExist: false,
  timeout: 5000,
  verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
};

/**
 * Initialize database connection
 */
export function initializeDatabase(): Database.Database {
  if (!IS_SERVER) {
    throw new Error('Database can only be initialized on the server');
  }

  if (db) {
    return db;
  }

  const dbPath = process.env.SQLITE_DB_PATH || path.join(process.cwd(), 'database.sqlite');
  
  try {
    db = new Database(dbPath, DB_CONFIG);
    
    // Enable foreign keys and WAL mode for better performance
    db.pragma('foreign_keys = ON');
    db.pragma('journal_mode = WAL');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 1000');
    db.pragma('temp_store = memory');
    
    // Create tables if they don't exist
    createTables(db);
    
    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Get database instance
 */
export function getDatabase(): Database.Database {
  if (!db) {
    return initializeDatabase();
  }
  return db;
}

/**
 * Create database tables with proper constraints
 */
function createTables(database: Database.Database): void {
  const tables = [
    // Users table with security fields
    `CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'user',
      phone TEXT,
      avatar_url TEXT,
      email_verified INTEGER DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      failed_login_attempts INTEGER DEFAULT 0,
      last_failed_login TEXT,
      lockout_until TEXT,
      last_login TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      CONSTRAINT chk_role CHECK (role IN ('user', 'admin', 'manager')),
      CONSTRAINT chk_email_format CHECK (email LIKE '%@%.%')
    )`,

    // Products table with inventory tracking
    `CREATE TABLE IF NOT EXISTS products (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      name_ar TEXT,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      description_ar TEXT,
      price REAL NOT NULL CHECK (price > 0),
      sale_price REAL CHECK (sale_price > 0),
      cost_price REAL CHECK (cost_price >= 0),
      sku TEXT UNIQUE NOT NULL,
      category TEXT NOT NULL,
      tags TEXT, -- JSON array
      images TEXT, -- JSON array
      featured INTEGER DEFAULT 0,
      in_stock INTEGER DEFAULT 1,
      stock_quantity INTEGER DEFAULT 0 CHECK (stock_quantity >= 0),
      low_stock_threshold INTEGER DEFAULT 10,
      rating REAL DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
      review_count INTEGER DEFAULT 0,
      specifications TEXT, -- JSON object
      meta_title TEXT,
      meta_description TEXT,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )`,

    // Orders table with comprehensive tracking
    `CREATE TABLE IF NOT EXISTS orders (
      id TEXT PRIMARY KEY,
      order_number TEXT UNIQUE NOT NULL,
      user_id TEXT,
      status TEXT NOT NULL DEFAULT 'pending',
      subtotal REAL NOT NULL CHECK (subtotal >= 0),
      tax_amount REAL DEFAULT 0 CHECK (tax_amount >= 0),
      shipping_fee REAL DEFAULT 0 CHECK (shipping_fee >= 0),
      discount_amount REAL DEFAULT 0 CHECK (discount_amount >= 0),
      total REAL NOT NULL CHECK (total >= 0),
      currency TEXT DEFAULT 'SAR',
      payment_method TEXT,
      payment_status TEXT DEFAULT 'pending',
      payment_reference TEXT,
      shipping_address TEXT, -- JSON object
      billing_address TEXT, -- JSON object
      notes TEXT,
      tracking_number TEXT,
      shipped_at TEXT,
      delivered_at TEXT,
      cancelled_at TEXT,
      cancellation_reason TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
      CONSTRAINT chk_status CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
      CONSTRAINT chk_payment_status CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded'))
    )`,

    // Order items table
    `CREATE TABLE IF NOT EXISTS order_items (
      id TEXT PRIMARY KEY,
      order_id TEXT NOT NULL,
      product_id TEXT NOT NULL,
      product_name TEXT NOT NULL,
      product_sku TEXT NOT NULL,
      quantity INTEGER NOT NULL CHECK (quantity > 0),
      unit_price REAL NOT NULL CHECK (unit_price >= 0),
      total_price REAL NOT NULL CHECK (total_price >= 0),
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
      FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT
    )`,

    // Reviews table with moderation
    `CREATE TABLE IF NOT EXISTS reviews (
      id TEXT PRIMARY KEY,
      product_id TEXT NOT NULL,
      user_id TEXT NOT NULL,
      order_id TEXT,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      title TEXT NOT NULL,
      comment TEXT NOT NULL,
      images TEXT, -- JSON array
      is_verified INTEGER DEFAULT 0,
      is_approved INTEGER DEFAULT 0,
      helpful_count INTEGER DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE SET NULL
    )`,

    // Sessions table for secure session management
    `CREATE TABLE IF NOT EXISTS user_sessions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      token_hash TEXT NOT NULL,
      refresh_token_hash TEXT NOT NULL,
      ip_address TEXT,
      user_agent TEXT,
      expires_at TEXT NOT NULL,
      refresh_expires_at TEXT NOT NULL,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Audit log table for security tracking
    `CREATE TABLE IF NOT EXISTS audit_logs (
      id TEXT PRIMARY KEY,
      user_id TEXT,
      action TEXT NOT NULL,
      resource_type TEXT NOT NULL,
      resource_id TEXT,
      old_values TEXT, -- JSON object
      new_values TEXT, -- JSON object
      ip_address TEXT,
      user_agent TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
    )`
  ];

  // Create indexes for better performance
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)',
    'CREATE INDEX IF NOT EXISTS idx_users_role ON users (role)',
    'CREATE INDEX IF NOT EXISTS idx_products_category ON products (category)',
    'CREATE INDEX IF NOT EXISTS idx_products_featured ON products (featured)',
    'CREATE INDEX IF NOT EXISTS idx_products_slug ON products (slug)',
    'CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status)',
    'CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders (created_at)',
    'CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items (order_id)',
    'CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items (product_id)',
    'CREATE INDEX IF NOT EXISTS idx_reviews_product_id ON reviews (product_id)',
    'CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions (token_hash)',
    'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs (created_at)'
  ];

  try {
    // Create tables
    for (const table of tables) {
      database.exec(table);
    }

    // Create indexes
    for (const index of indexes) {
      database.exec(index);
    }

    console.log('Database tables and indexes created successfully');
  } catch (error) {
    console.error('Error creating database tables:', error);
    throw error;
  }
}

/**
 * User management functions with security
 */
export class UserRepository {
  private db: Database.Database;

  constructor() {
    this.db = getDatabase();
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>, password: string): Promise<User | null> {
    try {
      const id = uuidv4();
      const passwordHash = await hashPassword(password);
      const now = new Date().toISOString();

      const stmt = this.db.prepare(`
        INSERT INTO users (
          id, email, password_hash, first_name, last_name, role, phone, 
          email_verified, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = stmt.run(
        id,
        userData.email,
        passwordHash,
        userData.firstName || '',
        userData.lastName || '',
        userData.role || 'user',
        userData.phone || null,
        0, // email_verified
        1, // is_active
        now,
        now
      );

      if (result.changes > 0) {
        return this.getUserById(id);
      }

      return null;
    } catch (error) {
      console.error('Error creating user:', error);
      return null;
    }
  }

  getUserById(id: string): User | null {
    try {
      const stmt = this.db.prepare(`
        SELECT id, email, first_name, last_name, role, phone, avatar_url,
               email_verified, is_active, last_login, created_at, updated_at
        FROM users WHERE id = ? AND is_active = 1
      `);
      
      const row = stmt.get(id) as any;
      return row ? this.mapRowToUser(row) : null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  getUserByEmail(email: string): User | null {
    try {
      const stmt = this.db.prepare(`
        SELECT id, email, first_name, last_name, role, phone, avatar_url,
               email_verified, is_active, last_login, created_at, updated_at,
               failed_login_attempts, lockout_until
        FROM users WHERE email = ? AND is_active = 1
      `);
      
      const row = stmt.get(email.toLowerCase()) as any;
      return row ? this.mapRowToUser(row) : null;
    } catch (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
  }

  async authenticateUser(email: string, password: string): Promise<User | null> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, email, password_hash, first_name, last_name, role, phone, 
               avatar_url, email_verified, is_active, last_login, created_at, 
               updated_at, failed_login_attempts, lockout_until
        FROM users WHERE email = ? AND is_active = 1
      `);
      
      const row = stmt.get(email.toLowerCase()) as any;
      
      if (!row) {
        return null;
      }

      // Check if account is locked
      if (row.lockout_until && new Date(row.lockout_until) > new Date()) {
        return null;
      }

      const isValidPassword = await verifyPassword(password, row.password_hash);
      
      if (!isValidPassword) {
        // Update failed login attempts
        this.updateFailedLoginAttempts(row.id);
        return null;
      }

      // Reset failed attempts and update last login
      this.resetFailedLoginAttempts(row.id);
      
      return this.mapRowToUser(row);
    } catch (error) {
      console.error('Error authenticating user:', error);
      return null;
    }
  }

  private updateFailedLoginAttempts(userId: string): void {
    const stmt = this.db.prepare(`
      UPDATE users 
      SET failed_login_attempts = failed_login_attempts + 1,
          last_failed_login = CURRENT_TIMESTAMP,
          lockout_until = CASE 
            WHEN failed_login_attempts >= 4 THEN datetime('now', '+15 minutes')
            ELSE lockout_until
          END
      WHERE id = ?
    `);
    stmt.run(userId);
  }

  private resetFailedLoginAttempts(userId: string): void {
    const stmt = this.db.prepare(`
      UPDATE users 
      SET failed_login_attempts = 0,
          last_failed_login = NULL,
          lockout_until = NULL,
          last_login = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    stmt.run(userId);
  }

  private mapRowToUser(row: any): User {
    return {
      id: row.id,
      email: row.email,
      firstName: row.first_name,
      lastName: row.last_name,
      role: row.role,
      phone: row.phone,
      avatarUrl: row.avatar_url,
      emailVerified: Boolean(row.email_verified),
      isActive: Boolean(row.is_active),
      lastLogin: row.last_login,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}

// Export singleton instance
export const userRepository = new UserRepository();
