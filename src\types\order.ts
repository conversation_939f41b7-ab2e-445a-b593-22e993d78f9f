export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  customer: OrderCustomer;
  type: OrderType;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  fulfillmentStatus: FulfillmentStatus;
  items: OrderItem[];
  pricing: OrderPricing;
  shipping: OrderShipping;
  billing: OrderBilling;
  payment: OrderPayment;
  notes?: string;
  internalNotes?: string;
  tags: string[];
  source: OrderSource;
  channel: OrderChannel;
  currency: string;
  exchangeRate?: number;
  timeline: OrderTimeline;
  tracking?: OrderTracking;
  refunds?: OrderRefund[];
  returns?: OrderReturn[];
  communications?: OrderCommunication[];
  attachments?: OrderAttachment[];
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  cancelledAt?: string;
}

export type OrderType = 'product' | 'service' | 'wholesale' | 'subscription' | 'digital' | 'mixed';
export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'refunded'
  | 'on_hold';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'partially_paid'
  | 'failed'
  | 'cancelled'
  | 'refunded'
  | 'partially_refunded';

export type FulfillmentStatus = 
  | 'pending'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'returned'
  | 'partially_fulfilled';

export interface OrderCustomer {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  isGuest: boolean;
  loyaltyPoints?: number;
  customerGroup?: string;
}

export interface OrderItem {
  id: string;
  type: 'product' | 'service';
  productId?: string;
  serviceId?: string;
  variantId?: string;
  name: string;
  name_ar: string;
  sku?: string;
  image?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  originalPrice?: number;
  discountAmount?: number;
  taxAmount?: number;
  weight?: number;
  dimensions?: ItemDimensions;
  customizations?: ItemCustomization[];
  requirements?: ItemRequirement[];
  status: OrderItemStatus;
  fulfillmentStatus: ItemFulfillmentStatus;
  tracking?: ItemTracking;
  notes?: string;
}

export type OrderItemStatus = 'pending' | 'confirmed' | 'processing' | 'completed' | 'cancelled' | 'refunded';
export type ItemFulfillmentStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'returned';

export interface ItemDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'in';
}

export interface ItemCustomization {
  name: string;
  value: string;
  additionalCost?: number;
}

export interface ItemRequirement {
  name: string;
  value: string;
  type: 'text' | 'file' | 'date' | 'select';
}

export interface ItemTracking {
  trackingNumber?: string;
  carrier?: string;
  trackingUrl?: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  events: TrackingEvent[];
}

export interface TrackingEvent {
  status: string;
  description: string;
  location?: string;
  timestamp: string;
}

export interface OrderPricing {
  subtotal: number;
  discountAmount: number;
  discounts: AppliedDiscount[];
  taxAmount: number;
  taxes: AppliedTax[];
  shippingAmount: number;
  handlingAmount?: number;
  totalAmount: number;
  paidAmount: number;
  refundedAmount: number;
  outstandingAmount: number;
}

export interface AppliedDiscount {
  id: string;
  code?: string;
  name: string;
  type: 'percentage' | 'fixed' | 'free_shipping';
  value: number;
  amount: number;
  reason?: string;
}

export interface AppliedTax {
  name: string;
  rate: number;
  amount: number;
  isInclusive: boolean;
  jurisdiction?: string;
}

export interface OrderShipping {
  method: ShippingMethod;
  address: ShippingAddress;
  cost: number;
  estimatedDelivery?: string;
  actualDelivery?: string;
  tracking?: OrderTracking;
  instructions?: string;
  requiresSignature: boolean;
  isInsured: boolean;
  insuranceAmount?: number;
}

export interface ShippingMethod {
  id: string;
  name: string;
  name_ar: string;
  carrier: string;
  service: string;
  estimatedDays: number;
  trackingSupported: boolean;
}

export interface ShippingAddress {
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface OrderBilling {
  address: BillingAddress;
  method: BillingMethod;
  taxId?: string;
  companyName?: string;
}

export interface BillingAddress {
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  email: string;
}

export interface BillingMethod {
  type: 'credit_card' | 'debit_card' | 'bank_transfer' | 'cash_on_delivery' | 'digital_wallet' | 'crypto';
  provider?: string;
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
}

export interface OrderPayment {
  transactions: PaymentTransaction[];
  totalPaid: number;
  totalRefunded: number;
  outstandingAmount: number;
  dueDate?: string;
  terms?: string;
}

export interface PaymentTransaction {
  id: string;
  type: 'payment' | 'refund' | 'chargeback' | 'adjustment';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount: number;
  currency: string;
  method: BillingMethod;
  gateway: string;
  gatewayTransactionId?: string;
  reference?: string;
  failureReason?: string;
  processedAt?: string;
  createdAt: string;
}

export type OrderSource = 'web' | 'mobile' | 'api' | 'admin' | 'pos' | 'phone' | 'email' | 'marketplace';
export type OrderChannel = 'online' | 'retail' | 'wholesale' | 'b2b' | 'marketplace' | 'social';

export interface OrderTimeline {
  events: OrderTimelineEvent[];
  estimatedCompletion?: string;
  actualCompletion?: string;
}

export interface OrderTimelineEvent {
  id: string;
  type: OrderEventType;
  status: string;
  description: string;
  userId?: string;
  userName?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export type OrderEventType = 
  | 'created'
  | 'confirmed'
  | 'payment_received'
  | 'payment_failed'
  | 'processing_started'
  | 'shipped'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'refunded'
  | 'returned'
  | 'note_added'
  | 'status_changed';

export interface OrderTracking {
  trackingNumber: string;
  carrier: string;
  service: string;
  trackingUrl?: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  events: TrackingEvent[];
  lastUpdated: string;
}

export interface OrderRefund {
  id: string;
  amount: number;
  reason: RefundReason;
  description?: string;
  items: RefundItem[];
  status: RefundStatus;
  method: RefundMethod;
  transactionId?: string;
  processedBy: string;
  processedAt?: string;
  createdAt: string;
}

export type RefundReason = 
  | 'customer_request'
  | 'damaged_item'
  | 'wrong_item'
  | 'quality_issue'
  | 'late_delivery'
  | 'cancelled_order'
  | 'duplicate_order'
  | 'other';

export type RefundStatus = 'pending' | 'approved' | 'processed' | 'failed' | 'cancelled';
export type RefundMethod = 'original_payment' | 'store_credit' | 'bank_transfer' | 'cash';

export interface RefundItem {
  orderItemId: string;
  quantity: number;
  amount: number;
  reason?: string;
}

export interface OrderReturn {
  id: string;
  reason: ReturnReason;
  description?: string;
  items: ReturnItem[];
  status: ReturnStatus;
  returnMethod: ReturnMethod;
  trackingNumber?: string;
  refundAmount?: number;
  restockingFee?: number;
  requestedBy: string;
  approvedBy?: string;
  processedBy?: string;
  requestedAt: string;
  approvedAt?: string;
  processedAt?: string;
}

export type ReturnReason = 
  | 'defective'
  | 'wrong_item'
  | 'not_as_described'
  | 'changed_mind'
  | 'damaged_shipping'
  | 'quality_issue'
  | 'size_issue'
  | 'other';

export type ReturnStatus = 'requested' | 'approved' | 'rejected' | 'received' | 'processed' | 'completed';
export type ReturnMethod = 'pickup' | 'drop_off' | 'mail';

export interface ReturnItem {
  orderItemId: string;
  quantity: number;
  condition: 'new' | 'used' | 'damaged';
  reason?: string;
  images?: string[];
}

export interface OrderCommunication {
  id: string;
  type: 'email' | 'sms' | 'call' | 'note' | 'chat';
  direction: 'inbound' | 'outbound';
  subject?: string;
  message: string;
  fromUserId?: string;
  toUserId?: string;
  fromEmail?: string;
  toEmail?: string;
  attachments?: string[];
  isInternal: boolean;
  readAt?: string;
  createdAt: string;
}

export interface OrderAttachment {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadedBy: string;
  uploadedAt: string;
}

// Request/Response types
export interface CreateOrderRequest {
  customerId?: string;
  guestCustomer?: Omit<OrderCustomer, 'id' | 'isGuest'>;
  items: CreateOrderItem[];
  shippingAddress: Omit<ShippingAddress, 'coordinates'>;
  billingAddress: Omit<BillingAddress, 'coordinates'>;
  shippingMethodId: string;
  paymentMethodId: string;
  discountCodes?: string[];
  notes?: string;
  source: OrderSource;
  channel: OrderChannel;
}

export interface CreateOrderItem {
  type: 'product' | 'service';
  id: string;
  variantId?: string;
  quantity: number;
  customizations?: ItemCustomization[];
  requirements?: ItemRequirement[];
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  fulfillmentStatus?: FulfillmentStatus;
  notes?: string;
  internalNotes?: string;
  tags?: string[];
  tracking?: Partial<OrderTracking>;
}

export interface OrderSearchFilters {
  status?: OrderStatus[];
  paymentStatus?: PaymentStatus[];
  fulfillmentStatus?: FulfillmentStatus[];
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  source?: OrderSource[];
  channel?: OrderChannel[];
  search?: string;
  sortBy?: OrderSortOption;
  sortOrder?: 'asc' | 'desc';
}

export type OrderSortOption = 
  | 'order_number'
  | 'date'
  | 'total'
  | 'status'
  | 'customer'
  | 'payment_status';

export interface OrderListResponse {
  orders: Order[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  summary: {
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    statusBreakdown: Record<OrderStatus, number>;
  };
}

// Utility types
export type OrderSummary = Pick<Order,
  | 'id'
  | 'orderNumber'
  | 'status'
  | 'paymentStatus'
  | 'fulfillmentStatus'
  | 'pricing'
  | 'customer'
  | 'createdAt'
>;

export type OrderDetails = Order & {
  canCancel: boolean;
  canRefund: boolean;
  canReturn: boolean;
  canReorder: boolean;
};
