import { useState, useEffect, useCallback } from 'react';

export interface UseMediaQueryReturn {
  matches: boolean;
  isLoading: boolean;
}

/**
 * Hook for responsive design using CSS media queries
 * Provides a way to conditionally render components based on screen size
 */
export function useMediaQuery(query: string): UseMediaQueryReturn {
  const [matches, setMatches] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const updateMatches = useCallback(() => {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia(query);
      setMatches(mediaQuery.matches);
      setIsLoading(false);
    }
  }, [query]);

  useEffect(() => {
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }

    const mediaQuery = window.matchMedia(query);
    
    // Set initial value
    setMatches(mediaQuery.matches);
    setIsLoading(false);

    // Listen for changes
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Add listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }

    // Cleanup
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handleChange);
      }
    };
  }, [query]);

  return { matches, isLoading };
}

// Predefined breakpoint hooks for common use cases
export const useIsMobile = () => useMediaQuery('(max-width: 768px)');
export const useIsTablet = () => useMediaQuery('(min-width: 769px) and (max-width: 1024px)');
export const useIsDesktop = () => useMediaQuery('(min-width: 1025px)');
export const useIsLargeScreen = () => useMediaQuery('(min-width: 1440px)');

// Orientation hooks
export const useIsPortrait = () => useMediaQuery('(orientation: portrait)');
export const useIsLandscape = () => useMediaQuery('(orientation: landscape)');

// Accessibility hooks
export const usePrefersReducedMotion = () => useMediaQuery('(prefers-reduced-motion: reduce)');
export const usePrefersDarkMode = () => useMediaQuery('(prefers-color-scheme: dark)');
export const usePrefersHighContrast = () => useMediaQuery('(prefers-contrast: high)');

// Touch device detection
export const useIsTouchDevice = () => useMediaQuery('(hover: none) and (pointer: coarse)');

// Print media
export const useIsPrint = () => useMediaQuery('print');

/**
 * Hook for multiple media queries
 * Returns an object with the results of multiple queries
 */
export function useMediaQueries(queries: Record<string, string>) {
  const [matches, setMatches] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }

    const mediaQueries: Record<string, MediaQueryList> = {};
    const initialMatches: Record<string, boolean> = {};

    // Create media query objects and set initial values
    Object.entries(queries).forEach(([key, query]) => {
      const mediaQuery = window.matchMedia(query);
      mediaQueries[key] = mediaQuery;
      initialMatches[key] = mediaQuery.matches;
    });

    setMatches(initialMatches);
    setIsLoading(false);

    // Create change handlers
    const handlers: Record<string, (event: MediaQueryListEvent) => void> = {};
    
    Object.entries(mediaQueries).forEach(([key, mediaQuery]) => {
      const handler = (event: MediaQueryListEvent) => {
        setMatches(prev => ({
          ...prev,
          [key]: event.matches
        }));
      };
      
      handlers[key] = handler;
      
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handler);
      } else {
        mediaQuery.addListener(handler);
      }
    });

    // Cleanup
    return () => {
      Object.entries(mediaQueries).forEach(([key, mediaQuery]) => {
        const handler = handlers[key];
        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handler);
        } else {
          mediaQuery.removeListener(handler);
        }
      });
    };
  }, [queries]);

  return { matches, isLoading };
}

/**
 * Hook for responsive breakpoints
 * Returns current breakpoint and boolean flags for each breakpoint
 */
export function useBreakpoint() {
  const breakpoints = {
    sm: '(min-width: 640px)',
    md: '(min-width: 768px)',
    lg: '(min-width: 1024px)',
    xl: '(min-width: 1280px)',
    '2xl': '(min-width: 1536px)'
  };

  const { matches, isLoading } = useMediaQueries(breakpoints);

  // Determine current breakpoint
  let currentBreakpoint = 'xs';
  if (matches['2xl']) currentBreakpoint = '2xl';
  else if (matches.xl) currentBreakpoint = 'xl';
  else if (matches.lg) currentBreakpoint = 'lg';
  else if (matches.md) currentBreakpoint = 'md';
  else if (matches.sm) currentBreakpoint = 'sm';

  return {
    current: currentBreakpoint,
    isXs: !matches.sm,
    isSm: matches.sm && !matches.md,
    isMd: matches.md && !matches.lg,
    isLg: matches.lg && !matches.xl,
    isXl: matches.xl && !matches['2xl'],
    is2Xl: matches['2xl'],
    isSmAndUp: matches.sm,
    isMdAndUp: matches.md,
    isLgAndUp: matches.lg,
    isXlAndUp: matches.xl,
    isLoading
  };
}
