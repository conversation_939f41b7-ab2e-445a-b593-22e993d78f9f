/**
 * خدمة إدارة المنتجات باستخدام SQLite
 */

import { getDbInstance, sqlite } from '../lib/sqlite';
import { Product, Review } from '../types/index';
import { products as initialProducts } from '../data/products';

// Initialize the db instance
const sqliteDB = getDbInstance();

// مفتاح التخزين المحلي
const LOCAL_PRODUCTS_KEY = 'local-products';

/**
 * الحصول على جميع المنتجات
 */
export async function getAllProducts(): Promise<Product[]> {
  try {
    // محاولة الحصول على المنتجات من SQLite
    const products = await sqliteDB.getProducts();

    // إذا لم تكن هناك منتجات، قم بتهيئة المنتجات الافتراضية
    if ((await sqliteDB.getProducts()).length === 0) {
      await initializeDefaultProducts();
      return await sqliteDB.getProducts();
    }

    return products;
  } catch (error) {
    console.error('Error getting products:', error);
    return [];
  }
}

/**
 * الحصول على منتج بواسطة المعرف
 */
export async function getProductById(id: string): Promise<Product | null> {
  try {
    const products = await sqliteDB.getProducts();
    return products.find((p: Product) => p.id.toString() === id.toString()) || null;
  } catch (error) {
    console.error(`Error getting product by ID ${id}:`, error);
    return null;
  }
}

/**
 * الحصول على منتج بواسطة الرابط
 */
export async function getProductBySlug(slug: string): Promise<Product | null> {
  try {
    const products = await sqliteDB.getProducts();
    return products.find((p: Product) => p.slug === slug) || null;
  } catch (error) {
    console.error(`Error getting product by slug ${slug}:`, error);
    return null;
  }
}

/**
 * إنشاء منتج جديد
 */
export async function createProduct(productData: Partial<Product>): Promise<Product> {
  try {
    const products = await sqliteDB.getProducts();

    // إنشاء معرف فريد للمنتج الجديد
    const id = productData.id || `product-${Date.now().toString()}`;

    // دمج بيانات المنتج مع القيم الافتراضية
    const newProduct: Product = {
      id: id,
      name: productData.name || "منتج جديد",
      slug: productData.slug || `new-product-${Date.now().toString()}`,
      description: productData.description || "وصف المنتج الجديد",
      price: productData.price || 0,
      compareAtPrice: productData.compareAtPrice || undefined,
      category: productData.category || "غير مصنف",
      images: productData.images || [],
      tags: productData.tags || [],
      stock: productData.stock || 0,
      featured: productData.featured || false,
      specifications: productData.specifications || {},
      reviews: productData.reviews || [],
      rating: productData.rating || 0,
      reviewCount: productData.reviewCount || 0,
      relatedProducts: productData.relatedProducts || [],
      createdAt: new Date().toISOString(),
    };

    // إضافة المنتج الجديد إلى المنتجات - relies on createProduct to persist
    const createdProduct = await sqliteDB.createProduct(newProduct);
    if (!createdProduct) {
      throw new Error('Failed to create product in DB');
    }
    return createdProduct;
  } catch (error) {
    console.error('Error creating product:', error);
    throw new Error('فشل إنشاء المنتج');
  }
}

/**
 * تحديث منتج
 */
export async function updateProduct(id: string, productData: Partial<Product>): Promise<Product | null> {
  try {
    const products = await sqliteDB.getProducts();
    const index = products.findIndex((p: Product) => p.id.toString() === id.toString());

    if (index === -1) {
      return null;
    }

    const updatedProductData = {
      ...products[index],
      ...productData,
    };

    // تحديث المنتج في المنتجات - relies on updateProduct to persist
    const updatedProduct = await sqliteDB.updateProduct(id.toString(), updatedProductData);
    if (!updatedProduct) {
      return null;
    }
    return updatedProduct;
  } catch (error) {
    console.error(`Error updating product ${id}:`, error);
    return null;
  }
}

/**
 * حذف منتج
 */
export async function deleteProduct(id: string): Promise<boolean> {
  try {
    const products = await sqliteDB.getProducts();
    const productExists = products.some((p: Product) => p.id.toString() === id.toString());

    if (!productExists) {
        return false; // Product not found, cannot delete
    }

    // حذف المنتج من المنتجات - relies on deleteProduct to persist
    const success = await sqliteDB.deleteProduct(id.toString());
    return success;

  } catch (error) {
    console.error(`Error deleting product ${id}:`, error);
    return false;
  }
}

/**
 * البحث عن منتجات
 */
export async function searchProducts(query: string): Promise<Product[]> {
  try {
    const products = await sqliteDB.getProducts();

    if (!query) {
      return products;
    }

    const lowerCaseQuery = query.toLowerCase();
    return products.filter((product: Product) =>
      product.name.toLowerCase().includes(lowerCaseQuery) ||
      product.description.toLowerCase().includes(lowerCaseQuery) ||
      (product.tags && product.tags.some((tag: string) => tag.toLowerCase().includes(lowerCaseQuery)))
    );
  } catch (error) {
    console.error('Error searching products:', error);
    return [];
  }
}

/**
 * تصفية المنتجات حسب الفئة
 */
export async function filterProductsByCategory(category: string): Promise<Product[]> {
  try {
    const products = await sqliteDB.getProducts();

    if (!category) {
      return products;
    }

    return products.filter((p: Product) => p.category === category);
  } catch (error) {
    console.error(`Error filtering products by category ${category}:`, error);
    return [];
  }
}

/**
 * الحصول على المنتجات المميزة
 */
export async function getFeaturedProducts(): Promise<Product[]> {
  try {
    const products = await sqliteDB.getProducts();
    return products.filter((p: Product) => p.featured);
  } catch (error) {
    console.error('Error getting featured products:', error);
    return [];
  }
}

/**
 * تهيئة المنتجات الافتراضية
 */
export async function initializeDefaultProducts(): Promise<void> {
  try {
    const currentProducts = await sqliteDB.getProducts();
    if (currentProducts.length === 0) {
      console.log("[ProductService] Initializing default products as DB is empty.");
      for (const product of initialProducts) {
        await sqliteDB.createProduct(product); // createProduct handles saving
      }
    }
  } catch (error) {
    console.error('Error initializing default products:', error);
  }
}

// تهيئة المنتجات الافتراضية عند تحميل الخدمة
if (typeof window !== 'undefined') {
  initializeDefaultProducts();
}

// Export as a class for easier usage
export class ProductService {
  static async getAllProducts(): Promise<Product[]> {
    return getAllProducts();
  }

  static async getProductById(id: string): Promise<Product | null> {
    return getProductById(id);
  }

  static async getProductBySlug(slug: string): Promise<Product | null> {
    return getProductBySlug(slug);
  }

  static async createProduct(productData: Partial<Product>): Promise<Product> {
    return createProduct(productData);
  }

  static async updateProduct(id: string, productData: Partial<Product>): Promise<Product | null> {
    return updateProduct(id, productData);
  }

  static async deleteProduct(id: string): Promise<boolean> {
    return deleteProduct(id);
  }

  static async searchProducts(query: string): Promise<Product[]> {
    return searchProducts(query);
  }

  static async filterProductsByCategory(category: string): Promise<Product[]> {
    return filterProductsByCategory(category);
  }

  static async getFeaturedProducts(limit?: number): Promise<Product[]> {
    const products = await getFeaturedProducts();
    return limit ? products.slice(0, limit) : products;
  }

  static async initializeDefaultProducts(): Promise<void> {
    return initializeDefaultProducts();
  }
}
