import { useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';

export type Language = 'ar' | 'en';

export interface UseLanguageReturn {
  language: Language;
  isRTL: boolean;
  isLoading: boolean;
  setLanguage: (language: Language) => void;
  toggleLanguage: () => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  formatMessage: (key: string, params?: Record<string, string | number>) => string;
  getLocalizedValue: (enValue: string, arValue?: string) => string;
}

/**
 * Hook for language management
 * Provides language state and methods for internationalization
 */
export function useLanguage(): UseLanguageReturn {
  const router = useRouter();
  const { language, isRTL, isLoading, setLanguage: setStoreLanguage } = useLanguageStore();
  const { t } = useTranslation();

  // Set language and update URL
  const setLanguage = useCallback((newLanguage: Language) => {
    setStoreLanguage(newLanguage);
    
    // Update URL to reflect language change
    const currentPath = window.location.pathname;
    const pathSegments = currentPath.split('/').filter(Boolean);
    
    // Remove current language from path if present
    if (pathSegments[0] === 'ar' || pathSegments[0] === 'en') {
      pathSegments.shift();
    }
    
    // Add new language to path
    const newPath = `/${newLanguage}${pathSegments.length > 0 ? '/' + pathSegments.join('/') : ''}`;
    
    // Navigate to new path
    router.push(newPath);
  }, [setStoreLanguage, router]);

  // Toggle between Arabic and English
  const toggleLanguage = useCallback(() => {
    const newLanguage = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLanguage);
  }, [language, setLanguage]);

  // Format message with parameters
  const formatMessage = useCallback((key: string, params?: Record<string, string | number>): string => {
    let message = t(key);
    
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        message = message.replace(new RegExp(`{${paramKey}}`, 'g'), String(value));
      });
    }
    
    return message;
  }, [t]);

  // Get localized value based on current language
  const getLocalizedValue = useCallback((enValue: string, arValue?: string): string => {
    if (language === 'ar' && arValue) {
      return arValue;
    }
    return enValue;
  }, [language]);

  // Update document direction and language attributes
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.lang = language;
      document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
      
      // Update body class for styling
      document.body.classList.toggle('rtl', isRTL);
      document.body.classList.toggle('ltr', !isRTL);
      document.body.classList.toggle('lang-ar', language === 'ar');
      document.body.classList.toggle('lang-en', language === 'en');
    }
  }, [language, isRTL]);

  // Store language preference in localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', language);
    }
  }, [language]);

  return {
    language,
    isRTL,
    isLoading,
    setLanguage,
    toggleLanguage,
    t,
    formatMessage,
    getLocalizedValue
  };
}
