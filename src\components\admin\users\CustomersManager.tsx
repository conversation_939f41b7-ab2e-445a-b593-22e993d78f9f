'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  ArrowUpDown,
  User,
  Mail,
  Phone,
  Calendar,
  ShoppingBag,
  CreditCard,
  MapPin,
  Heart
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { UserForm } from './UserForm';
import Link from 'next/link';

// نوع العميل
interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  ordersCount: number;
  totalSpent: number;
  wishlistCount: number;
  lastOrderDate: string | null;
  createdAt: string;
  status: 'active' | 'inactive' | 'blocked';
}

// بيانات العملاء (محاكاة)
const customers: Customer[] = Array.from({ length: 20 }, (_, index) => ({
  id: `customer-${index + 1}`,
  firstName: `First${index + 1}`,
  lastName: `Last${index + 1}`,
  email: `customer${index + 1}@example.com`,
  phone: `+1234567890${index}`,
  address: {
    street: `${index + 1} Main St`,
    city: 'City',
    state: 'State',
    zip: '12345',
    country: 'Country'
  },
  ordersCount: Math.floor(Math.random() * 10),
  totalSpent: Math.floor(Math.random() * 10000),
  wishlistCount: Math.floor(Math.random() * 5),
  lastOrderDate: Math.random() > 0.3 ? new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString() : null,
  createdAt: new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000).toISOString(),
  status: ['active', 'inactive', 'blocked'][Math.floor(Math.random() * 3)] as 'active' | 'inactive' | 'blocked'
}));

// مكون إدارة العملاء
export function CustomersManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<keyof Customer>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  
  // حالة العملاء
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [viewingCustomer, setViewingCustomer] = useState<Customer | null>(null);
  
  // تحديث العملاء المصفاة عند تغيير البحث أو التصفية
  useEffect(() => {
    let filtered = [...customers];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(customer => 
        customer.firstName.toLowerCase().includes(query) || 
        customer.lastName.toLowerCase().includes(query) || 
        customer.email.toLowerCase().includes(query) ||
        customer.phone.includes(query)
      );
    }
    
    // تطبيق تصفية الحالة
    if (selectedStatus) {
      filtered = filtered.filter(customer => customer.status === selectedStatus);
    }
    
    setFilteredCustomers(filtered);
  }, [searchQuery, selectedStatus]); // Removed 'customers'
  
  // ترتيب العملاء
  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    // Handle nulls first, especially for sortable fields like lastOrderDate
    if (sortField === 'lastOrderDate') {
      if (aValue === null && bValue === null) return 0;
      if (aValue === null) return sortDirection === 'asc' ? 1 : -1; // nulls last for asc, first for desc
      if (bValue === null) return sortDirection === 'asc' ? -1 : 1; // nulls last for asc, first for desc
      // Both are non-null strings if execution reaches here for lastOrderDate
    }
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    }
    
    return 0;
  });
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedCustomers.length / itemsPerPage);
  
  // الحصول على العملاء للصفحة الحالية
  const currentCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof Customer) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // تحرير عميل
  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowCustomerForm(true);
  };
  
  // إضافة عميل جديد
  const handleAddCustomer = () => {
    setEditingCustomer(null);
    setShowCustomerForm(true);
  };
  
  // حذف عميل
  const handleDeleteCustomer = (customerId: string) => {
    // هنا سيتم تنفيذ منطق حذف العميل
    console.log('Delete customer:', customerId);
    
    // في الإنتاج، سيتم استدعاء API لحذف العميل
    // وتحديث قائمة العملاء
  };
  
  // عرض تفاصيل العميل
  const handleViewCustomer = (customer: Customer) => {
    setViewingCustomer(customer);
  };
  
  // حفظ العميل (إضافة أو تحديث)
  const handleSaveCustomer = (customer: any) => {
    if (editingCustomer) {
      // تحديث عميل موجود
      console.log('Update customer:', customer);
      
      // في الإنتاج، سيتم استدعاء API لتحديث العميل
      // وتحديث قائمة العملاء
    } else {
      // إضافة عميل جديد
      console.log('Add customer:', customer);
      
      // في الإنتاج، سيتم استدعاء API لإضافة العميل
      // وتحديث قائمة العملاء
    }
    
    setShowCustomerForm(false);
  };
  
  // الحصول على لون حالة العميل
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'blocked':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };
  
  // ترجمة حالة العميل
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return language === 'ar' ? 'نشط' : 'Active';
      case 'inactive':
        return language === 'ar' ? 'غير نشط' : 'Inactive';
      case 'blocked':
        return language === 'ar' ? 'محظور' : 'Blocked';
      default:
        return status;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة العملاء' : 'Customers Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة حسابات العملاء وبياناتهم'
              : 'Manage customer accounts and their data'}
          </p>
        </div>
        
        <Button
          onClick={handleAddCustomer}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة عميل' : 'Add Customer'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن العملاء...' : 'Search customers...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="w-full md:w-64">
            <select
              value={selectedStatus || ''}
              onChange={(e) => setSelectedStatus(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الحالات' : 'All Statuses'}
              </option>
              <option value="active">
                {language === 'ar' ? 'نشط' : 'Active'}
              </option>
              <option value="inactive">
                {language === 'ar' ? 'غير نشط' : 'Inactive'}
              </option>
              <option value="blocked">
                {language === 'ar' ? 'محظور' : 'Blocked'}
              </option>
            </select>
          </div>
        </div>
      </Card>
      
      {/* جدول العملاء */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('firstName')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'العميل' : 'Customer'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('email')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('ordersCount')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الطلبات' : 'Orders'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('totalSpent')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الإنفاق' : 'Spent'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('status')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الحالة' : 'Status'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentCustomers.map((customer) => (
                <tr key={customer.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "w-10 h-10 rounded-full flex items-center justify-center",
                        isDarkMode ? "bg-slate-700" : "bg-slate-200"
                      )}>
                        <span className="text-lg font-medium">
                          {customer.firstName.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">{customer.firstName} {customer.lastName}</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          {new Date(customer.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-slate-400" />
                      <span>{customer.email}</span>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Phone className="h-4 w-4 text-slate-400" />
                      <span className="text-sm text-slate-500 dark:text-slate-400">
                        {customer.phone}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="h-4 w-4 text-slate-400" />
                      <span>{customer.ordersCount}</span>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-slate-400" />
                      <span className="text-sm text-slate-500 dark:text-slate-400">
                        {customer.lastOrderDate
                          ? new Date(customer.lastOrderDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
                          : language === 'ar' ? 'لا يوجد' : 'None'
                        }
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-slate-400" />
                      <span>
                        {new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
                          style: 'currency',
                          currency: 'USD'
                        }).format(customer.totalSpent)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Heart className="h-4 w-4 text-slate-400" />
                      <span className="text-sm text-slate-500 dark:text-slate-400">
                        {language === 'ar'
                          ? `${customer.wishlistCount} منتج في المفضلة`
                          : `${customer.wishlistCount} in wishlist`
                        }
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      getStatusColor(customer.status)
                    )}>
                      {getStatusText(customer.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewCustomer(customer)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleEditCustomer(customer)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Edit className="h-5 w-5 text-yellow-500" />
                      </button>
                      <button
                        onClick={() => handleDeleteCustomer(customer.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Trash2 className="h-5 w-5 text-red-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
            <div className="text-sm text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredCustomers.length)} من ${filteredCustomers.length} عميل`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredCustomers.length)} of ${filteredCustomers.length} customers`
              }
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>
      
      {/* نموذج إضافة/تحرير العميل */}
      {showCustomerForm && (
        <UserForm
          user={editingCustomer}
          onSave={handleSaveCustomer}
          onCancel={() => setShowCustomerForm(false)}
        />
      )}
    </div>
  );
}
