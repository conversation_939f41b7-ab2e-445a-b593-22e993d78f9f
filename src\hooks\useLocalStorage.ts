import { useState, useEffect, useCallback } from 'react';

export interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook for managing localStorage with TypeScript support
 * Provides safe localStorage access with error handling and SSR compatibility
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T,
  options?: {
    serialize?: (value: T) => string;
    deserialize?: (value: string) => T;
  }
): UseLocalStorageReturn<T> {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [storedValue, setStoredValue] = useState<T>(initialValue);

  // Custom serialization functions
  const serialize = options?.serialize || JSON.stringify;
  const deserialize = options?.deserialize || JSON.parse;

  // Read value from localStorage
  const readValue = useCallback((): T => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (item === null) {
        return initialValue;
      }
      return deserialize(item);
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      setError(error instanceof Error ? error.message : 'Failed to read from localStorage');
      return initialValue;
    }
  }, [key, initialValue, deserialize]);

  // Set value in localStorage
  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      if (typeof window === 'undefined') {
        console.warn('localStorage is not available in this environment');
        return;
      }

      try {
        setError(null);
        
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        // Save to localStorage
        window.localStorage.setItem(key, serialize(valueToStore));
        
        // Update state
        setStoredValue(valueToStore);
        
        // Dispatch custom event for cross-tab synchronization
        window.dispatchEvent(
          new CustomEvent('localStorage-change', {
            detail: { key, value: valueToStore }
          })
        );
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
        setError(error instanceof Error ? error.message : 'Failed to write to localStorage');
      }
    },
    [key, serialize, storedValue]
  );

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    if (typeof window === 'undefined') {
      console.warn('localStorage is not available in this environment');
      return;
    }

    try {
      setError(null);
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
      
      // Dispatch custom event for cross-tab synchronization
      window.dispatchEvent(
        new CustomEvent('localStorage-change', {
          detail: { key, value: null }
        })
      );
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
      setError(error instanceof Error ? error.message : 'Failed to remove from localStorage');
    }
  }, [key, initialValue]);

  // Initialize value from localStorage on mount
  useEffect(() => {
    const value = readValue();
    setStoredValue(value);
    setIsLoading(false);
  }, [readValue]);

  // Listen for changes in localStorage (cross-tab synchronization)
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const newValue = deserialize(e.newValue);
          setStoredValue(newValue);
        } catch (error) {
          console.warn(`Error parsing localStorage value for key "${key}":`, error);
        }
      } else if (e.key === key && e.newValue === null) {
        setStoredValue(initialValue);
      }
    };

    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === key) {
        if (e.detail.value !== null) {
          setStoredValue(e.detail.value);
        } else {
          setStoredValue(initialValue);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('localStorage-change', handleCustomStorageChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('localStorage-change', handleCustomStorageChange as EventListener);
    };
  }, [key, initialValue, deserialize]);

  return {
    value: storedValue,
    setValue,
    removeValue,
    isLoading,
    error
  };
}
