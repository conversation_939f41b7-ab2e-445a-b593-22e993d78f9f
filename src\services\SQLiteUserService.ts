/**
 * خدمة إدارة المستخدمين باستخدام SQLite
 * تستبدل هذه الخدمة استخدام LocalUserService في المشروع
 */

import { sqlite } from '../lib/sqlite';
import { User } from '../types';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// مفاتيح التخزين المحلي
const LOCAL_USERS_KEY = 'local-users';
const LOCAL_PROFILES_KEY = 'local-profiles';
const LOCAL_ADMIN_INITIALIZED_KEY = 'admin-initialized';

// الحصول على المستخدمين المحليين من localStorage
export async function getLocalUsers(): Promise<User[]> {
  try {
    const users = await sqlite.getUsers();
    return users.map((user: User) => ({
      ...user,
      password_hash: undefined // Never expose password hash
    }));
  } catch (error) {
    console.error('Error getting local users:', error);
    return [];
  }
}

// الحصول على ملفات المستخدمين المحليين من localStorage
export async function getLocalProfiles(): Promise<User[]> {
  try {
    const profiles = await sqlite.getUsers();
    return profiles;
  } catch (error) {
    console.error('Error getting local profiles:', error);
    return [];
  }
};

// حفظ المستخدمين المحليين في localStorage
export async function saveLocalUsers(users: User[]): Promise<void> {
  try {
    // Only save user data that should be persisted
    for (const user of users) {
      const userDataToUpdate: Partial<User> = {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      };

      // if (user.hasOwnProperty('emailVerified')) { // Temporarily commented out
      //   userDataToUpdate.emailVerified = user.emailVerified;
      // }
      // if (user.hasOwnProperty('lastLogin')) { // Temporarily commented out
      //   userDataToUpdate.lastLogin = user.lastLogin;
      // }
      // if (user.hasOwnProperty('isActive')) { // Temporarily commented out
      //   userDataToUpdate.isActive = user.isActive;
      // }
      // Add other optional fields similarly if they cause issues

      const definedUserData = Object.fromEntries(
        Object.entries(userDataToUpdate).filter(([_, v]) => v !== undefined)
      ) as Partial<User>;

      if (Object.keys(definedUserData).length > 0 && user.id) { 
         await sqlite.updateUser(user.id, definedUserData);
      }
    }
  } catch (error) {
    console.error('Error saving local users:', error);
    throw error;
  }
}

// حفظ ملفات المستخدمين المحليين في localStorage
export async function saveLocalProfiles(profiles: User[]): Promise<void> {
  try {
    // لا يوجد طريقة مباشرة لحفظ مجموعة من المستخدمين في sqlite
    // لذلك نقوم بحفظ كل مستخدم على حدة
    for (const profile of profiles) {
      await sqlite.updateUser(profile.id, profile);
    }
  } catch (error) {
    console.error('Error saving local profiles:', error);
  }
};

// التحقق مما إذا كان المستخدم المسؤول قد تم تهيئته
export async function isAdminInitialized(): Promise<boolean> {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(LOCAL_ADMIN_INITIALIZED_KEY) === 'true';
  }
  return false;
};

// تعيين حالة تهيئة المستخدم المسؤول
export async function setAdminInitialized(initialized: boolean): Promise<void> {
  if (typeof window !== 'undefined') {
    localStorage.setItem(LOCAL_ADMIN_INITIALIZED_KEY, initialized ? 'true' : 'false');
  }
};

// إعادة تعيين جميع بيانات المستخدمين المحليين
export async function resetLocalUsers(): Promise<void> {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(LOCAL_USERS_KEY);
    localStorage.removeItem(LOCAL_PROFILES_KEY);
    localStorage.removeItem(LOCAL_ADMIN_INITIALIZED_KEY);

    // إعادة تعيين المستخدمين في SQLite
    await sqlite.resetLocalUsers();
  }
};

// إنشاء مستخدم مسؤول افتراضي
export async function createDefaultAdminUser(): Promise<boolean> {
  if (typeof window === 'undefined') {
    return false; // تنفيذ فقط في جانب العميل
  }

  console.log('Creating default admin user with SQLite...');

  // التحقق مما إذا كان المستخدم المسؤول قد تم تهيئته بالفعل
  if (await isAdminInitialized()) {
    console.log('Admin user already initialized');
    return false;
  }

  const users = await getLocalUsers();
  const profiles = await getLocalProfiles();

  // مسح جميع المستخدمين والملفات الشخصية الموجودة
  await resetLocalUsers();

  // Data for the default admin user
  const adminEmail = '<EMAIL>';
  const adminPassword = 'password'; // Plain text password
  // Use the actual DB function for finding user by email
  const existingUser = await sqlite.getUserByEmail(adminEmail); 
  const adminUserId = existingUser?.id || uuidv4();

  let adminUser = existingUser;

  if (!adminUser) {
    console.log('Creating default admin user...');
    const hashedPassword = bcrypt.hashSync(adminPassword, 8);
    const now = new Date().toISOString();

    const newAdminData: Omit<User, 'id' | 'updatedAt' | 'avatarUrl' | 'phoneNumber' | 'addresses' | 'emailVerified' | 'lastLogin' | 'isActive' | 'failedLoginAttempts' | 'lastFailedLogin' | 'lockoutUntil' | 'preferences' | 'phone'> & { password_hash: string } = {
      email: adminEmail,
      password_hash: hashedPassword,
      firstName: 'أحمد',
      lastName: 'المدير',
      role: 'admin', 
      createdAt: now,
      // other essential fields required by _server_createUser can be added here
    };

    // Create user in SQLite database
    // We need to call the actual createUser function from sqlite.ts
    // Assuming sqlite object has a createUser method similar to _server_createUser structure
    adminUser = await sqlite.createUser(newAdminData); 
    // If createUser expects id, ensure it's passed or handled internally by createUser

    if (adminUser) {
      console.log('Default admin user created successfully in DB.');

      // Now, if saveLocalUsers is intended to update additional profile information
      // that wasn't part of the initial creation, we can prepare that data.
      const defaultAdminProfileForUpdate: Partial<User> = {
        firstName: 'أحمد',
        lastName: 'المدير',
        // role: 'admin', // Role is set during creation
        // createdAt: now, // Set during creation
        // @ts-expect-error This lint error seems incorrect. User interface defines avatarUrl, and updateUser should handle it.
        avatarUrl: '/avatars/default-admin.png', // Example additional data
        phoneNumber: '+966500000000', // Example additional data
        addresses: [{
          type: 'billing',
          street: 'شارع الملك فهد',
          city: 'الرياض',
          // state: 'الرياض', // Assuming state is not directly in User.Address, adjust if needed
          postalCode: '12345',
          country: 'المملكة العربية السعودية',
          isDefault: true
        }],
        // preferences: { // Assuming preferences is a separate field or JSON string
        //   language: 'ar',
        //   theme: 'light',
        //   notifications: true,
        //   newsletter: false
        // },
        isActive: true,
        emailVerified: true, // Typically set after email verification flow
      };
      
      // Ensure that we only try to update if adminUser and its id exist
      if (adminUser.id) {
         await sqlite.updateUser(adminUser.id, defaultAdminProfileForUpdate); 
        // Or, if saveLocalUsers is the correct function to call for this kind of update:
        // await saveLocalUsers([adminUser]); // but pass the updated adminUser object
        console.log('Default admin user profile information updated.');
      } else {
        console.error('Failed to get ID for default admin user after creation.');
      }
    } else {
      console.error('Failed to create default admin user in DB.');
    }
  } else {
    console.log('Default admin user already exists.');
  }

  // The old saveLocalUsers call with potentially incomplete data should be removed or re-evaluated.
  // The original snippet was: await saveLocalUsers([defaultAdminUser]);
  // This is now handled within the if (!adminUser) block with more complete data.

  console.log('Default admin user created:', adminEmail);
  return true;
};

// البحث عن مستخدم بالبريد الإلكتروني وكلمة المرور
export async function findUserByCredentials(email: string, password: string): Promise<any> {
  const users = await getLocalUsers();
  return users.find((u: any) => u.email === email && u.password === password);
};

// البحث عن ملف مستخدم بالمعرف
export async function findProfileById(id: string): Promise<User | undefined> {
  const profiles = await getLocalProfiles();
  return profiles.find((p: any) => p.id === id);
};

// البحث عن ملف مستخدم بالبريد الإلكتروني
export async function findProfileByEmail(email: string): Promise<User | undefined> {
  const profiles = await getLocalProfiles();
  return profiles.find((p: any) => p.email === email);
};

// إضافة أو تحديث ملف مستخدم
export async function saveProfile(profile: User): Promise<User> {
  const profiles = await getLocalProfiles();
  const existingIndex = profiles.findIndex((p: any) => p.id === profile.id);

  if (existingIndex >= 0) {
    // تحديث ملف موجود
    profiles[existingIndex] = { ...profiles[existingIndex], ...profile };
  } else {
    // إضافة ملف جديد
    profiles.push(profile);
  }

  await saveLocalProfiles(profiles);
  return profile;
};

// تهيئة البيانات الافتراضية
export async function initializeDefaultData(): Promise<void> {
  // تهيئة البيانات الافتراضية تتم تلقائيًا عند إنشاء كائن sqlite
  console.log('Initializing default data...');
};
