export interface BlogPost {
  id: string;
  title: string;
  title_ar: string;
  slug: string;
  excerpt: string;
  excerpt_ar: string;
  content: string;
  content_ar: string;
  featuredImage?: BlogImage;
  images: BlogImage[];
  author: BlogAuthor;
  category: BlogCategory;
  tags: BlogTag[];
  status: BlogPostStatus;
  visibility: BlogPostVisibility;
  isFeatured: boolean;
  isSticky: boolean;
  allowComments: boolean;
  commentCount: number;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  readingTime: number; // in minutes
  seo: BlogSEO;
  relatedPosts: string[];
  publishedAt?: string;
  scheduledAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BlogImage {
  id: string;
  url: string;
  alt: string;
  alt_ar: string;
  caption?: string;
  caption_ar?: string;
  width?: number;
  height?: number;
  size?: number;
}

export interface BlogAuthor {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  avatar?: string;
  bio?: string;
  bio_ar?: string;
  email?: string;
  website?: string;
  socialLinks?: AuthorSocialLinks;
  postCount: number;
  isActive: boolean;
}

export interface AuthorSocialLinks {
  twitter?: string;
  linkedin?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
  github?: string;
}

export interface BlogCategory {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  description?: string;
  description_ar?: string;
  color?: string;
  icon?: string;
  parentId?: string;
  postCount: number;
  isActive: boolean;
  sortOrder: number;
}

export interface BlogTag {
  id: string;
  name: string;
  name_ar: string;
  slug: string;
  description?: string;
  description_ar?: string;
  color?: string;
  postCount: number;
  isActive: boolean;
}

export type BlogPostStatus = 'draft' | 'published' | 'scheduled' | 'archived' | 'deleted';
export type BlogPostVisibility = 'public' | 'private' | 'password_protected' | 'members_only';

export interface BlogSEO {
  metaTitle?: string;
  metaTitle_ar?: string;
  metaDescription?: string;
  metaDescription_ar?: string;
  metaKeywords: string[];
  canonicalUrl?: string;
  ogTitle?: string;
  ogTitle_ar?: string;
  ogDescription?: string;
  ogDescription_ar?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterTitle_ar?: string;
  twitterDescription?: string;
  twitterDescription_ar?: string;
  twitterImage?: string;
  structuredData?: Record<string, any>;
  focusKeyword?: string;
  readabilityScore?: number;
  seoScore?: number;
}

export interface BlogComment {
  id: string;
  postId: string;
  parentId?: string;
  author: CommentAuthor;
  content: string;
  status: CommentStatus;
  isApproved: boolean;
  likeCount: number;
  dislikeCount: number;
  reportCount: number;
  replies: BlogComment[];
  ipAddress: string;
  userAgent: string;
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
}

export interface CommentAuthor {
  id?: string;
  name: string;
  email: string;
  website?: string;
  avatar?: string;
  isRegistered: boolean;
}

export type CommentStatus = 'pending' | 'approved' | 'spam' | 'trash';

export interface BlogNewsletter {
  id: string;
  email: string;
  name?: string;
  status: NewsletterStatus;
  preferences: NewsletterPreferences;
  source: string;
  ipAddress: string;
  subscribedAt: string;
  unsubscribedAt?: string;
  confirmedAt?: string;
}

export type NewsletterStatus = 'pending' | 'confirmed' | 'unsubscribed' | 'bounced' | 'complained';

export interface NewsletterPreferences {
  frequency: 'daily' | 'weekly' | 'monthly';
  categories: string[];
  tags: string[];
  language: 'ar' | 'en' | 'both';
}

export interface BlogStats {
  totalPosts: number;
  publishedPosts: number;
  draftPosts: number;
  totalViews: number;
  totalComments: number;
  totalShares: number;
  totalLikes: number;
  totalSubscribers: number;
  popularPosts: BlogPostSummary[];
  recentPosts: BlogPostSummary[];
  topCategories: { category: BlogCategory; postCount: number }[];
  topTags: { tag: BlogTag; postCount: number }[];
  monthlyStats: MonthlyBlogStats[];
}

export interface MonthlyBlogStats {
  month: string;
  year: number;
  posts: number;
  views: number;
  comments: number;
  shares: number;
  subscribers: number;
}

export interface BlogSearchFilters {
  category?: string;
  tag?: string;
  author?: string;
  status?: BlogPostStatus;
  featured?: boolean;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  sortBy?: BlogSortOption;
  sortOrder?: 'asc' | 'desc';
}

export type BlogSortOption = 
  | 'title'
  | 'date'
  | 'views'
  | 'comments'
  | 'likes'
  | 'shares'
  | 'reading_time';

export interface BlogListResponse {
  posts: BlogPost[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    categories: { id: string; name: string; count: number }[];
    tags: { id: string; name: string; count: number }[];
    authors: { id: string; name: string; count: number }[];
  };
}

export interface CreateBlogPostRequest {
  title: string;
  title_ar: string;
  excerpt: string;
  excerpt_ar: string;
  content: string;
  content_ar: string;
  featuredImage?: Omit<BlogImage, 'id'>;
  images: Omit<BlogImage, 'id'>[];
  categoryId: string;
  tagIds: string[];
  status: BlogPostStatus;
  visibility: BlogPostVisibility;
  isFeatured: boolean;
  isSticky: boolean;
  allowComments: boolean;
  seo?: Partial<BlogSEO>;
  scheduledAt?: string;
}

export interface UpdateBlogPostRequest extends Partial<CreateBlogPostRequest> {
  id: string;
}

export interface CreateBlogCommentRequest {
  postId: string;
  parentId?: string;
  content: string;
  author: {
    name: string;
    email: string;
    website?: string;
  };
}

export interface CreateBlogCategoryRequest {
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  color?: string;
  icon?: string;
  parentId?: string;
}

export interface CreateBlogTagRequest {
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  color?: string;
}

export interface CreateBlogAuthorRequest {
  name: string;
  name_ar: string;
  bio?: string;
  bio_ar?: string;
  email?: string;
  website?: string;
  socialLinks?: AuthorSocialLinks;
  avatar?: string;
}

export interface SubscribeNewsletterRequest {
  email: string;
  name?: string;
  preferences?: Partial<NewsletterPreferences>;
  source?: string;
}

// Utility types
export type BlogPostSummary = Pick<BlogPost,
  | 'id'
  | 'title'
  | 'title_ar'
  | 'slug'
  | 'excerpt'
  | 'excerpt_ar'
  | 'featuredImage'
  | 'author'
  | 'category'
  | 'tags'
  | 'viewCount'
  | 'commentCount'
  | 'readingTime'
  | 'publishedAt'
>;

export type BlogPostCard = Pick<BlogPost,
  | 'id'
  | 'title'
  | 'title_ar'
  | 'slug'
  | 'excerpt'
  | 'excerpt_ar'
  | 'featuredImage'
  | 'author'
  | 'category'
  | 'tags'
  | 'isFeatured'
  | 'viewCount'
  | 'commentCount'
  | 'likeCount'
  | 'readingTime'
  | 'publishedAt'
>;

export type BlogPostDetails = Omit<BlogPost, 'content' | 'content_ar'> & {
  content: string;
  content_ar: string;
  previousPost?: BlogPostSummary;
  nextPost?: BlogPostSummary;
  relatedPostsData?: BlogPostSummary[];
};

export type PublicBlogPost = Omit<BlogPost, 
  | 'status' 
  | 'visibility' 
  | 'scheduledAt' 
  | 'createdAt' 
  | 'updatedAt'
> & {
  publishedAt: string;
};
