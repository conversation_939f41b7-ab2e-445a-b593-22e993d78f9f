import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface UIState {
  // Loading states
  isLoading: boolean;
  loadingMessage: string;
  
  // Modal states
  isModalOpen: boolean;
  modalType: string | null;
  modalData: any;
  
  // Sidebar states
  isSidebarOpen: boolean;
  sidebarType: 'navigation' | 'cart' | 'filters' | null;
  
  // Mobile menu
  isMobileMenuOpen: boolean;
  
  // Search
  isSearchOpen: boolean;
  searchQuery: string;
  
  // Notifications
  notifications: Notification[];
  
  // Toast messages
  toasts: Toast[];
  
  // Page transitions
  isPageTransitioning: boolean;
  
  // Scroll position
  scrollPosition: number;
  
  // Layout preferences
  layoutMode: 'grid' | 'list';
  itemsPerPage: number;
  
  // Accessibility
  isHighContrast: boolean;
  fontSize: 'small' | 'medium' | 'large';
  
  // Actions
  setLoading: (isLoading: boolean, message?: string) => void;
  openModal: (type: string, data?: any) => void;
  closeModal: () => void;
  toggleSidebar: (type?: 'navigation' | 'cart' | 'filters') => void;
  closeSidebar: () => void;
  toggleMobileMenu: () => void;
  closeMobileMenu: () => void;
  openSearch: () => void;
  closeSearch: () => void;
  setSearchQuery: (query: string) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  addToast: (toast: Omit<Toast, 'id' | 'timestamp'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  setPageTransitioning: (isTransitioning: boolean) => void;
  setScrollPosition: (position: number) => void;
  setLayoutMode: (mode: 'grid' | 'list') => void;
  setItemsPerPage: (count: number) => void;
  toggleHighContrast: () => void;
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface Toast {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  message: string;
  timestamp: number;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const generateId = () => Math.random().toString(36).substr(2, 9);

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      isLoading: false,
      loadingMessage: '',
      isModalOpen: false,
      modalType: null,
      modalData: null,
      isSidebarOpen: false,
      sidebarType: null,
      isMobileMenuOpen: false,
      isSearchOpen: false,
      searchQuery: '',
      notifications: [],
      toasts: [],
      isPageTransitioning: false,
      scrollPosition: 0,
      layoutMode: 'grid',
      itemsPerPage: 12,
      isHighContrast: false,
      fontSize: 'medium',

      // Actions
      setLoading: (isLoading, message = '') =>
        set({ isLoading, loadingMessage: message }),

      openModal: (type, data = null) =>
        set({ isModalOpen: true, modalType: type, modalData: data }),

      closeModal: () =>
        set({ isModalOpen: false, modalType: null, modalData: null }),

      toggleSidebar: (type) => {
        const { isSidebarOpen, sidebarType } = get();
        if (isSidebarOpen && sidebarType === type) {
          set({ isSidebarOpen: false, sidebarType: null });
        } else {
          set({ isSidebarOpen: true, sidebarType: type });
        }
      },

      closeSidebar: () =>
        set({ isSidebarOpen: false, sidebarType: null }),

      toggleMobileMenu: () =>
        set((state) => ({ isMobileMenuOpen: !state.isMobileMenuOpen })),

      closeMobileMenu: () =>
        set({ isMobileMenuOpen: false }),

      openSearch: () =>
        set({ isSearchOpen: true }),

      closeSearch: () =>
        set({ isSearchOpen: false, searchQuery: '' }),

      setSearchQuery: (query) =>
        set({ searchQuery: query }),

      addNotification: (notification) =>
        set((state) => ({
          notifications: [
            {
              ...notification,
              id: generateId(),
              timestamp: Date.now(),
              read: false
            },
            ...state.notifications
          ]
        })),

      removeNotification: (id) =>
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        })),

      clearNotifications: () =>
        set({ notifications: [] }),

      addToast: (toast) => {
        const newToast = {
          ...toast,
          id: generateId(),
          timestamp: Date.now()
        };
        
        set((state) => ({
          toasts: [...state.toasts, newToast]
        }));

        // Auto-remove toast after duration
        const duration = toast.duration || 5000;
        setTimeout(() => {
          set((state) => ({
            toasts: state.toasts.filter(t => t.id !== newToast.id)
          }));
        }, duration);
      },

      removeToast: (id) =>
        set((state) => ({
          toasts: state.toasts.filter(t => t.id !== id)
        })),

      clearToasts: () =>
        set({ toasts: [] }),

      setPageTransitioning: (isTransitioning) =>
        set({ isPageTransitioning: isTransitioning }),

      setScrollPosition: (position) =>
        set({ scrollPosition: position }),

      setLayoutMode: (mode) =>
        set({ layoutMode: mode }),

      setItemsPerPage: (count) =>
        set({ itemsPerPage: count }),

      toggleHighContrast: () =>
        set((state) => ({ isHighContrast: !state.isHighContrast })),

      setFontSize: (size) =>
        set({ fontSize: size })
    }),
    {
      name: 'ui-store',
      partialize: (state) => ({
        layoutMode: state.layoutMode,
        itemsPerPage: state.itemsPerPage,
        isHighContrast: state.isHighContrast,
        fontSize: state.fontSize
      })
    }
  )
);
