# Hostinger Deployment Guide

This guide provides step-by-step instructions for deploying EcommercePro on Hostinger hosting services.

## Prerequisites

- Hostinger hosting account (VPS or Business hosting)
- Domain name configured with Hostinger
- SSH access to your hosting account
- Node.js 18+ support (available on VPS plans)

## Deployment Options

### Option 1: VPS Deployment (Recommended)

#### Step 1: Server Setup

1. **Access your VPS via SSH:**
   ```bash
   ssh root@your-server-ip
   ```

2. **Update system packages:**
   ```bash
   apt update && apt upgrade -y
   ```

3. **Install Node.js 18:**
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   apt-get install -y nodejs
   ```

4. **Install PM2 for process management:**
   ```bash
   npm install -g pm2
   ```

5. **Install Nginx:**
   ```bash
   apt install nginx -y
   systemctl start nginx
   systemctl enable nginx
   ```

#### Step 2: Application Deployment

1. **Clone the repository:**
   ```bash
   cd /var/www
   git clone https://github.com/your-username/ecommercepro.git
   cd ecommercepro
   ```

2. **Install dependencies:**
   ```bash
   npm ci --only=production
   ```

3. **Create environment file:**
   ```bash
   cp .env.example .env.production
   nano .env.production
   ```

4. **Configure environment variables:**
   ```env
   NODE_ENV=production
   NEXT_PUBLIC_APP_URL=https://yourdomain.com
   JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
   ENCRYPTION_KEY=your-super-secret-encryption-key-32-chars
   
   # Database
   SQLITE_DB_PATH=/var/www/ecommercepro/data/database.sqlite
   
   # Email Configuration
   SMTP_HOST=smtp.hostinger.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-email-password
   EMAIL_FROM_ADDRESS=<EMAIL>
   
   # Payment Gateways
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_PUBLISHABLE_KEY=pk_live_...
   PAYPAL_CLIENT_ID=your-paypal-client-id
   PAYPAL_CLIENT_SECRET=your-paypal-client-secret
   ```

5. **Build the application:**
   ```bash
   npm run build
   ```

6. **Create data directories:**
   ```bash
   mkdir -p data uploads
   chown -R www-data:www-data data uploads
   chmod 755 data uploads
   ```

7. **Initialize database:**
   ```bash
   npm run db:init
   ```

#### Step 3: PM2 Configuration

1. **Create PM2 ecosystem file:**
   ```bash
   nano ecosystem.config.js
   ```

   ```javascript
   module.exports = {
     apps: [{
       name: 'ecommercepro',
       script: 'npm',
       args: 'start',
       cwd: '/var/www/ecommercepro',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 3000
       },
       error_file: '/var/log/ecommercepro/error.log',
       out_file: '/var/log/ecommercepro/out.log',
       log_file: '/var/log/ecommercepro/combined.log',
       time: true
     }]
   };
   ```

2. **Create log directory:**
   ```bash
   mkdir -p /var/log/ecommercepro
   chown -R www-data:www-data /var/log/ecommercepro
   ```

3. **Start the application:**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

#### Step 4: Nginx Configuration

1. **Create Nginx site configuration:**
   ```bash
   nano /etc/nginx/sites-available/ecommercepro
   ```

   ```nginx
   server {
       listen 80;
       server_name yourdomain.com www.yourdomain.com;
       return 301 https://$server_name$request_uri;
   }

   server {
       listen 443 ssl http2;
       server_name yourdomain.com www.yourdomain.com;

       # SSL Configuration
       ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
       ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;

       # Security Headers
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header Referrer-Policy "no-referrer-when-downgrade" always;
       add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

       # Gzip Compression
       gzip on;
       gzip_vary on;
       gzip_min_length 1024;
       gzip_proxied expired no-cache no-store private must-revalidate auth;
       gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

       # Static files
       location /_next/static {
           alias /var/www/ecommercepro/.next/static;
           expires 365d;
           access_log off;
       }

       location /uploads {
           alias /var/www/ecommercepro/uploads;
           expires 30d;
           access_log off;
       }

       # Main application
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

2. **Enable the site:**
   ```bash
   ln -s /etc/nginx/sites-available/ecommercepro /etc/nginx/sites-enabled/
   nginx -t
   systemctl reload nginx
   ```

#### Step 5: SSL Certificate Setup

1. **Install Certbot:**
   ```bash
   apt install certbot python3-certbot-nginx -y
   ```

2. **Obtain SSL certificate:**
   ```bash
   certbot --nginx -d yourdomain.com -d www.yourdomain.com
   ```

3. **Set up auto-renewal:**
   ```bash
   crontab -e
   # Add this line:
   0 12 * * * /usr/bin/certbot renew --quiet
   ```

### Option 2: Shared Hosting Deployment

For shared hosting plans that support Node.js:

#### Step 1: File Upload

1. **Build the application locally:**
   ```bash
   npm run build
   npm run export
   ```

2. **Upload files via FTP/SFTP:**
   - Upload the `out` folder contents to your domain's public_html directory
   - Upload the `uploads` folder
   - Upload necessary server files

#### Step 2: Database Setup

1. **Create SQLite database:**
   - Upload the database file to a secure location outside public_html
   - Update the database path in your configuration

#### Step 3: Environment Configuration

1. **Create .htaccess file:**
   ```apache
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

   # Security headers
   Header always set X-Frame-Options "SAMEORIGIN"
   Header always set X-XSS-Protection "1; mode=block"
   Header always set X-Content-Type-Options "nosniff"
   ```

## Post-Deployment Configuration

### 1. Database Initialization

```bash
# Run database migrations
npm run db:migrate

# Seed initial data
npm run db:seed
```

### 2. Create Admin User

```bash
# Access the application console
npm run console

# Create admin user
node scripts/create-admin.js
```

### 3. Configure Email Settings

1. **Set up email account in Hostinger:**
   - Create email account: <EMAIL>
   - Note SMTP settings

2. **Test email functionality:**
   ```bash
   npm run test:email
   ```

### 4. Payment Gateway Setup

#### Stripe Configuration:
1. Create Stripe account
2. Get API keys from Stripe dashboard
3. Configure webhook endpoints
4. Test payment flow

#### PayPal Configuration:
1. Create PayPal developer account
2. Create application
3. Get client ID and secret
4. Configure webhook endpoints

### 5. Monitoring Setup

1. **Set up log rotation:**
   ```bash
   nano /etc/logrotate.d/ecommercepro
   ```

   ```
   /var/log/ecommercepro/*.log {
       daily
       missingok
       rotate 52
       compress
       delaycompress
       notifempty
       create 644 www-data www-data
       postrotate
           pm2 reload ecommercepro
       endscript
   }
   ```

2. **Configure monitoring:**
   ```bash
   pm2 install pm2-logrotate
   pm2 set pm2-logrotate:max_size 10M
   pm2 set pm2-logrotate:retain 30
   ```

## Backup Strategy

### 1. Database Backup

```bash
# Create backup script
nano /usr/local/bin/backup-ecommercepro.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/ecommercepro"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup database
cp /var/www/ecommercepro/data/database.sqlite $BACKUP_DIR/database_$DATE.sqlite

# Backup uploads
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /var/www/ecommercepro/uploads

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sqlite" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 2. Automated Backups

```bash
chmod +x /usr/local/bin/backup-ecommercepro.sh
crontab -e
# Add daily backup at 2 AM
0 2 * * * /usr/local/bin/backup-ecommercepro.sh
```

## Troubleshooting

### Common Issues:

1. **Application won't start:**
   - Check PM2 logs: `pm2 logs ecommercepro`
   - Verify environment variables
   - Check file permissions

2. **Database connection errors:**
   - Verify database file path
   - Check file permissions
   - Ensure directory exists

3. **Email not working:**
   - Test SMTP settings
   - Check firewall rules
   - Verify email account credentials

4. **Payment gateway issues:**
   - Verify API keys
   - Check webhook configurations
   - Test in sandbox mode first

### Performance Optimization:

1. **Enable caching:**
   ```bash
   # Install Redis
   apt install redis-server -y
   systemctl start redis
   systemctl enable redis
   ```

2. **Optimize Nginx:**
   - Enable gzip compression
   - Set proper cache headers
   - Configure rate limiting

3. **Monitor performance:**
   ```bash
   pm2 monit
   ```

## Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Strong passwords for all accounts
- [ ] Regular security updates
- [ ] Firewall configured
- [ ] Database secured
- [ ] File permissions set correctly
- [ ] Backup strategy implemented
- [ ] Monitoring configured

## Support

For deployment issues:
1. Check the troubleshooting section
2. Review application logs
3. Contact Hostinger support for server-related issues
4. Submit issues to the project repository

---

**Note:** This guide assumes you have basic knowledge of Linux server administration. For shared hosting, some steps may not be applicable or may require different approaches.
