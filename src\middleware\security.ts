/**
 * Security Middleware for API Routes
 * Implements authentication, authorization, rate limiting, and CSRF protection
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  checkRateLimit, 
  validateCSRFToken, 
  addSecurityHeaders, 
  getClientIP,
  trackLoginAttempt 
} from '../lib/security';
import { userRepository } from '../lib/database-secure';
import { validateInput } from '../lib/validation-enhanced';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '24h';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

/**
 * Rate limiting middleware
 */
export function rateLimitMiddleware(request: NextRequest): NextResponse | null {
  const ip = getClientIP(request);
  const rateLimit = checkRateLimit(ip);
  
  if (!rateLimit.allowed) {
    const response = NextResponse.json(
      { 
        error: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
      },
      { status: 429 }
    );
    
    response.headers.set('X-RateLimit-Limit', '100');
    response.headers.set('X-RateLimit-Remaining', '0');
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());
    response.headers.set('Retry-After', Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString());
    
    return addSecurityHeaders(response);
  }
  
  return null;
}

/**
 * CSRF protection middleware
 */
export function csrfMiddleware(request: NextRequest): NextResponse | null {
  // Skip CSRF for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
    return null;
  }
  
  const csrfToken = request.headers.get('x-csrf-token');
  const sessionId = request.headers.get('x-session-id') || request.cookies.get('session-id')?.value;
  
  if (!csrfToken || !sessionId) {
    const response = NextResponse.json(
      { error: 'CSRF token missing' },
      { status: 403 }
    );
    return addSecurityHeaders(response);
  }
  
  if (!validateCSRFToken(sessionId, csrfToken)) {
    const response = NextResponse.json(
      { error: 'Invalid CSRF token' },
      { status: 403 }
    );
    return addSecurityHeaders(response);
  }
  
  return null;
}

/**
 * Authentication middleware
 */
export function authMiddleware(request: NextRequest): { user: any; error?: NextResponse } | { error: NextResponse } {
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;
  
  if (!token) {
    const response = NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
    return { error: addSecurityHeaders(response) };
  }
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    const user = userRepository.getUserById(decoded.userId);
    
    if (!user || !user.isActive) {
      const response = NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
      return { error: addSecurityHeaders(response) };
    }
    
    return { user: { id: user.id, email: user.email, role: user.role } };
  } catch (error) {
    const response = NextResponse.json(
      { error: 'Invalid token' },
      { status: 401 }
    );
    return { error: addSecurityHeaders(response) };
  }
}

/**
 * Authorization middleware
 */
export function authorizationMiddleware(
  user: any, 
  requiredRoles: string[] = []
): NextResponse | null {
  if (requiredRoles.length === 0) {
    return null; // No specific roles required
  }
  
  if (!requiredRoles.includes(user.role)) {
    const response = NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
    return addSecurityHeaders(response);
  }
  
  return null;
}

/**
 * Input validation middleware
 */
export function validationMiddleware<T>(
  request: NextRequest,
  schema: any,
  source: 'body' | 'query' | 'params' = 'body'
): Promise<{ data: T; error?: NextResponse } | { error: NextResponse }> {
  return new Promise(async (resolve) => {
    try {
      let data: any;
      
      switch (source) {
        case 'body':
          data = await request.json();
          break;
        case 'query':
          data = Object.fromEntries(request.nextUrl.searchParams.entries());
          break;
        case 'params':
          // This would need to be passed from the route handler
          data = {};
          break;
        default:
          data = {};
      }
      
      const validation = validateInput(schema, data);
      
      if (!validation.success) {
        const response = NextResponse.json(
          { 
            error: 'Validation failed',
            details: validation.errors 
          },
          { status: 400 }
        );
        resolve({ error: addSecurityHeaders(response) });
        return;
      }
      
      resolve({ data: validation.data as T });
    } catch (error) {
      const response = NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
      resolve({ error: addSecurityHeaders(response) });
    }
  });
}

/**
 * Login attempt tracking middleware
 */
export function loginAttemptMiddleware(
  request: NextRequest,
  success: boolean,
  email?: string
): NextResponse | null {
  const ip = getClientIP(request);
  const identifier = email || ip;
  
  const attempt = trackLoginAttempt(identifier, success);
  
  if (!attempt.allowed) {
    const response = NextResponse.json(
      { 
        error: 'Account temporarily locked due to too many failed login attempts',
        lockedUntil: attempt.lockedUntil 
      },
      { status: 429 }
    );
    return addSecurityHeaders(response);
  }
  
  return null;
}

/**
 * Generate JWT token
 */
export function generateTokens(userId: string): { accessToken: string; refreshToken: string } {
  const accessToken = jwt.sign(
    { userId, type: 'access' },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
  
  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    JWT_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRES_IN }
  );
  
  return { accessToken, refreshToken };
}

/**
 * Verify refresh token
 */
export function verifyRefreshToken(token: string): { userId: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    
    if (decoded.type !== 'refresh') {
      return null;
    }
    
    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
}

/**
 * Comprehensive security middleware wrapper
 */
export function withSecurity(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: {
    requireAuth?: boolean;
    requiredRoles?: string[];
    skipCSRF?: boolean;
    skipRateLimit?: boolean;
    validationSchema?: any;
    validationSource?: 'body' | 'query' | 'params';
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Rate limiting
      if (!options.skipRateLimit) {
        const rateLimitResponse = rateLimitMiddleware(request);
        if (rateLimitResponse) return rateLimitResponse;
      }
      
      // CSRF protection
      if (!options.skipCSRF) {
        const csrfResponse = csrfMiddleware(request);
        if (csrfResponse) return csrfResponse;
      }
      
      // Authentication
      let user: any = null;
      if (options.requireAuth) {
        const authResult = authMiddleware(request);
        if ('error' in authResult && !authResult.user) {
          return authResult.error;
        }
        user = authResult.user;
        
        // Authorization
        if (options.requiredRoles && options.requiredRoles.length > 0) {
          const authzResponse = authorizationMiddleware(user, options.requiredRoles);
          if (authzResponse) return authzResponse;
        }
      }
      
      // Input validation
      if (options.validationSchema) {
        const validationResult = await validationMiddleware(
          request,
          options.validationSchema,
          options.validationSource
        );
        if ('error' in validationResult && !validationResult.data) {
          return validationResult.error;
        }
        // Attach validated data to request
        (request as any).validatedData = validationResult.data;
      }
      
      // Attach user to request
      if (user) {
        (request as AuthenticatedRequest).user = user;
      }
      
      // Call the actual handler
      const response = await handler(request as AuthenticatedRequest);
      
      // Add security headers to response
      return addSecurityHeaders(response);
      
    } catch (error) {
      console.error('Security middleware error:', error);
      const response = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
      return addSecurityHeaders(response);
    }
  };
}

/**
 * Audit logging function
 */
export function logAuditEvent(
  userId: string | null,
  action: string,
  resourceType: string,
  resourceId?: string,
  oldValues?: any,
  newValues?: any,
  request?: NextRequest
): void {
  try {
    // In a real application, this would write to the audit_logs table
    const auditLog = {
      userId,
      action,
      resourceType,
      resourceId,
      oldValues: oldValues ? JSON.stringify(oldValues) : null,
      newValues: newValues ? JSON.stringify(newValues) : null,
      ipAddress: request ? getClientIP(request) : null,
      userAgent: request ? request.headers.get('user-agent') : null,
      timestamp: new Date().toISOString(),
    };
    
    console.log('Audit Log:', auditLog);
    // TODO: Implement actual database logging
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
}
