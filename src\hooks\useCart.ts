import { useCallback } from 'react';
import { useCartStore } from '../stores/cartStore';
import { useAuthStore } from '../stores/authStore';
import { CartService } from '../services/CartService';
import { Product } from '../types';

export interface UseCartReturn {
  items: any[];
  itemCount: number;
  totalPrice: number;
  isLoading: boolean;
  error: string | null;
  addItem: (product: Product, quantity?: number) => Promise<void>;
  removeItem: (productId: string) => Promise<void>;
  updateQuantity: (productId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
  getItemQuantity: (productId: string) => number;
  isInCart: (productId: string) => boolean;
}

/**
 * Hook for cart management
 * Provides cart state and methods for managing cart items
 */
export function useCart(): UseCartReturn {
  const {
    items,
    itemCount,
    totalPrice,
    isLoading,
    error,
    addItem: addCartItem,
    removeItem: removeCartItem,
    updateQuantity: updateCartQuantity,
    clearCart: clearCartItems,
    setItems,
    setLoading,
    setError
  } = useCartStore();

  const { user } = useAuthStore();

  // Add item to cart
  const addItem = useCallback(async (product: Product, quantity: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        await CartService.addToCart(user.id, product.id, quantity);
      }
      
      // Update local state
      addCartItem(product, quantity);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart';
      setError(errorMessage);
      console.error('Add to cart error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, addCartItem, setLoading, setError]);

  // Remove item from cart
  const removeItem = useCallback(async (productId: string) => {
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        await CartService.removeCartItem(user.id, productId);
      }
      
      // Update local state
      removeCartItem(productId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove item from cart';
      setError(errorMessage);
      console.error('Remove from cart error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, removeCartItem, setLoading, setError]);

  // Update item quantity
  const updateQuantity = useCallback(async (productId: string, quantity: number) => {
    if (quantity <= 0) {
      await removeItem(productId);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        await CartService.updateCartItemQuantity(user.id, productId, quantity);
      }
      
      // Update local state
      updateCartQuantity(productId, quantity);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update item quantity';
      setError(errorMessage);
      console.error('Update cart quantity error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, updateCartQuantity, removeItem, setLoading, setError]);

  // Clear cart
  const clearCart = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // For authenticated users, sync with server
        await CartService.clearCart(user.id);
      }
      
      // Update local state
      clearCartItems();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to clear cart';
      setError(errorMessage);
      console.error('Clear cart error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, clearCartItems, setLoading, setError]);

  // Refresh cart from server
  const refreshCart = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const cartItems = await CartService.getCartItems(user.id);
      setItems(cartItems);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh cart';
      setError(errorMessage);
      console.error('Refresh cart error:', error);
    } finally {
      setLoading(false);
    }
  }, [user, setItems, setLoading, setError]);

  // Get quantity of specific item in cart
  const getItemQuantity = useCallback((productId: string): number => {
    const item = items.find(item => item.product.id === productId);
    return item ? item.quantity : 0;
  }, [items]);

  // Check if item is in cart
  const isInCart = useCallback((productId: string): boolean => {
    return items.some(item => item.product.id === productId);
  }, [items]);

  return {
    items,
    itemCount,
    totalPrice,
    isLoading,
    error,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    refreshCart,
    getItemQuantity,
    isInCart
  };
}
