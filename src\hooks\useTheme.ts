import { useCallback, useEffect } from 'react';
import { useTheme as useNextTheme } from 'next-themes';
import { useThemeStore } from '../stores/themeStore';

export type Theme = 'light' | 'dark' | 'system';

export interface UseThemeReturn {
  theme: Theme;
  resolvedTheme: 'light' | 'dark';
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
  isLoading: boolean;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  setLightTheme: () => void;
  setDarkTheme: () => void;
  setSystemTheme: () => void;
}

/**
 * Hook for theme management
 * Provides theme state and methods for switching between light/dark modes
 */
export function useTheme(): UseThemeReturn {
  const { theme, setTheme: setNextTheme, resolvedTheme } = useNextTheme();
  const { isLoading, setLoading } = useThemeStore();

  // Set theme
  const setTheme = useCallback((newTheme: Theme) => {
    setNextTheme(newTheme);
  }, [setNextTheme]);

  // Toggle between light and dark themes
  const toggleTheme = useCallback(() => {
    if (theme === 'system') {
      // If currently system, toggle to opposite of resolved theme
      setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
    } else {
      // Toggle between light and dark
      setTheme(theme === 'dark' ? 'light' : 'dark');
    }
  }, [theme, resolvedTheme, setTheme]);

  // Set light theme
  const setLightTheme = useCallback(() => {
    setTheme('light');
  }, [setTheme]);

  // Set dark theme
  const setDarkTheme = useCallback(() => {
    setTheme('dark');
  }, [setTheme]);

  // Set system theme
  const setSystemTheme = useCallback(() => {
    setTheme('system');
  }, [setTheme]);

  // Computed values
  const isDark = resolvedTheme === 'dark';
  const isLight = resolvedTheme === 'light';
  const isSystem = theme === 'system';

  // Handle loading state
  useEffect(() => {
    // Theme is loaded when we have a resolved theme
    if (resolvedTheme && isLoading) {
      setLoading(false);
    }
  }, [resolvedTheme, isLoading, setLoading]);

  // Apply theme-specific classes to body
  useEffect(() => {
    if (typeof document !== 'undefined' && resolvedTheme) {
      document.body.classList.toggle('dark', isDark);
      document.body.classList.toggle('light', isLight);
      
      // Update CSS custom properties for theme colors
      const root = document.documentElement;
      
      if (isDark) {
        root.style.setProperty('--color-scheme', 'dark');
        root.style.setProperty('--bg-primary', '#0f172a');
        root.style.setProperty('--bg-secondary', '#1e293b');
        root.style.setProperty('--text-primary', '#f8fafc');
        root.style.setProperty('--text-secondary', '#cbd5e1');
      } else {
        root.style.setProperty('--color-scheme', 'light');
        root.style.setProperty('--bg-primary', '#ffffff');
        root.style.setProperty('--bg-secondary', '#f8fafc');
        root.style.setProperty('--text-primary', '#0f172a');
        root.style.setProperty('--text-secondary', '#475569');
      }
    }
  }, [isDark, isLight, resolvedTheme]);

  // Store theme preference
  useEffect(() => {
    if (typeof window !== 'undefined' && theme) {
      localStorage.setItem('preferred-theme', theme);
    }
  }, [theme]);

  return {
    theme: theme as Theme,
    resolvedTheme: resolvedTheme as 'light' | 'dark',
    isDark,
    isLight,
    isSystem,
    isLoading,
    setTheme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme
  };
}
