"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_sections_FeaturedProducts_tsx";
exports.ids = ["_ssr_src_components_sections_FeaturedProducts_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/product/ProductCard.tsx":
/*!************************************************!*\
  !*** ./src/components/product/ProductCard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCard: () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/EnhancedImage */ \"(ssr)/./src/components/ui/EnhancedImage.tsx\");\n/* harmony import */ var _stores_cartStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../stores/cartStore */ \"(ssr)/./src/stores/cartStore.ts\");\n/* harmony import */ var _stores_wishlistStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../stores/wishlistStore */ \"(ssr)/./src/stores/wishlistStore.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/themeStore */ \"(ssr)/./src/stores/themeStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../translations */ \"(ssr)/./src/translations/index.ts\");\n/* harmony import */ var _ui_animations_HoverAnimation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../ui/animations/HoverAnimation */ \"(ssr)/./src/components/ui/animations/HoverAnimation.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductCard auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCard({ product, index = 0, className = '', showQuickView = true, showAddToCart = true, showWishlist = true, onQuickView, onAddToCart, onToggleWishlist }) {\n    const { t, currentLanguage } = (0,_translations__WEBPACK_IMPORTED_MODULE_10__.useTranslation)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore)();\n    const cartStore = (0,_stores_cartStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore)();\n    const wishlistStore = (0,_stores_wishlistStore__WEBPACK_IMPORTED_MODULE_7__.useWishlistStore)();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAddToCart = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n        if (onAddToCart) {\n            onAddToCart(product);\n        } else {\n            cartStore.addItem({\n                id: product.id,\n                name: product.name,\n                name_ar: product.name_ar,\n                price: product.price,\n                image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',\n                quantity: 1\n            });\n        }\n    };\n    const handleToggleWishlist = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n        if (onToggleWishlist) {\n            onToggleWishlist(product);\n        } else {\n            if (wishlistStore.isInWishlist(product.id)) {\n                wishlistStore.removeItem(product.id);\n            } else {\n                wishlistStore.addItem(product);\n            }\n        }\n    };\n    const handleQuickView = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (onQuickView) {\n            onQuickView(product);\n        }\n    };\n    // Fallback image if the product image fails to load\n    const fallbackImage = `/images/product-placeholder-${isDarkMode ? 'dark' : 'light'}.svg`;\n    // Use the first product image or fallback if there are no images\n    const productImage = imageError || !product.images || product.images.length === 0 ? fallbackImage : product.images[0];\n    // تحديد ما إذا كان المنتج في المخزون\n    const isInStock = product.stock > 0;\n    // حساب نسبة الخصم إذا كان هناك سعر مقارنة\n    const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price ? Math.round((1 - product.price / product.compareAtPrice) * 100) : product.discount || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_animations_HoverAnimation__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n        animation: \"lift\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group flex flex-col h-full overflow-hidden rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-slate-800\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: `/shop/${product.slug}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-48 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_5__.EnhancedImage, {\n                                    src: productImage,\n                                    alt: product.name,\n                                    fill: true,\n                                    objectFit: \"cover\",\n                                    progressive: true,\n                                    placeholder: \"shimmer\",\n                                    className: \"transition-transform duration-500 group-hover:scale-105\",\n                                    sizes: \"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw\",\n                                    priority: index < 4,\n                                    onError: ()=>setImageError(true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 flex flex-col gap-1 z-10\",\n                            children: [\n                                product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-blue-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? 'جديد' : 'New'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                discountPercentage > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-red-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                !isInStock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-gray-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-amber-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? 'مميز' : 'Featured'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 flex flex-col gap-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            children: [\n                                showWishlist && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"icon\",\n                                    size: \"sm\",\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"p-1.5 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110\", wishlistStore.isInWishlist(product.id) ? \"bg-primary-500 text-white\" : \"bg-white text-slate-700 dark:bg-slate-700 dark:text-white\"),\n                                    onClick: handleToggleWishlist,\n                                    \"aria-label\": t('shop.addToWishlist'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"h-4 w-4\", wishlistStore.isInWishlist(product.id) && \"fill-current\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                showQuickView && onQuickView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"icon\",\n                                    size: \"sm\",\n                                    className: \"p-1.5 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110\",\n                                    onClick: handleQuickView,\n                                    \"aria-label\": t('shop.quickView'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 flex flex-col flex-grow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                product.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full\",\n                                    children: product.category\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-slate-500 dark:text-slate-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: `h-4 w-4 text-yellow-400 ${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                product.rating?.toFixed(1) ?? 'N/A',\n                                                \"(\",\n                                                product.reviewCount ?? 0,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: `/${currentLanguage}/shop/${product.slug}`,\n                            className: \"block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1\",\n                                children: currentLanguage === 'ar' ? product.name_ar || product.name : product.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm\",\n                            children: currentLanguage === 'ar' ? product.description_ar || product.description : product.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3 mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-baseline gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-slate-900 dark:text-white\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(product.price)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.compareAtPrice && product.compareAtPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-500 line-through\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(product.compareAtPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium\",\n                                    children: isInStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 dark:text-green-400\",\n                                        children: currentLanguage === 'ar' ? 'متوفر' : 'In Stock'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 dark:text-red-400\",\n                                        children: currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                showAddToCart && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"primary\",\n                                    size: \"sm\",\n                                    className: \"flex-1 rounded-md\",\n                                    onClick: handleAddToCart,\n                                    disabled: !isInStock,\n                                    \"aria-label\": t('shop.addToCart'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: `h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                showQuickView && onQuickView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"rounded-md\",\n                                    onClick: handleQuickView,\n                                    \"aria-label\": t('shop.quickView'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/product/ProductCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/FeaturedProducts.tsx":
/*!******************************************************!*\
  !*** ./src/components/sections/FeaturedProducts.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../stores/languageStore */ \"(ssr)/./src/stores/languageStore.ts\");\n/* harmony import */ var _product_ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../product/ProductCard */ \"(ssr)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _services_ProductService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/ProductService */ \"(ssr)/./src/services/ProductService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst FeaturedProducts = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function FeaturedProducts() {\n    const { language, t } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore)();\n    const isRTL = language === 'ar';\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeaturedProducts.FeaturedProducts.useEffect\": ()=>{\n            const loadFeaturedProducts = {\n                \"FeaturedProducts.FeaturedProducts.useEffect.loadFeaturedProducts\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const featuredProducts = await _services_ProductService__WEBPACK_IMPORTED_MODULE_6__.ProductService.getFeaturedProducts(8);\n                        setProducts(featuredProducts);\n                    } catch (error) {\n                        console.error('Failed to load featured products:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FeaturedProducts.FeaturedProducts.useEffect.loadFeaturedProducts\"];\n            loadFeaturedProducts();\n        }\n    }[\"FeaturedProducts.FeaturedProducts.useEffect\"], []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: 'easeOut'\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: Array.from({\n                            length: 8\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-square bg-gray-200 rounded-lg mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-2/3 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('featuredProducts.title', 'Featured Products')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t('featuredProducts.description', 'Discover our handpicked selection of premium products, carefully chosen for their quality and value.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_ProductCard__WEBPACK_IMPORTED_MODULE_4__.ProductCard, {\n                                product: product,\n                                showQuickView: true,\n                                showWishlist: true,\n                                className: \"h-full hover:shadow-lg transition-shadow duration-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this)\n                        }, product.id, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        asChild: true,\n                        size: \"lg\",\n                        variant: \"outline\",\n                        className: \"border-2 border-blue-500 text-blue-600 hover:bg-blue-500 hover:text-white px-8 py-3 rounded-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop\",\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                t('featuredProducts.viewAll', 'View All Products'),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: `w-5 h-5 ${isRTL ? 'rotate-180' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: \"100%\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.authentic', 'Authentic Products')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: \"24/7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.support', 'Customer Support')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: \"Free\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.shipping', 'Fast Shipping')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-orange-600\",\n                                    children: \"30\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.returns', 'Day Returns')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeaturedProducts);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/FeaturedProducts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/EnhancedImage.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/EnhancedImage.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedImage: () => (/* binding */ EnhancedImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../stores/themeStore */ \"(ssr)/./src/stores/themeStore.ts\");\n/* __next_internal_client_entry_do_not_use__ EnhancedImage auto */ \n\n\n\n\nfunction EnhancedImage({ src, alt, width, height, className, containerClassName, priority = false, effect = 'none', rounded = 'md', aspectRatio = 'auto', objectFit = 'cover', quality = 85, loading = 'lazy', fill = false, sizes, onClick, progressive = false, placeholder = 'blur', onError }) {\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_4__.useThemeStore)();\n    // تعيين نسبة العرض إلى الارتفاع\n    const getAspectRatio = ()=>{\n        switch(aspectRatio){\n            case '1:1':\n                return 'aspect-square';\n            case '4:3':\n                return 'aspect-[4/3]';\n            case '16:9':\n                return 'aspect-[16/9]';\n            case '21:9':\n                return 'aspect-[21/9]';\n            default:\n                return '';\n        }\n    };\n    // تعيين حجم التقريب\n    const getRoundedSize = ()=>{\n        switch(rounded){\n            case 'none':\n                return '';\n            case 'sm':\n                return 'rounded-sm';\n            case 'md':\n                return 'rounded-md';\n            case 'lg':\n                return 'rounded-lg';\n            case 'full':\n                return 'rounded-full';\n            default:\n                return 'rounded-md';\n        }\n    };\n    // تعيين نوع التأثير\n    const getEffectStyles = ()=>{\n        switch(effect){\n            case 'zoom':\n                return 'group-hover:scale-110 transition-transform duration-500';\n            case 'fade':\n                return 'opacity-90 group-hover:opacity-100 transition-opacity duration-300';\n            case 'blur':\n                return 'blur-[2px] group-hover:blur-0 transition-all duration-300';\n            case 'tilt':\n                return '';\n            case 'shine':\n                return 'relative';\n            default:\n                return '';\n        }\n    };\n    // تعيين نوع الاحتواء\n    const getObjectFit = ()=>{\n        switch(objectFit){\n            case 'cover':\n                return 'object-cover';\n            case 'contain':\n                return 'object-contain';\n            case 'fill':\n                return 'object-fill';\n            case 'none':\n                return 'object-none';\n            default:\n                return 'object-cover';\n        }\n    };\n    // تأثير العنصر النائب\n    const renderPlaceholder = ()=>{\n        if (!isLoaded) {\n            switch(placeholder){\n                case 'blur':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this);\n                case 'shimmer':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this);\n                default:\n                    return null;\n            }\n        }\n        return null;\n    };\n    // تحسين تجربة المستخدم عند تحميل الصور\n    const handleImageLoad = ()=>{\n        setIsLoaded(true);\n        // تأخير إظهار الصورة قليلاً لتجنب الوميض\n        if (progressive) {\n            setTimeout(()=>{\n                const imageElement = document.getElementById(`enhanced-image-${src.split('/').pop()}`);\n                if (imageElement) {\n                    imageElement.classList.remove('opacity-0');\n                    imageElement.classList.add('opacity-100');\n                }\n            }, 50);\n        }\n    };\n    // معالجة خطأ تحميل الصورة\n    const handleImageError = ()=>{\n        setHasError(true);\n        if (onError) onError();\n        console.warn(`Failed to load image: ${src}`);\n    };\n    // استخدام صورة احتياطية في حالة الخطأ\n    const fallbackSrc = hasError ? `/images/placeholder-${isDarkMode ? 'dark' : 'light'}.svg` : src;\n    // تحميل الصورة مسبقًا للتحقق من صحتها\n    const preloadImage = (url)=>{\n        if (false) {}\n    };\n    // تحميل الصورة مسبقًا إذا كانت ذات أولوية\n    if (priority && \"undefined\" !== 'undefined') {}\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative overflow-hidden group', getAspectRatio(), getRoundedSize(), isDarkMode ? 'bg-slate-800' : 'bg-slate-100', onClick && 'cursor-pointer', containerClassName),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: `enhanced-image-${src.split('/').pop()}`,\n                src: fallbackSrc,\n                alt: alt,\n                width: width,\n                height: height,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getObjectFit(), getEffectStyles(), 'transition-all duration-300', !isLoaded && progressive ? 'opacity-0' : 'opacity-100', className),\n                priority: priority,\n                quality: quality,\n                loading: priority ? 'eager' : loading,\n                fill: fill,\n                sizes: sizes || (fill ? '100vw' : undefined),\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                unoptimized: hasError\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            renderPlaceholder()\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/EnhancedImage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/animations/HoverAnimation.tsx":
/*!*********************************************************!*\
  !*** ./src/components/ui/animations/HoverAnimation.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverAnimation: () => (/* binding */ HoverAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ HoverAnimation auto */ \n\n\n\nfunction HoverAnimation({ children, className, animation = 'scale', scale = 1.05, duration = 0.3, disabled = false, as = 'div', onClick, ...props }) {\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HoverAnimation.useEffect\": ()=>{\n            setIsMounted(true);\n            setIsMobile(window.innerWidth < 768);\n            const handleResize = {\n                \"HoverAnimation.useEffect.handleResize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"HoverAnimation.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"HoverAnimation.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"HoverAnimation.useEffect\"];\n        }\n    }[\"HoverAnimation.useEffect\"], []);\n    // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true\n    const isAnimationDisabled = disabled || isMobile && typeof document !== 'undefined' && document.documentElement.classList.contains('mobile-device');\n    // تحسين الأداء عن طريق استخدام will-change للعناصر التي تحتاج إلى تحسين أداء الرسوم المتحركة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HoverAnimation.useEffect\": ()=>{\n            if (isMounted && !isAnimationDisabled) {\n                const needsHardwareAcceleration = [\n                    'lift',\n                    'rotate',\n                    'tilt',\n                    'pulse',\n                    'bounce'\n                ].includes(animation);\n                if (needsHardwareAcceleration) {\n                    const element = document.querySelector(`.animation-${animation}`);\n                    if (element) {\n                        element.classList.add('will-change-transform');\n                    }\n                }\n            }\n        }\n    }[\"HoverAnimation.useEffect\"], [\n        isMounted,\n        isAnimationDisabled,\n        animation\n    ]);\n    // تحديد متغيرات الرسوم المتحركة بناءً على النوع\n    const getVariants = ()=>{\n        const baseTransition = {\n            type: 'tween',\n            duration\n        };\n        switch(animation){\n            case 'scale':\n                return {\n                    initial: {},\n                    hover: {\n                        scale,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        scale: scale * 0.95,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'lift':\n                return {\n                    initial: {},\n                    hover: {\n                        y: -8,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        y: -4,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'glow':\n                return {\n                    initial: {},\n                    hover: {\n                        boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n                        transition: baseTransition\n                    },\n                    tap: {\n                        boxShadow: '0 0 8px rgba(var(--color-primary-500), 0.3)',\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'rotate':\n                return {\n                    initial: {},\n                    hover: {\n                        rotate: 5,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        rotate: 2,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'pulse':\n                return {\n                    initial: {},\n                    hover: {\n                        scale: [\n                            1,\n                            scale,\n                            1,\n                            scale,\n                            1\n                        ],\n                        transition: {\n                            duration: duration * 2,\n                            repeat: Infinity,\n                            repeatType: 'loop'\n                        }\n                    },\n                    tap: {\n                        scale: scale * 0.95,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'bounce':\n                return {\n                    initial: {},\n                    hover: {\n                        y: [\n                            0,\n                            -10,\n                            0\n                        ],\n                        transition: {\n                            duration: duration * 1.5,\n                            repeat: Infinity,\n                            repeatType: 'loop'\n                        }\n                    },\n                    tap: {\n                        y: -5,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'tilt':\n                return {\n                    initial: {},\n                    hover: {\n                        rotateX: -10,\n                        rotateY: 10,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        rotateX: -5,\n                        rotateY: 5,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'shine':\n                return {\n                    initial: {},\n                    hover: {},\n                    tap: {\n                        scale: 0.98,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'border':\n                return {\n                    initial: {\n                        borderColor: 'rgba(var(--color-primary-500), 0)'\n                    },\n                    hover: {\n                        borderColor: 'rgba(var(--color-primary-500), 1)',\n                        borderWidth: '2px',\n                        transition: baseTransition\n                    },\n                    tap: {\n                        borderColor: 'rgba(var(--color-primary-600), 1)',\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'shadow':\n                return {\n                    initial: {\n                        boxShadow: '0 0 0 rgba(0, 0, 0, 0)'\n                    },\n                    hover: {\n                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',\n                        y: -2,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        boxShadow: '0 5px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',\n                        y: -1,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'fade':\n                return {\n                    initial: {\n                        opacity: 1\n                    },\n                    hover: {\n                        opacity: 0.8,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        opacity: 0.9,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            default:\n                return {\n                    initial: {},\n                    hover: {},\n                    tap: {}\n                };\n        }\n    };\n    const variants = getVariants();\n    const Component = framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion[as] || framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div;\n    // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة\n    if (isAnimationDisabled || !isMounted) {\n        const ElementType = as;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElementType, {\n            className: className,\n            onClick: onClick,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(className, `animation-${animation}`, animation === 'shine' && 'group overflow-hidden relative', animation === 'border' && 'border border-transparent', animation === 'shadow' && 'transition-shadow'),\n        initial: \"initial\",\n        whileHover: \"hover\",\n        whileTap: \"tap\",\n        variants: variants,\n        onClick: onClick,\n        ...props,\n        children: [\n            children,\n            animation === 'shine' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:translate-x-full\",\n                transition: {\n                    duration: duration * 2,\n                    ease: 'linear'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this),\n            animation === 'glow' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100\",\n                initial: {\n                    opacity: 0,\n                    boxShadow: '0 0 0 rgba(var(--color-primary-500), 0)'\n                },\n                whileHover: {\n                    opacity: 1,\n                    boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n                    transition: {\n                        duration\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/animations/HoverAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/ProductService.ts":
/*!****************************************!*\
  !*** ./src/services/ProductService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductService: () => (/* binding */ ProductService),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   filterProductsByCategory: () => (/* binding */ filterProductsByCategory),\n/* harmony export */   getAllProducts: () => (/* binding */ getAllProducts),\n/* harmony export */   getFeaturedProducts: () => (/* binding */ getFeaturedProducts),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductBySlug: () => (/* binding */ getProductBySlug),\n/* harmony export */   initializeDefaultProducts: () => (/* binding */ initializeDefaultProducts),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_sqlite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/sqlite */ \"(ssr)/./src/lib/sqlite.ts\");\n/* harmony import */ var _data_products__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/products */ \"(ssr)/./src/data/products.ts\");\n/**\n * خدمة إدارة المنتجات باستخدام SQLite\n */ \n\n// Initialize the db instance\nconst sqliteDB = (0,_lib_sqlite__WEBPACK_IMPORTED_MODULE_0__.getDbInstance)();\n// مفتاح التخزين المحلي\nconst LOCAL_PRODUCTS_KEY = 'local-products';\n/**\n * الحصول على جميع المنتجات\n */ async function getAllProducts() {\n    try {\n        // محاولة الحصول على المنتجات من SQLite\n        const products = await sqliteDB.getProducts();\n        // إذا لم تكن هناك منتجات، قم بتهيئة المنتجات الافتراضية\n        if ((await sqliteDB.getProducts()).length === 0) {\n            await initializeDefaultProducts();\n            return await sqliteDB.getProducts();\n        }\n        return products;\n    } catch (error) {\n        console.error('Error getting products:', error);\n        return [];\n    }\n}\n/**\n * الحصول على منتج بواسطة المعرف\n */ async function getProductById(id) {\n    try {\n        const products = await sqliteDB.getProducts();\n        return products.find((p)=>p.id.toString() === id.toString()) || null;\n    } catch (error) {\n        console.error(`Error getting product by ID ${id}:`, error);\n        return null;\n    }\n}\n/**\n * الحصول على منتج بواسطة الرابط\n */ async function getProductBySlug(slug) {\n    try {\n        const products = await sqliteDB.getProducts();\n        return products.find((p)=>p.slug === slug) || null;\n    } catch (error) {\n        console.error(`Error getting product by slug ${slug}:`, error);\n        return null;\n    }\n}\n/**\n * إنشاء منتج جديد\n */ async function createProduct(productData) {\n    try {\n        const products = await sqliteDB.getProducts();\n        // إنشاء معرف فريد للمنتج الجديد\n        const id = productData.id || `product-${Date.now().toString()}`;\n        // دمج بيانات المنتج مع القيم الافتراضية\n        const newProduct = {\n            id: id,\n            name: productData.name || \"منتج جديد\",\n            slug: productData.slug || `new-product-${Date.now().toString()}`,\n            description: productData.description || \"وصف المنتج الجديد\",\n            price: productData.price || 0,\n            compareAtPrice: productData.compareAtPrice || undefined,\n            category: productData.category || \"غير مصنف\",\n            images: productData.images || [],\n            tags: productData.tags || [],\n            stock: productData.stock || 0,\n            featured: productData.featured || false,\n            specifications: productData.specifications || {},\n            reviews: productData.reviews || [],\n            rating: productData.rating || 0,\n            reviewCount: productData.reviewCount || 0,\n            relatedProducts: productData.relatedProducts || [],\n            createdAt: new Date().toISOString()\n        };\n        // إضافة المنتج الجديد إلى المنتجات - relies on createProduct to persist\n        const createdProduct = await sqliteDB.createProduct(newProduct);\n        if (!createdProduct) {\n            throw new Error('Failed to create product in DB');\n        }\n        return createdProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('فشل إنشاء المنتج');\n    }\n}\n/**\n * تحديث منتج\n */ async function updateProduct(id, productData) {\n    try {\n        const products = await sqliteDB.getProducts();\n        const index = products.findIndex((p)=>p.id.toString() === id.toString());\n        if (index === -1) {\n            return null;\n        }\n        const updatedProductData = {\n            ...products[index],\n            ...productData\n        };\n        // تحديث المنتج في المنتجات - relies on updateProduct to persist\n        const updatedProduct = await sqliteDB.updateProduct(id.toString(), updatedProductData);\n        if (!updatedProduct) {\n            return null;\n        }\n        return updatedProduct;\n    } catch (error) {\n        console.error(`Error updating product ${id}:`, error);\n        return null;\n    }\n}\n/**\n * حذف منتج\n */ async function deleteProduct(id) {\n    try {\n        const products = await sqliteDB.getProducts();\n        const productExists = products.some((p)=>p.id.toString() === id.toString());\n        if (!productExists) {\n            return false; // Product not found, cannot delete\n        }\n        // حذف المنتج من المنتجات - relies on deleteProduct to persist\n        const success = await sqliteDB.deleteProduct(id.toString());\n        return success;\n    } catch (error) {\n        console.error(`Error deleting product ${id}:`, error);\n        return false;\n    }\n}\n/**\n * البحث عن منتجات\n */ async function searchProducts(query) {\n    try {\n        const products = await sqliteDB.getProducts();\n        if (!query) {\n            return products;\n        }\n        const lowerCaseQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerCaseQuery) || product.description.toLowerCase().includes(lowerCaseQuery) || product.tags && product.tags.some((tag)=>tag.toLowerCase().includes(lowerCaseQuery)));\n    } catch (error) {\n        console.error('Error searching products:', error);\n        return [];\n    }\n}\n/**\n * تصفية المنتجات حسب الفئة\n */ async function filterProductsByCategory(category) {\n    try {\n        const products = await sqliteDB.getProducts();\n        if (!category) {\n            return products;\n        }\n        return products.filter((p)=>p.category === category);\n    } catch (error) {\n        console.error(`Error filtering products by category ${category}:`, error);\n        return [];\n    }\n}\n/**\n * الحصول على المنتجات المميزة\n */ async function getFeaturedProducts() {\n    try {\n        const products = await sqliteDB.getProducts();\n        return products.filter((p)=>p.featured);\n    } catch (error) {\n        console.error('Error getting featured products:', error);\n        return [];\n    }\n}\n/**\n * تهيئة المنتجات الافتراضية\n */ async function initializeDefaultProducts() {\n    try {\n        const currentProducts = await sqliteDB.getProducts();\n        if (currentProducts.length === 0) {\n            console.log(\"[ProductService] Initializing default products as DB is empty.\");\n            for (const product of _data_products__WEBPACK_IMPORTED_MODULE_1__.products){\n                await sqliteDB.createProduct(product); // createProduct handles saving\n            }\n        }\n    } catch (error) {\n        console.error('Error initializing default products:', error);\n    }\n}\n// تهيئة المنتجات الافتراضية عند تحميل الخدمة\nif (false) {}\n// Export as a class for easier usage\nclass ProductService {\n    static async getAllProducts() {\n        return getAllProducts();\n    }\n    static async getProductById(id) {\n        return getProductById(id);\n    }\n    static async getProductBySlug(slug) {\n        return getProductBySlug(slug);\n    }\n    static async createProduct(productData) {\n        return createProduct(productData);\n    }\n    static async updateProduct(id, productData) {\n        return updateProduct(id, productData);\n    }\n    static async deleteProduct(id) {\n        return deleteProduct(id);\n    }\n    static async searchProducts(query) {\n        return searchProducts(query);\n    }\n    static async filterProductsByCategory(category) {\n        return filterProductsByCategory(category);\n    }\n    static async getFeaturedProducts(limit) {\n        const products = await getFeaturedProducts();\n        return limit ? products.slice(0, limit) : products;\n    }\n    static async initializeDefaultProducts() {\n        return initializeDefaultProducts();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ProductService.ts\n");

/***/ })

};
;