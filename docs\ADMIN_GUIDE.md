# FastCart E-Commerce Platform Administrator Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Admin Dashboard Overview](#admin-dashboard-overview)
3. [User Management](#user-management)
   - [Managing Customer Accounts](#managing-customer-accounts)
   - [Managing Vendor Accounts](#managing-vendor-accounts)
   - [Managing Staff Accounts](#managing-staff-accounts)
   - [Role-Based Access Control](#role-based-access-control)
4. [Product Management](#product-management)
   - [Adding Products](#adding-products)
   - [Editing Products](#editing-products)
   - [Managing Categories](#managing-categories)
   - [Product Variants](#product-variants)
   - [Product Gallery](#product-gallery)
   - [Product Import/Export](#product-importexport)
5. [Inventory Management](#inventory-management)
   - [Stock Tracking](#stock-tracking)
   - [Low Stock Alerts](#low-stock-alerts)
   - [Stock Adjustments](#stock-adjustments)
   - [Inventory Reports](#inventory-reports)
6. [Order Management](#order-management)
   - [Order Processing Workflow](#order-processing-workflow)
   - [Order Status Updates](#order-status-updates)
   - [Order Cancellation](#order-cancellation)
   - [Returns & Refunds](#returns--refunds)
   - [Order Reporting](#order-reporting)
7. [B2B Management](#b2b-management)
   - [RFQ Management](#rfq-management)
   - [Wholesale Pricing](#wholesale-pricing)
   - [Production Line Management](#production-line-management)
   - [Clearance Deal Setup](#clearance-deal-setup)
8. [Service Management](#service-management)
   - [Service Configuration](#service-configuration)
   - [Service Booking Administration](#service-booking-administration)
   - [Service Provider Management](#service-provider-management)
9. [Content Management](#content-management)
   - [Homepage Management](#homepage-management)
   - [Blog Management](#blog-management)
   - [Banner & Promotional Content](#banner--promotional-content)
   - [Email Templates](#email-templates)
10. [Analytics & Reporting](#analytics--reporting)
    - [Sales Analytics](#sales-analytics)
    - [Customer Analytics](#customer-analytics)
    - [Inventory Analytics](#inventory-analytics)
    - [Marketing Performance](#marketing-performance)
    - [B2B Analytics](#b2b-analytics)
    - [Export Reports](#export-reports)
11. [System Configuration](#system-configuration)
    - [Payment Gateway Setup](#payment-gateway-setup)
    - [Shipping Configuration](#shipping-configuration)
    - [Tax Settings](#tax-settings)
    - [Email Configuration](#email-configuration)
    - [System Maintenance](#system-maintenance)
12. [Security Management](#security-management)
    - [Security Settings](#security-settings)
    - [Access Logs](#access-logs)
    - [Security Alerts](#security-alerts)
13. [Advanced Features](#advanced-features)
    - [API Management](#api-management)
    - [Caching Configuration](#caching-configuration)
    - [Performance Optimization](#performance-optimization)

## Introduction

This guide provides comprehensive instructions for managing the FastCart E-Commerce platform. It covers all administrative functions from user management to system configuration. The guide is designed for platform administrators, store managers, and technical staff who need to manage and maintain the FastCart system.

## Admin Dashboard Overview

The Admin Dashboard is your central control hub for managing all aspects of the FastCart platform.

To access the Admin Dashboard:

1. Log in with your admin credentials at `https://yourdomain.com/admin/`
2. The dashboard home screen displays key metrics and alerts:
   - Recent orders
   - Low stock alerts
   - Pending RFQs
   - Service bookings requiring attention
   - Revenue charts
   - User activity

The main navigation menu provides access to all administrative functions, organized by section.

## User Management

### Managing Customer Accounts

To manage customer accounts:

1. Navigate to **Users > Customers**
2. View a list of all customer accounts with search and filtering options
3. Click on a customer to view their profile, including:
   - Personal information
   - Order history
   - Wish lists
   - Communication history
   - Account status
4. Actions available:
   - Edit customer details
   - Reset password
   - Enable/disable account
   - View login activity
   - Add notes to customer profile

### Managing Vendor Accounts

To manage vendor accounts:

1. Navigate to **Users > Vendors**
2. View a list of all vendor accounts with search and filtering options
3. Click on a vendor to access their profile:
   - Business information
   - Product listings
   - Sales performance
   - Commission structure
   - Payment history
4. Actions available:
   - Approve/reject vendor applications
   - Edit vendor details
   - Manage commission rates
   - Enable/disable vendor account
   - Process vendor payouts

### Managing Staff Accounts

To manage staff accounts:

1. Navigate to **Users > Staff**
2. View a list of all staff accounts with roles and permissions
3. Click "Add Staff" to create a new staff account:
   - Enter staff details
   - Assign roles and permissions
   - Set account expiration (optional)
4. Edit existing staff accounts:
   - Update contact information
   - Modify role assignments
   - Reset passwords
   - Deactivate accounts

### Role-Based Access Control

To configure roles and permissions:

1. Navigate to **Users > Roles & Permissions**
2. View existing roles or create new roles
3. For each role, configure permissions across different modules:
   - Read access
   - Create access
   - Update access
   - Delete access
4. Assign roles to staff members as needed

## Product Management

### Adding Products

To add a new product:

1. Navigate to **Products > Add New**
2. Fill out the product information form:
   - Basic Information:
     - Product name
     - SKU
     - Description (short and long)
     - Category selection
     - Brand
   - Pricing Information:
     - Regular price
     - Sale price
     - Wholesale pricing (if applicable)
   - Inventory Information:
     - Stock quantity
     - Low stock threshold
     - Backorder settings
   - Shipping Information:
     - Weight
     - Dimensions
     - Shipping class
     - Free shipping option
   - Media:
     - Featured image
     - Gallery images
     - Videos (if applicable)
   - SEO:
     - Meta title
     - Meta description
     - Focus keywords
3. Click "Save" or "Publish" to add the product to your catalog

### Editing Products

To edit an existing product:

1. Navigate to **Products > All Products**
2. Find the product using search or filters
3. Click on the product name to open the edit screen
4. Make your changes to any section
5. Click "Update" to save changes

### Managing Categories

To manage product categories:

1. Navigate to **Products > Categories**
2. View existing categories in a hierarchical structure
3. To add a new category:
   - Click "Add New Category"
   - Enter category name
   - Enter slug (or let it generate automatically)
   - Select parent category (if applicable)
   - Add description
   - Upload category image
   - Configure display options
   - Click "Add Category"
4. To edit a category:
   - Click the category name
   - Make changes
   - Click "Update Category"
5. To delete a category:
   - Select the category
   - Click "Delete"
   - Confirm the deletion

### Product Variants

To manage product variants (e.g., sizes, colors):

1. While creating or editing a product, go to the "Variants" section
2. Click "Add Variant Attribute" (e.g., Size, Color)
3. For each attribute:
   - Enter attribute name (e.g., "Size")
   - Add attribute values (e.g., "Small", "Medium", "Large")
4. Configure variant-specific details:
   - Price adjustments
   - Stock levels
   - Images
5. Click "Save Variants"

### Product Gallery

To manage product images:

1. While editing a product, go to the "Gallery" section
2. Drag and drop images or click "Add Images"
3. Arrange images in the desired order
4. For each image:
   - Add alt text for accessibility
   - Set as featured image if desired
5. Click "Update Gallery"

### Product Import/Export

To bulk import products:

1. Navigate to **Products > Import/Export**
2. Download the product CSV template
3. Fill out the template with your product data
4. Upload the completed CSV file
5. Map CSV columns to product fields
6. Click "Import"
7. Review the import results

To export products:

1. Navigate to **Products > Import/Export**
2. Configure export options:
   - Select product categories
   - Choose product status
   - Select fields to export
3. Click "Export"
4. Download the generated CSV file

## Inventory Management

### Stock Tracking

To view current inventory:

1. Navigate to **Inventory > Stock Levels**
2. View a complete list of products with current stock levels
3. Use filters to view:
   - Low stock items
   - Out of stock items
   - Products by category
   - Products by vendor

### Low Stock Alerts

To configure low stock alerts:

1. Navigate to **Inventory > Alert Settings**
2. Set global low stock threshold or override at product level
3. Configure notification settings:
   - Email notifications
   - Dashboard alerts
   - Notification frequency
4. Set up automated reorder functionality (if enabled)

### Stock Adjustments

To make manual stock adjustments:

1. Navigate to **Inventory > Stock Adjustments**
2. Click "New Adjustment"
3. Select adjustment type:
   - Stock received
   - Damaged inventory
   - Stock count correction
   - Returns
4. Enter adjustment details:
   - Product selection
   - Quantity adjustment
   - Reason for adjustment
   - Reference number
5. Click "Submit Adjustment"

### Inventory Reports

To access inventory reports:

1. Navigate to **Inventory > Reports**
2. Select report type:
   - Inventory valuation
   - Stock movement history
   - Low stock report
   - Stock forecasting
3. Configure report parameters and date ranges
4. Generate and view the report
5. Export to CSV, Excel, or PDF

## Order Management

### Order Processing Workflow

The standard order processing workflow includes:

1. **New Order**: Order is placed but not yet processed
2. **Processing**: Order is being prepared
3. **Shipped**: Order has been shipped
4. **Fulfilled**: Order has been delivered
5. **Cancelled**: Order has been cancelled
6. **Refunded**: Order has been refunded

To manage this workflow:

1. Navigate to **Orders > All Orders**
2. View orders filtered by status
3. Click on an order to view details and manage its status

### Order Status Updates

To update order status:

1. Open the order details page
2. In the "Order Status" section, select the new status
3. Add any internal notes about the status change
4. If applicable, enter tracking information
5. Click "Update"
6. The system will automatically send appropriate notifications to the customer

### Order Cancellation

To cancel an order:

1. Open the order details page
2. Click "Cancel Order"
3. Select a cancellation reason
4. Choose whether to restock the items
5. Click "Confirm Cancellation"
6. The system will notify the customer and process any necessary refunds

### Returns & Refunds

To process a return:

1. Navigate to **Orders > Returns**
2. Click "New Return" or open an existing return request
3. Link to the original order
4. Select items being returned
5. Enter quantities and return reason
6. Inspect returned items when received
7. Process refund:
   - Full refund
   - Partial refund
   - Store credit
   - Replacement

### Order Reporting

To access order reports:

1. Navigate to **Reports > Orders**
2. Select report type:
   - Sales by period
   - Sales by product
   - Sales by category
   - Sales by vendor
   - Order status summary
3. Configure filters and date ranges
4. Generate and view the report
5. Export to CSV, Excel, or PDF

## B2B Management

### RFQ Management

To manage RFQs:

1. Navigate to **B2B > RFQ Management**
2. View all RFQs with status filters:
   - New
   - Processing
   - Quoted
   - Accepted
   - Declined
   - Cancelled
3. Click on an RFQ to view details:
   - Customer information
   - Product requirements
   - Quantities
   - Special requests
4. Process an RFQ:
   - Update status
   - Add internal notes
   - Prepare and send quote
   - Set expiration date for quote
   - Track RFQ to order conversion

### Wholesale Pricing

To manage wholesale pricing:

1. Navigate to **B2B > Wholesale Pricing**
2. Configure global volume discount rules:
   - Quantity thresholds
   - Percentage or fixed discounts
   - Customer group-specific pricing
3. Set product-specific wholesale pricing:
   - Select product
   - Configure tiered pricing structure
   - Set minimum order quantities
4. Import/export wholesale price lists

### Production Line Management

To manage production lines:

1. Navigate to **B2B > Production Lines**
2. View existing production lines
3. Add a new production line:
   - Production line name
   - Type (Manufacturing, Assembly, etc.)
   - Description
   - Specifications
   - Capacity
   - Lead times
   - Images and documentation
4. Manage production line availability
5. Configure custom quote request process

### Clearance Deal Setup

To create clearance deals:

1. Navigate to **B2B > Clearance Deals**
2. Click "New Clearance Deal"
3. Configure deal details:
   - Select products to include
   - Set clearance prices
   - Specify available quantities
   - Set clearance type (Overstock, Closeout, etc.)
   - Configure deal duration
   - Set visibility (public or business customers only)
4. Schedule deal publication
5. Set up email notifications for target customers

## Service Management

### Service Configuration

To configure service offerings:

1. Navigate to **Services > Configuration**
2. Add or edit service categories
3. For each service:
   - Set service name
   - Add description
   - Configure pricing structure
   - Set availability calendar
   - Define service duration
   - Upload images
   - Configure booking requirements

### Service Booking Administration

To manage service bookings:

1. Navigate to **Services > Bookings**
2. View all bookings with status filters
3. Click on a booking to view details
4. Take actions:
   - Confirm booking
   - Assign service provider
   - Update booking status
   - Reschedule booking
   - Cancel booking
5. Communicate with customer through the platform

### Service Provider Management

To manage service providers:

1. Navigate to **Services > Providers**
2. View all service providers
3. Add a new provider:
   - Provider name
   - Contact information
   - Service types offered
   - Availability schedule
   - Commission structure
4. Assign providers to specific bookings
5. Track provider performance

## Content Management

### Homepage Management

To manage the homepage:

1. Navigate to **Content > Homepage**
2. Configure homepage sections:
   - Hero banner
   - Featured products
   - Category showcases
   - Special offers
   - Testimonials
   - Blog posts
3. Change section order via drag-and-drop
4. Preview changes before publishing
5. Schedule homepage updates

### Blog Management

To manage the blog:

1. Navigate to **Content > Blog**
2. View all blog posts
3. Create a new post:
   - Title
   - Content (rich text editor)
   - Categories and tags
   - Featured image
   - Author
   - SEO settings
   - Publication date
4. Manage blog categories and tags
5. Moderate comments

### Banner & Promotional Content

To manage banners and promotions:

1. Navigate to **Content > Banners**
2. Create new banner:
   - Upload banner image
   - Set destination URL
   - Configure display options
   - Set display period
   - Target specific pages or categories
3. Create promotional popups:
   - Design popup content
   - Set trigger conditions
   - Configure display frequency
   - Set expiration

### Email Templates

To manage email templates:

1. Navigate to **Content > Email Templates**
2. View all system email templates:
   - Order confirmation
   - Shipping notification
   - Password reset
   - Welcome email
   - Abandoned cart reminder
   - RFQ responses
   - Service booking confirmations
3. Edit templates:
   - Update copy
   - Adjust layout
   - Configure personalization tokens
4. Test templates before saving
5. Track email open rates and engagement

## Analytics & Reporting

### Sales Analytics

To access sales analytics:

1. Navigate to **Analytics > Sales**
2. View key metrics:
   - Revenue
   - Average order value
   - Conversion rate
   - Best-selling products
   - Sales by channel
3. Configure date ranges and comparisons
4. View visual charts and detailed data tables
5. Set up automated reports

### Customer Analytics

To access customer analytics:

1. Navigate to **Analytics > Customers**
2. View key metrics:
   - New vs. returning customers
   - Customer lifetime value
   - Geographic distribution
   - Purchase frequency
   - Acquisition channels
3. Segment customers based on behavior
4. Identify high-value customers
5. Track retention rates

### Inventory Analytics

To access inventory analytics:

1. Navigate to **Analytics > Inventory**
2. View key metrics:
   - Stock turnover rate
   - Days of supply
   - Dead stock analysis
   - Seasonal trends
   - Reorder timing recommendations
3. Identify fast and slow-moving products
4. Forecast inventory needs

### Marketing Performance

To access marketing analytics:

1. Navigate to **Analytics > Marketing**
2. View campaign performance:
   - Email campaign metrics
   - Coupon usage
   - Referral sources
   - Banner performance
   - Search term analysis
3. Track ROI for marketing activities
4. A/B testing results for promotional content

### B2B Analytics

To access B2B analytics:

1. Navigate to **Analytics > B2B**
2. View key metrics:
   - RFQ conversion rate
   - Average B2B order value
   - Top B2B customers
   - Product line performance
   - RFQ response time
3. Track wholesale vs. retail sales ratio
4. Analyze production line utilization

### Export Reports

To export analytics data:

1. From any analytics page, click "Export"
2. Select export format:
   - CSV
   - Excel
   - PDF
3. Choose data points to include
4. Configure scheduled exports for recurring reports

## System Configuration

### Payment Gateway Setup

To configure payment gateways:

1. Navigate to **Settings > Payments**
2. View available payment gateways
3. For each gateway you want to enable:
   - Enter API credentials
   - Configure processing modes (test/live)
   - Set payment restrictions
   - Configure display order
4. Test payment processing before going live
5. View payment transaction logs

### Shipping Configuration

To configure shipping methods:

1. Navigate to **Settings > Shipping**
2. Set up shipping zones:
   - Define geographic regions
   - Configure shipping methods per zone
   - Set rates (flat, weight-based, or value-based)
3. Configure shipping carriers:
   - Enter API credentials
   - Set up live rate calculation
   - Configure package dimensions
4. Set shipping-related tax rules
5. Configure special shipping rules (free shipping thresholds, etc.)

### Tax Settings

To configure tax settings:

1. Navigate to **Settings > Taxes**
2. Configure tax calculation method:
   - Based on shipping address
   - Based on billing address
   - Based on store location
3. Set up tax zones:
   - Define geographic tax jurisdictions
   - Configure tax rates per jurisdiction
4. Set product tax classes
5. Configure digital goods taxation
6. Set up tax exemption rules

### Email Configuration

To configure email settings:

1. Navigate to **Settings > Email**
2. Configure SMTP settings:
   - SMTP server
   - Port
   - Authentication credentials
   - Encryption method
3. Set sender email and name
4. Configure email delivery schedule
5. Set up email testing

### System Maintenance

To perform system maintenance:

1. Navigate to **Settings > Maintenance**
2. Schedule database optimizations
3. Configure cache management
4. Schedule automated backups
5. Manage error logs
6. Configure maintenance mode

## Security Management

### Security Settings

To configure security settings:

1. Navigate to **Settings > Security**
2. Configure password policies:
   - Minimum length
   - Complexity requirements
   - Expiration policy
3. Set up two-factor authentication requirements
4. Configure login attempt limitations
5. Set up session timeout policies
6. Configure permission inheritance

### Access Logs

To view access logs:

1. Navigate to **Settings > Security > Access Logs**
2. View login attempts:
   - Successful logins
   - Failed login attempts
   - IP addresses
   - Date and time
   - Device information
3. Filter logs by user, date, or status
4. Export logs for compliance purposes

### Security Alerts

To configure security alerts:

1. Navigate to **Settings > Security > Alerts**
2. Set up notification rules for:
   - Multiple failed login attempts
   - Admin account login
   - Sensitive data access
   - Configuration changes
   - Permission changes
3. Configure alert delivery methods:
   - Email
   - SMS
   - Dashboard notification
4. Set alert severity levels

## Advanced Features

### API Management

To manage API access:

1. Navigate to **Settings > API**
2. View existing API keys
3. Generate new API keys:
   - Select permissions scope
   - Set usage limits
   - Configure IP restrictions
   - Set expiration
4. Revoke API keys
5. View API usage logs

### Caching Configuration

To configure caching:

1. Navigate to **Settings > Advanced > Caching**
2. Configure cache types:
   - Page cache
   - Block cache
   - Database query cache
   - API response cache
3. Set cache lifetime for different content types
4. Configure cache invalidation rules
5. Schedule cache warming
6. View cache hit/miss statistics

### Performance Optimization

To optimize performance:

1. Navigate to **Settings > Advanced > Performance**
2. Configure image optimization:
   - Auto-resize images
   - Compression quality
   - WebP conversion
   - Lazy loading
3. Configure JavaScript and CSS optimization:
   - Minification
   - Combination
   - Defer loading
4. Set up content delivery network (CDN)
5. Configure database query optimization
6. Schedule performance analysis reports
