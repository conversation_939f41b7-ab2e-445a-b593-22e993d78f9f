/**
 * Performance optimization utilities
 * Comprehensive performance enhancements for the e-commerce platform
 */

// Image optimization
export const imageOptimization = {
  // Preload critical images
  preloadCriticalImages: () => {
    if (typeof window !== 'undefined') {
      const criticalImages = [
        '/images/hero-dashboard.jpg',
        '/images/logo.svg',
        '/images/categories/electronics.jpg',
        '/images/categories/fashion.jpg',
      ];

      criticalImages.forEach((src) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    }
  },

  // Lazy load images with intersection observer
  setupLazyLoading: () => {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || '';
            img.classList.remove('lazy');
            observer.unobserve(img);
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach((img) => {
        imageObserver.observe(img);
      });
    }
  },
};

// Bundle optimization
export const bundleOptimization = {
  // Dynamic imports for heavy components
  loadComponent: async (componentPath: string) => {
    try {
      const component = await import(componentPath);
      return component.default || component;
    } catch (error) {
      console.error(`Failed to load component: ${componentPath}`, error);
      return null;
    }
  },

  // Preload critical chunks
  preloadCriticalChunks: () => {
    if (typeof window !== 'undefined') {
      const criticalChunks = [
        '/_next/static/chunks/pages/_app.js',
        '/_next/static/chunks/main.js',
        '/_next/static/chunks/webpack.js',
      ];

      criticalChunks.forEach((chunk) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'script';
        link.href = chunk;
        document.head.appendChild(link);
      });
    }
  },
};

// Memory optimization
export const memoryOptimization = {
  // Cleanup unused resources
  cleanup: () => {
    if (typeof window !== 'undefined') {
      // Clear unused timers
      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }

      // Clear unused intervals
      const highestIntervalId = setInterval(() => {}, 0);
      clearInterval(highestIntervalId);
      for (let i = 0; i < highestIntervalId; i++) {
        clearInterval(i);
      }
    }
  },

  // Optimize React Query cache
  optimizeQueryCache: (queryClient: any) => {
    // Set aggressive garbage collection
    queryClient.setDefaultOptions({
      queries: {
        cacheTime: 5 * 60 * 1000, // 5 minutes
        staleTime: 2 * 60 * 1000, // 2 minutes
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
      },
    });

    // Clear old cache entries
    queryClient.getQueryCache().clear();
  },
};

// Network optimization
export const networkOptimization = {
  // Prefetch critical resources
  prefetchCriticalResources: () => {
    if (typeof window !== 'undefined') {
      const criticalResources = [
        '/api/products/featured',
        '/api/categories',
        '/api/user/profile',
      ];

      criticalResources.forEach((url) => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
      });
    }
  },

  // Setup service worker for caching
  setupServiceWorker: async () => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered:', registration);
        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        return null;
      }
    }
  },

  // Optimize API requests
  optimizeApiRequests: () => {
    // Implement request deduplication
    const requestCache = new Map();
    
    return {
      dedupedFetch: async (url: string, options?: RequestInit) => {
        const key = `${url}-${JSON.stringify(options)}`;
        
        if (requestCache.has(key)) {
          return requestCache.get(key);
        }

        const promise = fetch(url, options);
        requestCache.set(key, promise);

        // Clear cache after 5 seconds
        setTimeout(() => {
          requestCache.delete(key);
        }, 5000);

        return promise;
      },
    };
  },
};

// Database optimization
export const databaseOptimization = {
  // Optimize SQLite queries
  optimizeQueries: () => {
    return {
      // Use prepared statements
      prepareStatement: (sql: string) => {
        // This would be implemented with the actual database connection
        return sql;
      },

      // Batch operations
      batchOperations: (operations: Array<() => Promise<any>>) => {
        return Promise.all(operations.map(op => op()));
      },

      // Connection pooling
      setupConnectionPool: () => {
        // Implementation would depend on the database library
        console.log('Database connection pool setup');
      },
    };
  },
};

// CSS optimization
export const cssOptimization = {
  // Remove unused CSS
  removeUnusedCSS: () => {
    if (typeof window !== 'undefined') {
      const usedSelectors = new Set();
      
      // Collect used selectors
      document.querySelectorAll('*').forEach((element) => {
        element.classList.forEach((className) => {
          usedSelectors.add(`.${className}`);
        });
      });

      // This would be used in build process to remove unused CSS
      return Array.from(usedSelectors);
    }
    return [];
  },

  // Critical CSS extraction
  extractCriticalCSS: () => {
    const criticalSelectors = [
      'body',
      'html',
      '.container',
      '.header',
      '.footer',
      '.btn',
      '.card',
    ];

    return criticalSelectors;
  },
};

// Performance monitoring
export const performanceMonitoring = {
  // Measure Core Web Vitals
  measureCoreWebVitals: () => {
    if (typeof window !== 'undefined') {
      // Largest Contentful Paint
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log('LCP:', lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach((entry) => {
          console.log('FID:', entry.processingStart - entry.startTime);
        });
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      new PerformanceObserver((entryList) => {
        let clsValue = 0;
        const entries = entryList.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        console.log('CLS:', clsValue);
      }).observe({ entryTypes: ['layout-shift'] });
    }
  },

  // Performance budget monitoring
  checkPerformanceBudget: () => {
    if (typeof window !== 'undefined') {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      const metrics = {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstByte: navigation.responseStart - navigation.requestStart,
      };

      // Check against budget (example thresholds)
      const budget = {
        domContentLoaded: 1500, // 1.5s
        loadComplete: 3000, // 3s
        firstByte: 500, // 500ms
      };

      Object.entries(metrics).forEach(([metric, value]) => {
        if (value > budget[metric as keyof typeof budget]) {
          console.warn(`Performance budget exceeded for ${metric}: ${value}ms`);
        }
      });

      return metrics;
    }
    return null;
  },
};

// Initialize all optimizations
export const initializePerformanceOptimizations = () => {
  if (typeof window !== 'undefined') {
    // Run on page load
    window.addEventListener('load', () => {
      imageOptimization.preloadCriticalImages();
      imageOptimization.setupLazyLoading();
      bundleOptimization.preloadCriticalChunks();
      networkOptimization.prefetchCriticalResources();
      networkOptimization.setupServiceWorker();
      performanceMonitoring.measureCoreWebVitals();
    });

    // Run on page unload
    window.addEventListener('beforeunload', () => {
      memoryOptimization.cleanup();
    });

    // Run periodically
    setInterval(() => {
      performanceMonitoring.checkPerformanceBudget();
    }, 30000); // Every 30 seconds
  }
};

export default {
  imageOptimization,
  bundleOptimization,
  memoryOptimization,
  networkOptimization,
  databaseOptimization,
  cssOptimization,
  performanceMonitoring,
  initializePerformanceOptimizations,
};
