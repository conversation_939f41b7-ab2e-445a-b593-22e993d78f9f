/**
 * Environment Configuration Management
 * Centralized configuration with validation and type safety
 */

import { z } from 'zod';

// Environment schema validation
const envSchema = z.object({
  // App Configuration
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  NEXT_PUBLIC_APP_NAME: z.string().default('EcommercePro'),
  NEXT_PUBLIC_APP_VERSION: z.string().default('1.0.0'),

  // Database Configuration
  SQLITE_DB_PATH: z.string().optional(),
  DATABASE_URL: z.string().optional(),
  
  // Authentication & Security
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  NEXTAUTH_SECRET: z.string().optional(),
  NEXTAUTH_URL: z.string().url().optional(),
  ENCRYPTION_KEY: z.string().min(32, 'Encryption key must be at least 32 characters'),
  
  // Payment Gateways
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  PAYPAL_CLIENT_ID: z.string().optional(),
  PAYPAL_CLIENT_SECRET: z.string().optional(),
  PAYPAL_WEBHOOK_ID: z.string().optional(),
  
  // Email Configuration
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.string().default('587'),
  SMTP_SECURE: z.string().default('false'),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  EMAIL_FROM_NAME: z.string().default('EcommercePro'),
  EMAIL_FROM_ADDRESS: z.string().email().optional(),
  
  // File Storage
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_FILE_SIZE: z.string().default('10485760'), // 10MB
  ALLOWED_FILE_TYPES: z.string().default('image/jpeg,image/png,image/webp,image/gif'),
  
  // External Services
  GOOGLE_ANALYTICS_ID: z.string().optional(),
  FACEBOOK_PIXEL_ID: z.string().optional(),
  SENTRY_DSN: z.string().optional(),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW: z.string().default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().default('100'),
  
  // Cache Configuration
  REDIS_URL: z.string().optional(),
  CACHE_TTL: z.string().default('3600'), // 1 hour
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE_PATH: z.string().optional(),
  
  // Feature Flags
  ENABLE_REGISTRATION: z.string().default('true'),
  ENABLE_GUEST_CHECKOUT: z.string().default('true'),
  ENABLE_REVIEWS: z.string().default('true'),
  ENABLE_WISHLIST: z.string().default('true'),
  ENABLE_ANALYTICS: z.string().default('true'),
  
  // Business Configuration
  DEFAULT_CURRENCY: z.string().default('SAR'),
  DEFAULT_LANGUAGE: z.string().default('ar'),
  TAX_RATE: z.string().default('0.15'), // 15%
  FREE_SHIPPING_THRESHOLD: z.string().default('200'),
  
  // API Configuration
  API_RATE_LIMIT: z.string().default('1000'),
  API_TIMEOUT: z.string().default('30000'), // 30 seconds
});

// Parse and validate environment variables
function parseEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment configuration:');
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  ${err.path.join('.')}: ${err.message}`);
      });
    }
    process.exit(1);
  }
}

// Validated environment variables
const env = parseEnv();

// Configuration object with typed values
export const config = {
  // App Configuration
  app: {
    name: env.NEXT_PUBLIC_APP_NAME,
    version: env.NEXT_PUBLIC_APP_VERSION,
    url: env.NEXT_PUBLIC_APP_URL,
    env: env.NODE_ENV,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isStaging: env.NODE_ENV === 'staging',
  },

  // Database Configuration
  database: {
    sqlitePath: env.SQLITE_DB_PATH || './database.sqlite',
    url: env.DATABASE_URL,
  },

  // Security Configuration
  security: {
    jwtSecret: env.JWT_SECRET,
    encryptionKey: env.ENCRYPTION_KEY,
    nextAuthSecret: env.NEXTAUTH_SECRET,
    nextAuthUrl: env.NEXTAUTH_URL,
  },

  // Payment Configuration
  payments: {
    stripe: {
      secretKey: env.STRIPE_SECRET_KEY,
      publishableKey: env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: env.STRIPE_WEBHOOK_SECRET,
    },
    paypal: {
      clientId: env.PAYPAL_CLIENT_ID,
      clientSecret: env.PAYPAL_CLIENT_SECRET,
      webhookId: env.PAYPAL_WEBHOOK_ID,
    },
  },

  // Email Configuration
  email: {
    smtp: {
      host: env.SMTP_HOST,
      port: parseInt(env.SMTP_PORT),
      secure: env.SMTP_SECURE === 'true',
      user: env.SMTP_USER,
      pass: env.SMTP_PASS,
    },
    from: {
      name: env.EMAIL_FROM_NAME,
      address: env.EMAIL_FROM_ADDRESS,
    },
  },

  // File Upload Configuration
  upload: {
    dir: env.UPLOAD_DIR,
    maxFileSize: parseInt(env.MAX_FILE_SIZE),
    allowedTypes: env.ALLOWED_FILE_TYPES.split(','),
  },

  // External Services
  services: {
    googleAnalytics: env.GOOGLE_ANALYTICS_ID,
    facebookPixel: env.FACEBOOK_PIXEL_ID,
    sentry: env.SENTRY_DSN,
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(env.RATE_LIMIT_WINDOW),
    maxRequests: parseInt(env.RATE_LIMIT_MAX_REQUESTS),
  },

  // Cache Configuration
  cache: {
    redisUrl: env.REDIS_URL,
    ttl: parseInt(env.CACHE_TTL),
  },

  // Logging Configuration
  logging: {
    level: env.LOG_LEVEL,
    filePath: env.LOG_FILE_PATH,
  },

  // Feature Flags
  features: {
    registration: env.ENABLE_REGISTRATION === 'true',
    guestCheckout: env.ENABLE_GUEST_CHECKOUT === 'true',
    reviews: env.ENABLE_REVIEWS === 'true',
    wishlist: env.ENABLE_WISHLIST === 'true',
    analytics: env.ENABLE_ANALYTICS === 'true',
  },

  // Business Configuration
  business: {
    currency: env.DEFAULT_CURRENCY,
    language: env.DEFAULT_LANGUAGE,
    taxRate: parseFloat(env.TAX_RATE),
    freeShippingThreshold: parseFloat(env.FREE_SHIPPING_THRESHOLD),
  },

  // API Configuration
  api: {
    rateLimit: parseInt(env.API_RATE_LIMIT),
    timeout: parseInt(env.API_TIMEOUT),
  },
} as const;

// Configuration validation helpers
export function validateConfig(): boolean {
  const errors: string[] = [];

  // Check required configurations for production
  if (config.app.isProduction) {
    if (!config.security.jwtSecret || config.security.jwtSecret.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters in production');
    }

    if (!config.email.smtp.user || !config.email.smtp.pass) {
      errors.push('SMTP credentials are required in production');
    }

    if (!config.email.from.address) {
      errors.push('EMAIL_FROM_ADDRESS is required in production');
    }

    if (config.app.url.includes('localhost')) {
      errors.push('NEXT_PUBLIC_APP_URL should not be localhost in production');
    }
  }

  // Check payment gateway configuration
  const hasStripe = config.payments.stripe.secretKey && config.payments.stripe.publishableKey;
  const hasPayPal = config.payments.paypal.clientId && config.payments.paypal.clientSecret;

  if (!hasStripe && !hasPayPal) {
    console.warn('⚠️  No payment gateways configured. Only cash on delivery will be available.');
  }

  if (errors.length > 0) {
    console.error('❌ Configuration validation failed:');
    errors.forEach(error => console.error(`  ${error}`));
    return false;
  }

  return true;
}

// Environment-specific configurations
export const getEnvironmentConfig = () => {
  switch (config.app.env) {
    case 'development':
      return {
        logLevel: 'debug',
        enableDebugMode: true,
        enableHotReload: true,
        skipEmailVerification: true,
      };

    case 'staging':
      return {
        logLevel: 'info',
        enableDebugMode: true,
        enableHotReload: false,
        skipEmailVerification: false,
      };

    case 'production':
      return {
        logLevel: 'warn',
        enableDebugMode: false,
        enableHotReload: false,
        skipEmailVerification: false,
      };

    default:
      return {
        logLevel: 'info',
        enableDebugMode: false,
        enableHotReload: false,
        skipEmailVerification: false,
      };
  }
};

// Export individual configurations for convenience
export const {
  app: appConfig,
  database: databaseConfig,
  security: securityConfig,
  payments: paymentsConfig,
  email: emailConfig,
  upload: uploadConfig,
  services: servicesConfig,
  rateLimit: rateLimitConfig,
  cache: cacheConfig,
  logging: loggingConfig,
  features: featuresConfig,
  business: businessConfig,
  api: apiConfig,
} = config;

// Validate configuration on import
if (typeof window === 'undefined') {
  validateConfig();
}

export default config;
