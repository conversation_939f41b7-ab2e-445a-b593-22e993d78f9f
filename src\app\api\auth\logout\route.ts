import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '../../../../services/AuthService';

// POST /api/auth/logout
export async function POST(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get('auth-token')?.value;

    if (token) {
      // Invalidate the token on the server side
      await AuthService.signOut(token);
    }

    // Clear the auth cookie
    const response = NextResponse.json({
      message: 'Logout successful'
    });

    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0 // Expire immediately
    });

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
